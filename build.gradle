plugins {
    id 'java-library'
    id "com.google.protobuf" version "${protobufGradlePluginVersion}"
}

repositories {
    mavenCentral()
}

dependencies {
    api(platform("io.grpc:grpc-bom:${grpcVersion}"))
    runtimeOnly(platform("io.grpc:grpc-bom:${grpcVersion}"))

    // grpc dependencies
    runtimeOnly("io.grpc:grpc-netty-shaded")
    api("io.grpc:grpc-services")
    // from 1.58.0, grpc-inprocess has been separated from grpc-core
    // see https://github.com/grpc/grpc-java/releases/tag/v1.58.0
    api("io.grpc:grpc-inprocess")
    // 这里使用 javax.annotation 命名空间替换 jakarta.annotation
    // 如果这里使用 jakarta.annotation 会出现相同在类路径下面有两个不同版本，导致低版本被覆盖
    // 无法找到 javax.annotation.Generated，生成的 grpc 代码将不能正常编译
    api "javax.annotation:javax.annotation-api:1.3.2" // necessary for Java 9+

    api "build.buf:protovalidate:${protobufValidateVersion}"
}

tasks.register("copyProtos", Copy) {
    from("${rootDir}/backend") {
        include "**/*.proto"
        into "backend"
    }
    into "${rootDir}/_java_"
    includeEmptyDirs = false
}

extractIncludeProto.dependsOn copyProtos
processResources.dependsOn copyProtos

sourceSets {
    main {
        proto {
            srcDir '_java_'
        }
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVersion}"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:${grpcVersion}"
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
        }
    }
}

