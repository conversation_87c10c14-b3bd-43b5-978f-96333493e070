package nacos

import (
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/plugin"
)

const (
	pluginName = "nacos"
	pluginType = "config"
)

func init() {
	plugin.Register(pluginName, &nacosConfig{})
}

type pluginConfig struct {
	Providers []*Config `yaml:"providers"`
}

type nacosConfig struct{}

// Type return nacos config plugin type
func (n *nacosConfig) Type() string {
	return pluginType
}

// Setup register nacos config plugin
func (n *nacosConfig) Setup(name string, decoder plugin.Decoder) error {
	cfg := &pluginConfig{}
	if err := decoder.Decode(cfg); err != nil {
		return err
	}
	for _, c := range cfg.Providers {
		if err := c.CheckAndApplyDefaultConfig(); err != nil {
			return nil
		}
		client, err := New(c)
		if err != nil {
			log.Errorf("nacos config plugin init error: %v", err)
			continue
		}
		config.SetGlobalKV(client)
		config.Register(client)
	}
	return nil
}
