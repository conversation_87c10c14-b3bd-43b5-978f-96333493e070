load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "platformsalespb_proto",
    srcs = ["platform_sales_service.proto"],
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto",
        "@googleapis//google/type:decimal_proto",
        "@googleapis//google/type:money_proto",
    ],
)

go_proto_library(
    name = "platformsalespb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/openapi/admin/platform_sales/v1",
    proto = ":platformsalespb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library",
        "@org_golang_google_genproto//googleapis/type/decimal",
        "@org_golang_google_genproto//googleapis/type/money",
    ],
)

go_library(
    name = "platform_sales",
    embed = [":platformsalespb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/openapi/admin/platform_sales/v1",
    visibility = ["//visibility:public"],
)
