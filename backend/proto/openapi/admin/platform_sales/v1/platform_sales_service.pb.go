// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: platform sales link 偏业务定制化，暂不设计成 resource --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: platform sales link 偏业务定制化，暂不设计成 resource --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/openapi/admin/platform_sales/v1/platform_sales_service.proto

package platformsalespb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CreateSalesLinkRequest
type CreateSalesLinkRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// creator
	Creator string `protobuf:"bytes,1,opt,name=creator,proto3" json:"creator,omitempty"`
	// email
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// subscription plan
	SubscriptionPlan string `protobuf:"bytes,3,opt,name=subscription_plan,json=subscriptionPlan,proto3" json:"subscription_plan,omitempty"`
	// enable custom rate
	EnableCustomRate bool `protobuf:"varint,4,opt,name=enable_custom_rate,json=enableCustomRate,proto3" json:"enable_custom_rate,omitempty"`
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: `in` has meaning so can not remove --)
	//
	// in-person rate
	InPersonRate *decimal.Decimal `protobuf:"bytes,5,opt,name=in_person_rate,json=inPersonRate,proto3" json:"in_person_rate,omitempty"`
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: `in` has meaning so can not remove --)
	//
	// in-person fee
	InPersonFee *money.Money `protobuf:"bytes,6,opt,name=in_person_fee,json=inPersonFee,proto3" json:"in_person_fee,omitempty"`
	// online rate
	OnlineRate *decimal.Decimal `protobuf:"bytes,7,opt,name=online_rate,json=onlineRate,proto3" json:"online_rate,omitempty"`
	// online fee
	OnlineFee *money.Money `protobuf:"bytes,8,opt,name=online_fee,json=onlineFee,proto3" json:"online_fee,omitempty"`
	// min monthly transaction volume
	MinMonthlyTransactionVolume *money.Money `protobuf:"bytes,9,opt,name=min_monthly_transaction_volume,json=minMonthlyTransactionVolume,proto3" json:"min_monthly_transaction_volume,omitempty"`
	// van count
	VanCount int32 `protobuf:"varint,10,opt,name=van_count,json=vanCount,proto3" json:"van_count,omitempty"`
	// grooming location count
	GroomingLocationCount int32 `protobuf:"varint,11,opt,name=grooming_location_count,json=groomingLocationCount,proto3" json:"grooming_location_count,omitempty"`
	// bd location count
	BdLocationCount int32 `protobuf:"varint,12,opt,name=bd_location_count,json=bdLocationCount,proto3" json:"bd_location_count,omitempty"`
	// is annual plan
	IsAnnualPlan bool `protobuf:"varint,13,opt,name=is_annual_plan,json=isAnnualPlan,proto3" json:"is_annual_plan,omitempty"`
	// contract term months
	ContractTermMonths int32 `protobuf:"varint,14,opt,name=contract_term_months,json=contractTermMonths,proto3" json:"contract_term_months,omitempty"`
	// annual plan discount
	AnnualPlanDiscount *decimal.Decimal `protobuf:"bytes,15,opt,name=annual_plan_discount,json=annualPlanDiscount,proto3" json:"annual_plan_discount,omitempty"`
	// include hardware
	IncludeHardware bool `protobuf:"varint,16,opt,name=include_hardware,json=includeHardware,proto3" json:"include_hardware,omitempty"`
	// hardware discount
	HardwareDiscount *decimal.Decimal `protobuf:"bytes,17,opt,name=hardware_discount,json=hardwareDiscount,proto3" json:"hardware_discount,omitempty"`
	// include accounting
	IncludeAccounting bool `protobuf:"varint,18,opt,name=include_accounting,json=includeAccounting,proto3" json:"include_accounting,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateSalesLinkRequest) Reset() {
	*x = CreateSalesLinkRequest{}
	mi := &file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSalesLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSalesLinkRequest) ProtoMessage() {}

func (x *CreateSalesLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSalesLinkRequest.ProtoReflect.Descriptor instead.
func (*CreateSalesLinkRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSalesLinkRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CreateSalesLinkRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateSalesLinkRequest) GetSubscriptionPlan() string {
	if x != nil {
		return x.SubscriptionPlan
	}
	return ""
}

func (x *CreateSalesLinkRequest) GetEnableCustomRate() bool {
	if x != nil {
		return x.EnableCustomRate
	}
	return false
}

func (x *CreateSalesLinkRequest) GetInPersonRate() *decimal.Decimal {
	if x != nil {
		return x.InPersonRate
	}
	return nil
}

func (x *CreateSalesLinkRequest) GetInPersonFee() *money.Money {
	if x != nil {
		return x.InPersonFee
	}
	return nil
}

func (x *CreateSalesLinkRequest) GetOnlineRate() *decimal.Decimal {
	if x != nil {
		return x.OnlineRate
	}
	return nil
}

func (x *CreateSalesLinkRequest) GetOnlineFee() *money.Money {
	if x != nil {
		return x.OnlineFee
	}
	return nil
}

func (x *CreateSalesLinkRequest) GetMinMonthlyTransactionVolume() *money.Money {
	if x != nil {
		return x.MinMonthlyTransactionVolume
	}
	return nil
}

func (x *CreateSalesLinkRequest) GetVanCount() int32 {
	if x != nil {
		return x.VanCount
	}
	return 0
}

func (x *CreateSalesLinkRequest) GetGroomingLocationCount() int32 {
	if x != nil {
		return x.GroomingLocationCount
	}
	return 0
}

func (x *CreateSalesLinkRequest) GetBdLocationCount() int32 {
	if x != nil {
		return x.BdLocationCount
	}
	return 0
}

func (x *CreateSalesLinkRequest) GetIsAnnualPlan() bool {
	if x != nil {
		return x.IsAnnualPlan
	}
	return false
}

func (x *CreateSalesLinkRequest) GetContractTermMonths() int32 {
	if x != nil {
		return x.ContractTermMonths
	}
	return 0
}

func (x *CreateSalesLinkRequest) GetAnnualPlanDiscount() *decimal.Decimal {
	if x != nil {
		return x.AnnualPlanDiscount
	}
	return nil
}

func (x *CreateSalesLinkRequest) GetIncludeHardware() bool {
	if x != nil {
		return x.IncludeHardware
	}
	return false
}

func (x *CreateSalesLinkRequest) GetHardwareDiscount() *decimal.Decimal {
	if x != nil {
		return x.HardwareDiscount
	}
	return nil
}

func (x *CreateSalesLinkRequest) GetIncludeAccounting() bool {
	if x != nil {
		return x.IncludeAccounting
	}
	return false
}

// CreateSalesLinkResponse
type CreateSalesLinkResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// sales link
	SalesLink     string `protobuf:"bytes,1,opt,name=sales_link,json=salesLink,proto3" json:"sales_link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSalesLinkResponse) Reset() {
	*x = CreateSalesLinkResponse{}
	mi := &file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSalesLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSalesLinkResponse) ProtoMessage() {}

func (x *CreateSalesLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSalesLinkResponse.ProtoReflect.Descriptor instead.
func (*CreateSalesLinkResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateSalesLinkResponse) GetSalesLink() string {
	if x != nil {
		return x.SalesLink
	}
	return ""
}

var File_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto protoreflect.FileDescriptor

const file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDesc = "" +
	"\n" +
	"Jbackend/proto/openapi/admin/platform_sales/v1/platform_sales_service.proto\x12-backend.proto.openapi.admin.platform_sales.v1\x1a\x19google/type/decimal.proto\x1a\x17google/type/money.proto\x1a\x1bbuf/validate/validate.proto\"\xc3\a\n" +
	"\x16CreateSalesLinkRequest\x12!\n" +
	"\acreator\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\acreator\x12\x1d\n" +
	"\x05email\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x05email\x12D\n" +
	"\x11subscription_plan\x18\x03 \x01(\tB\x17\xbaH\x14r\x12R\x06growthR\bultimateR\x10subscriptionPlan\x12,\n" +
	"\x12enable_custom_rate\x18\x04 \x01(\bR\x10enableCustomRate\x12:\n" +
	"\x0ein_person_rate\x18\x05 \x01(\v2\x14.google.type.DecimalR\finPersonRate\x126\n" +
	"\rin_person_fee\x18\x06 \x01(\v2\x12.google.type.MoneyR\vinPersonFee\x125\n" +
	"\vonline_rate\x18\a \x01(\v2\x14.google.type.DecimalR\n" +
	"onlineRate\x121\n" +
	"\n" +
	"online_fee\x18\b \x01(\v2\x12.google.type.MoneyR\tonlineFee\x12W\n" +
	"\x1emin_monthly_transaction_volume\x18\t \x01(\v2\x12.google.type.MoneyR\x1bminMonthlyTransactionVolume\x12\x1b\n" +
	"\tvan_count\x18\n" +
	" \x01(\x05R\bvanCount\x126\n" +
	"\x17grooming_location_count\x18\v \x01(\x05R\x15groomingLocationCount\x12*\n" +
	"\x11bd_location_count\x18\f \x01(\x05R\x0fbdLocationCount\x12$\n" +
	"\x0eis_annual_plan\x18\r \x01(\bR\fisAnnualPlan\x120\n" +
	"\x14contract_term_months\x18\x0e \x01(\x05R\x12contractTermMonths\x12F\n" +
	"\x14annual_plan_discount\x18\x0f \x01(\v2\x14.google.type.DecimalR\x12annualPlanDiscount\x12)\n" +
	"\x10include_hardware\x18\x10 \x01(\bR\x0fincludeHardware\x12A\n" +
	"\x11hardware_discount\x18\x11 \x01(\v2\x14.google.type.DecimalR\x10hardwareDiscount\x12-\n" +
	"\x12include_accounting\x18\x12 \x01(\bR\x11includeAccounting\"8\n" +
	"\x17CreateSalesLinkResponse\x12\x1d\n" +
	"\n" +
	"sales_link\x18\x01 \x01(\tR\tsalesLink2\xb9\x01\n" +
	"\x14PlatformSalesService\x12\xa0\x01\n" +
	"\x0fCreateSalesLink\x12E.backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest\x1aF.backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkResponseB\x98\x01\n" +
	"7com.moego.backend.proto.openapi.admin.platform_sales.v1P\x01Z[github.com/MoeGolibrary/moego/backend/proto/openapi/admin/platform_sales/v1;platformsalespbb\x06proto3"

var (
	file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDescOnce sync.Once
	file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDescData []byte
)

func file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDescGZIP() []byte {
	file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDesc), len(file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDesc)))
	})
	return file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDescData
}

var file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_goTypes = []any{
	(*CreateSalesLinkRequest)(nil),  // 0: backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest
	(*CreateSalesLinkResponse)(nil), // 1: backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkResponse
	(*decimal.Decimal)(nil),         // 2: google.type.Decimal
	(*money.Money)(nil),             // 3: google.type.Money
}
var file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_depIdxs = []int32{
	2, // 0: backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest.in_person_rate:type_name -> google.type.Decimal
	3, // 1: backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest.in_person_fee:type_name -> google.type.Money
	2, // 2: backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest.online_rate:type_name -> google.type.Decimal
	3, // 3: backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest.online_fee:type_name -> google.type.Money
	3, // 4: backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest.min_monthly_transaction_volume:type_name -> google.type.Money
	2, // 5: backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest.annual_plan_discount:type_name -> google.type.Decimal
	2, // 6: backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest.hardware_discount:type_name -> google.type.Decimal
	0, // 7: backend.proto.openapi.admin.platform_sales.v1.PlatformSalesService.CreateSalesLink:input_type -> backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkRequest
	1, // 8: backend.proto.openapi.admin.platform_sales.v1.PlatformSalesService.CreateSalesLink:output_type -> backend.proto.openapi.admin.platform_sales.v1.CreateSalesLinkResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_init() }
func file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_init() {
	if File_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDesc), len(file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_msgTypes,
	}.Build()
	File_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto = out.File
	file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_goTypes = nil
	file_backend_proto_openapi_admin_platform_sales_v1_platform_sales_service_proto_depIdxs = nil
}
