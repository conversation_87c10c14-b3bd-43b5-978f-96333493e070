// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: platform sales link 偏业务定制化，暂不设计成 resource --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: platform sales link 偏业务定制化，暂不设计成 resource --)

syntax = "proto3";

package backend.proto.openapi.admin.platform_sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/openapi/admin/platform_sales/v1;platformsalespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.openapi.admin.platform_sales.v1";

import "google/type/decimal.proto";
import "google/type/money.proto";
import "buf/validate/validate.proto";

// PlatformSalesService
service PlatformSalesService {
  // Create Sales Link
  rpc CreateSalesLink(CreateSalesLinkRequest) returns (CreateSalesLinkResponse);
}

// CreateSalesLinkRequest
message CreateSalesLinkRequest {
  // creator
  string creator = 1 [(buf.validate.field) = { string: { min_len: 1 } }];

  // email
  string email = 2 [(buf.validate.field) = { string: { min_len: 1 } }];

  // subscription plan
  string subscription_plan = 3 [(buf.validate.field) = { string: { in: ["growth", "ultimate"] } }];

  // enable custom rate
  bool enable_custom_rate = 4;
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: `in` has meaning so can not remove --)
  // in-person rate
  google.type.Decimal in_person_rate = 5;
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: `in` has meaning so can not remove --)
  // in-person fee
  google.type.Money in_person_fee = 6;
  // online rate
  google.type.Decimal online_rate = 7;
  // online fee
  google.type.Money online_fee = 8;
  // min monthly transaction volume
  google.type.Money min_monthly_transaction_volume = 9;

  // van count
  int32 van_count = 10;
  // grooming location count
  int32 grooming_location_count = 11;
  // bd location count
  int32 bd_location_count = 12;

  // is annual plan
  bool is_annual_plan = 13;
  // contract term months
  int32 contract_term_months = 14;
  // annual plan discount
  google.type.Decimal annual_plan_discount = 15;

  // include hardware
  bool include_hardware = 16;
  // hardware discount
  google.type.Decimal hardware_discount = 17;

  // include accounting
  bool include_accounting = 18;
}

// CreateSalesLinkResponse
message CreateSalesLinkResponse {
  // sales link
  string sales_link = 1;
}
