// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/instance.proto

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 服务实例
type ServiceInstance struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务实例ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 预约ID
	AppointmentId int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// 宠物ID
	PetId int64 `protobuf:"varint,6,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 护理类型
	CareType CareType `protobuf:"varint,7,opt,name=care_type,json=careType,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_type,omitempty"`
	// 日期类型
	DateType DateType `protobuf:"varint,8,opt,name=date_type,json=dateType,proto3,enum=backend.proto.fulfillment.v1.DateType" json:"date_type,omitempty"`
	// 服务工厂ID
	ServiceFactoryId int64 `protobuf:"varint,9,opt,name=service_factory_id,json=serviceFactoryId,proto3" json:"service_factory_id,omitempty"`
	// 父服务实例ID
	ParentId int64 `protobuf:"varint,10,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 根服务实例ID
	RootId int64 `protobuf:"varint,11,opt,name=root_id,json=rootId,proto3" json:"root_id,omitempty"`
	// 开始时间
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	aip.dev/not-precedent: start_date is clear and appropriate --)
	StartDate *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// 结束时间
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	aip.dev/not-precedent: end_date is clear and appropriate --)
	EndDate *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// 创建时间
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	aip.dev/not-precedent: created_at is clear and appropriate --)
	//
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: created_at is clear and appropriate --)
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 更新时间
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	aip.dev/not-precedent: updated_at is clear and appropriate --)
	//
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: updated_at is clear and appropriate --)
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceInstance) Reset() {
	*x = ServiceInstance{}
	mi := &file_backend_proto_fulfillment_v1_instance_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInstance) ProtoMessage() {}

func (x *ServiceInstance) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_instance_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInstance.ProtoReflect.Descriptor instead.
func (*ServiceInstance) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_instance_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceInstance) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceInstance) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ServiceInstance) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ServiceInstance) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ServiceInstance) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ServiceInstance) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ServiceInstance) GetCareType() CareType {
	if x != nil {
		return x.CareType
	}
	return CareType_CARE_TYPE_UNSPECIFIED
}

func (x *ServiceInstance) GetDateType() DateType {
	if x != nil {
		return x.DateType
	}
	return DateType_DATE_TYPE_UNSPECIFIED
}

func (x *ServiceInstance) GetServiceFactoryId() int64 {
	if x != nil {
		return x.ServiceFactoryId
	}
	return 0
}

func (x *ServiceInstance) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *ServiceInstance) GetRootId() int64 {
	if x != nil {
		return x.RootId
	}
	return 0
}

func (x *ServiceInstance) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ServiceInstance) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ServiceInstance) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ServiceInstance) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// 服务实例过滤条件
type ServiceInstanceFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID列表
	PetIds []int64 `protobuf:"varint,1,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 护理类型列表
	CareTypes []CareType `protobuf:"varint,3,rep,packed,name=care_types,json=careTypes,proto3,enum=backend.proto.fulfillment.v1.CareType" json:"care_types,omitempty"`
	// 日期类型列表
	DateTypes []DateType `protobuf:"varint,4,rep,packed,name=date_types,json=dateTypes,proto3,enum=backend.proto.fulfillment.v1.DateType" json:"date_types,omitempty"`
	// 根服务实例ID列表
	RootIds       []int64 `protobuf:"varint,5,rep,packed,name=root_ids,json=rootIds,proto3" json:"root_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceInstanceFilter) Reset() {
	*x = ServiceInstanceFilter{}
	mi := &file_backend_proto_fulfillment_v1_instance_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInstanceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInstanceFilter) ProtoMessage() {}

func (x *ServiceInstanceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_instance_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInstanceFilter.ProtoReflect.Descriptor instead.
func (*ServiceInstanceFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_instance_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceInstanceFilter) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ServiceInstanceFilter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ServiceInstanceFilter) GetCareTypes() []CareType {
	if x != nil {
		return x.CareTypes
	}
	return nil
}

func (x *ServiceInstanceFilter) GetDateTypes() []DateType {
	if x != nil {
		return x.DateTypes
	}
	return nil
}

func (x *ServiceInstanceFilter) GetRootIds() []int64 {
	if x != nil {
		return x.RootIds
	}
	return nil
}

var File_backend_proto_fulfillment_v1_instance_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_instance_proto_rawDesc = "" +
	"\n" +
	"+backend/proto/fulfillment/v1/instance.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a)backend/proto/fulfillment/v1/common.proto\"\x96\x05\n" +
	"\x0fServiceInstance\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x1f\n" +
	"\vcustomer_id\x18\x03 \x01(\x03R\n" +
	"customerId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x04 \x01(\x03R\tcompanyId\x12%\n" +
	"\x0eappointment_id\x18\x05 \x01(\x03R\rappointmentId\x12\x15\n" +
	"\x06pet_id\x18\x06 \x01(\x03R\x05petId\x12C\n" +
	"\tcare_type\x18\a \x01(\x0e2&.backend.proto.fulfillment.v1.CareTypeR\bcareType\x12C\n" +
	"\tdate_type\x18\b \x01(\x0e2&.backend.proto.fulfillment.v1.DateTypeR\bdateType\x12,\n" +
	"\x12service_factory_id\x18\t \x01(\x03R\x10serviceFactoryId\x12\x1b\n" +
	"\tparent_id\x18\n" +
	" \x01(\x03R\bparentId\x12\x17\n" +
	"\aroot_id\x18\v \x01(\x03R\x06rootId\x129\n" +
	"\n" +
	"start_date\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x129\n" +
	"\n" +
	"created_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xfc\x01\n" +
	"\x15ServiceInstanceFilter\x12\x17\n" +
	"\apet_ids\x18\x01 \x03(\x03R\x06petIds\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\x12E\n" +
	"\n" +
	"care_types\x18\x03 \x03(\x0e2&.backend.proto.fulfillment.v1.CareTypeR\tcareTypes\x12E\n" +
	"\n" +
	"date_types\x18\x04 \x03(\x0e2&.backend.proto.fulfillment.v1.DateTypeR\tdateTypes\x12\x19\n" +
	"\broot_ids\x18\x05 \x03(\x03R\arootIdsBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_instance_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_instance_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_instance_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_instance_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_instance_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_instance_proto_rawDesc), len(file_backend_proto_fulfillment_v1_instance_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_instance_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_instance_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_fulfillment_v1_instance_proto_goTypes = []any{
	(*ServiceInstance)(nil),       // 0: backend.proto.fulfillment.v1.ServiceInstance
	(*ServiceInstanceFilter)(nil), // 1: backend.proto.fulfillment.v1.ServiceInstanceFilter
	(CareType)(0),                 // 2: backend.proto.fulfillment.v1.CareType
	(DateType)(0),                 // 3: backend.proto.fulfillment.v1.DateType
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_backend_proto_fulfillment_v1_instance_proto_depIdxs = []int32{
	2, // 0: backend.proto.fulfillment.v1.ServiceInstance.care_type:type_name -> backend.proto.fulfillment.v1.CareType
	3, // 1: backend.proto.fulfillment.v1.ServiceInstance.date_type:type_name -> backend.proto.fulfillment.v1.DateType
	4, // 2: backend.proto.fulfillment.v1.ServiceInstance.start_date:type_name -> google.protobuf.Timestamp
	4, // 3: backend.proto.fulfillment.v1.ServiceInstance.end_date:type_name -> google.protobuf.Timestamp
	4, // 4: backend.proto.fulfillment.v1.ServiceInstance.created_at:type_name -> google.protobuf.Timestamp
	4, // 5: backend.proto.fulfillment.v1.ServiceInstance.updated_at:type_name -> google.protobuf.Timestamp
	2, // 6: backend.proto.fulfillment.v1.ServiceInstanceFilter.care_types:type_name -> backend.proto.fulfillment.v1.CareType
	3, // 7: backend.proto.fulfillment.v1.ServiceInstanceFilter.date_types:type_name -> backend.proto.fulfillment.v1.DateType
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_instance_proto_init() }
func file_backend_proto_fulfillment_v1_instance_proto_init() {
	if File_backend_proto_fulfillment_v1_instance_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_instance_proto_rawDesc), len(file_backend_proto_fulfillment_v1_instance_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_instance_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_instance_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_instance_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_instance_proto = out.File
	file_backend_proto_fulfillment_v1_instance_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_instance_proto_depIdxs = nil
}
