// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/fulfillment/v1/instance_service.proto

package fulfillmentpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	InstanceService_ListServiceInstance_FullMethodName     = "/backend.proto.fulfillment.v1.InstanceService/ListServiceInstance"
	InstanceService_GetServiceInstanceByIDs_FullMethodName = "/backend.proto.fulfillment.v1.InstanceService/GetServiceInstanceByIDs"
)

// InstanceServiceClient is the client API for InstanceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 服务实例服务
type InstanceServiceClient interface {
	// 列出服务实例
	ListServiceInstance(ctx context.Context, in *ListServiceInstanceRequest, opts ...grpc.CallOption) (*ListServiceInstanceResponse, error)
	// 获取服务实例
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetServiceInstanceByIDs is clear and appropriate --)
	//
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceInstanceByIDsResponse is appropriate for this use case --)
	GetServiceInstanceByIDs(ctx context.Context, in *GetServiceInstanceByIDsRequest, opts ...grpc.CallOption) (*GetServiceInstanceByIDsResponse, error)
}

type instanceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewInstanceServiceClient(cc grpc.ClientConnInterface) InstanceServiceClient {
	return &instanceServiceClient{cc}
}

func (c *instanceServiceClient) ListServiceInstance(ctx context.Context, in *ListServiceInstanceRequest, opts ...grpc.CallOption) (*ListServiceInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListServiceInstanceResponse)
	err := c.cc.Invoke(ctx, InstanceService_ListServiceInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *instanceServiceClient) GetServiceInstanceByIDs(ctx context.Context, in *GetServiceInstanceByIDsRequest, opts ...grpc.CallOption) (*GetServiceInstanceByIDsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceInstanceByIDsResponse)
	err := c.cc.Invoke(ctx, InstanceService_GetServiceInstanceByIDs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InstanceServiceServer is the server API for InstanceService service.
// All implementations must embed UnimplementedInstanceServiceServer
// for forward compatibility.
//
// 服务实例服务
type InstanceServiceServer interface {
	// 列出服务实例
	ListServiceInstance(context.Context, *ListServiceInstanceRequest) (*ListServiceInstanceResponse, error)
	// 获取服务实例
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetServiceInstanceByIDs is clear and appropriate --)
	//
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetServiceInstanceByIDsResponse is appropriate for this use case --)
	GetServiceInstanceByIDs(context.Context, *GetServiceInstanceByIDsRequest) (*GetServiceInstanceByIDsResponse, error)
	mustEmbedUnimplementedInstanceServiceServer()
}

// UnimplementedInstanceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedInstanceServiceServer struct{}

func (UnimplementedInstanceServiceServer) ListServiceInstance(context.Context, *ListServiceInstanceRequest) (*ListServiceInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceInstance not implemented")
}
func (UnimplementedInstanceServiceServer) GetServiceInstanceByIDs(context.Context, *GetServiceInstanceByIDsRequest) (*GetServiceInstanceByIDsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceInstanceByIDs not implemented")
}
func (UnimplementedInstanceServiceServer) mustEmbedUnimplementedInstanceServiceServer() {}
func (UnimplementedInstanceServiceServer) testEmbeddedByValue()                         {}

// UnsafeInstanceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InstanceServiceServer will
// result in compilation errors.
type UnsafeInstanceServiceServer interface {
	mustEmbedUnimplementedInstanceServiceServer()
}

func RegisterInstanceServiceServer(s grpc.ServiceRegistrar, srv InstanceServiceServer) {
	// If the following call pancis, it indicates UnimplementedInstanceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&InstanceService_ServiceDesc, srv)
}

func _InstanceService_ListServiceInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstanceServiceServer).ListServiceInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InstanceService_ListServiceInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstanceServiceServer).ListServiceInstance(ctx, req.(*ListServiceInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InstanceService_GetServiceInstanceByIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceInstanceByIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstanceServiceServer).GetServiceInstanceByIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InstanceService_GetServiceInstanceByIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstanceServiceServer).GetServiceInstanceByIDs(ctx, req.(*GetServiceInstanceByIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// InstanceService_ServiceDesc is the grpc.ServiceDesc for InstanceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var InstanceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.fulfillment.v1.InstanceService",
	HandlerType: (*InstanceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListServiceInstance",
			Handler:    _InstanceService_ListServiceInstance_Handler,
		},
		{
			MethodName: "GetServiceInstanceByIDs",
			Handler:    _InstanceService_GetServiceInstanceByIDs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/fulfillment/v1/instance_service.proto",
}
