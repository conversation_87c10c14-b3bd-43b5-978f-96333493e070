syntax = "proto3";

package backend.proto.fulfillment.v1;
import "google/protobuf/timestamp.proto";
import "backend/proto/fulfillment/v1/fulfillment.proto";
import "backend/proto/fulfillment/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

import "buf/validate/validate.proto";

// FulfillmentService
service FulfillmentService {
  // ListFulfillment 获取履约信息
  rpc ListFulfillment(ListFulfillmentRequest) returns (ListFulfillmentResponse);
}

// ListFulfillmentRequest
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无效的parent定义 --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 不需要page-size --)
// (-- api-linter: core::0158::request-page_token-field=disabled
//     aip.dev/not-precedent: 不需要page_token --)
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//     aip.dev/not-precedent: 不需要next_page_token --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 不需要page-token --)
message ListFulfillmentRequest {
  // 公司ID
  int64 company_id = 1 [(buf.validate.field).int64.gt = 0];
  // 商家ID
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 查询开始时间
  google.protobuf.Timestamp start_time = 3;
  // 查询结束时间
  google.protobuf.Timestamp end_time = 4;
  // 过滤条件
  // (-- api-linter: core::0132::request-field-types=disabled
  //     aip.dev/not-precedent: 打平成string不利用协议理解 --)
  FulfillmentFilter filter = 5;
  // 排序方式
  SortType sort_type = 6;
  // 分页信息
  PaginationRef pagination = 7;
}

// ListFulfillmentResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 不需要next-page-token --)
message ListFulfillmentResponse {
  // 履约列表
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  repeated Fulfillment fulfillments = 1;
  // 分页信息
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  PaginationRef pagination = 2;
  // 是否最后一页
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  bool is_end = 3;
  // 总条数
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  int32 total = 4;
}

// ErrCode 定义错误码枚举
// 
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because 
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 不合法的参数
  ERR_CODE_INVALID = 122000;
}
