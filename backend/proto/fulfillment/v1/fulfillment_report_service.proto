syntax = "proto3";

package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "backend/proto/fulfillment/v1/fulfillment_report.proto";
import "backend/proto/fulfillment/v1/common.proto";
import "backend/proto/pet/v1/pet.proto";
import "buf/validate/validate.proto";


option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// FulfillmentReportService
service FulfillmentReportService {
    // GetFulfillmentReportTemplate 获取履约报告模板
    rpc GetFulfillmentReportTemplate(GetFulfillmentReportTemplateRequest) returns (GetFulfillmentReportTemplateResponse);
    // UpdateFulfillmentReportTemplate 更新履约报告模板
    rpc UpdateFulfillmentReportTemplate(UpdateFulfillmentReportTemplateRequest) returns (UpdateFulfillmentReportTemplateResponse);

    // GetFulfillmentReport 获取履约报告
    rpc GetFulfillmentReport(GetFulfillmentReportRequest) returns (GetFulfillmentReportResponse);
    // UpdateFulfillmentReport 更新履约报告
    rpc UpdateFulfillmentReport(UpdateFulfillmentReportRequest) returns (UpdateFulfillmentReportResponse);
    // ListFulfillmentReport 获取履约报告列表
    // 应该只返回 report list，其他数据（report）通过 BFF 实现
    rpc ListFulfillmentReport(ListFulfillmentReportRequest) returns (ListFulfillmentReportResponse);
    // CountFulfillmentReport 获取履约报告数量
    rpc CountFulfillmentReport(CountFulfillmentReportRequest) returns (CountFulfillmentReportResponse);
    // BatchDeleteFulfillmentReport 批量删除履约报告
    // (-- api-linter: core::0235::plural-method-name=disabled --)
    rpc BatchDeleteFulfillmentReport(BatchDeleteFulfillmentReportRequest) returns (BatchDeleteFulfillmentReportResponse);
    // BatchSendFulfillmentReport 批量发送履约报告
    rpc BatchSendFulfillmentReport(BatchSendFulfillmentReportRequest) returns (BatchSendFulfillmentReportResponse);

    // GetFulfillmentReportSummaryInfo 获取履约报告详细信息
    rpc GetFulfillmentReportSummaryInfo(GetFulfillmentReportSummaryInfoRequest) returns (GetFulfillmentReportSummaryInfoResponse);
    // GetFulfillmentTemplateReport 获取 template 中展示的 preview report 相关内容
    rpc GetFulfillmentTemplateReport(GetFulfillmentTemplateReportRequest) returns (GetFulfillmentTemplateReportResponse);

    // GenerateMessageContent 生成消息内容
    rpc GenerateMessageContent(GenerateMessageContentRequest) returns (GenerateMessageContentResponse);
    // SendFulfillmentReport 发送履约报告
    rpc SendFulfillmentReport(SendFulfillmentReportRequest) returns (SendFulfillmentReportResponse);
    // IncreaseFulfillmentOpenedCount 增加履约报告打开次数
    rpc IncreaseFulfillmentOpenedCount(IncreaseFulfillmentOpenedCountRequest) returns (IncreaseFulfillmentOpenedCountResponse); 
    // ListSendReportRecords 获取履约报告发送记录
    rpc ListSendReportRecords(ListSendReportRecordsRequest) returns (ListSendReportRecordsResponse);
    // 下面这仨可以统一成一个 ListSendReportRecords 接口，通过 BFF 区分不同功能的接口
    // GetFulfillmentReportSendResult 获取一个具体的履约报告发送结果(这个接口可能可以删掉，暂时没有场景会用到）
    rpc GetFulfillmentReportSendResult(GetFulfillmentReportSendResultRequest) returns (GetFulfillmentReportSendResultResponse);
    // GetFulfillmentReportSendHistory 获取履约报告发送历史记录
    rpc GetFulfillmentReportSendHistory(GetFulfillmentReportSendHistoryRequest) returns (GetFulfillmentReportSendHistoryResponse);
    // GetFulfillmentReportRecords 根据预约获取其中的所有报告及发送记录
    rpc GetFulfillmentReportRecords(GetFulfillmentReportRecordsRequest) returns (GetFulfillmentReportRecordsResponse);

    // listFulfillmentThemeConfig 获取所有主题配置
    rpc ListFulfillmentThemeConfig(ListFulfillmentThemeConfigRequest) returns (ListFulfillmentThemeConfigResponse);


    // ------------------------------
    // fulfillment report 双写相关内部接口
    // ------------------------------
    // SyncFulfillmentReportTemplate 同步履约报告模板（双写）
    rpc SyncFulfillmentReportTemplate(SyncFulfillmentReportTemplateRequest) returns (SyncFulfillmentReportTemplateResponse);
    // BatchSyncFulfillmentReportQuestions 批量同步履约报告模板问题（双写）
    rpc BatchSyncFulfillmentReportQuestions(BatchSyncFulfillmentReportQuestionsRequest) returns (BatchSyncFulfillmentReportQuestionsResponse);
    // SyncFulfillmentReport 同步履约报告（双写）
    rpc SyncFulfillmentReport(SyncFulfillmentReportRequest) returns (SyncFulfillmentReportResponse);
    // SyncFulfillmentReportSendRecord 同步履约报告发送记录（双写）
    rpc SyncFulfillmentReportSendRecord(SyncFulfillmentReportSendRecordRequest) returns (SyncFulfillmentReportSendRecordResponse);

    // ------------------------------
    // 批量迁移接口
    // ------------------------------
    // BatchMigrateTemplates 批量迁移模板（数据迁移专用）
    rpc BatchMigrateTemplates(BatchMigrateTemplatesRequest) returns (BatchMigrateTemplatesResponse);
    // BatchMigrateQuestions 批量迁移问题（数据迁移专用）
    rpc BatchMigrateQuestions(BatchMigrateQuestionsRequest) returns (BatchMigrateQuestionsResponse);
    // BatchMigrateReports 批量迁移报告（数据迁移专用）
    rpc BatchMigrateReports(BatchMigrateReportsRequest) returns (BatchMigrateReportsResponse);
    // BatchMigrateRecords 批量迁移发送
    rpc BatchMigrateRecords(BatchMigrateRecordsRequest) returns (BatchMigrateRecordsResponse);

    // ------------------------------
    // 数据查询接口（用于双写验证和读数据源迁移）
    // ------------------------------
    // GetTemplatesByUniqueKeys 通过唯一键批量查询模板
    // (-- api-linter: core::0136::prepositions=disabled --)
    rpc GetTemplatesByUniqueKeys(GetTemplatesByUniqueKeysRequest) returns (GetTemplatesByUniqueKeysResponse);
    // GetQuestionsByTemplateKeys 通过模板唯一键批量查询问题
    // (-- api-linter: core::0136::prepositions=disabled --)
    rpc GetQuestionsByTemplateKeys(GetQuestionsByTemplateKeysRequest) returns (GetQuestionsByTemplateKeysResponse);
    // GetGroomingQuestionsByQuestionKeys 通过 question 唯一键批量查询问题
    // (-- api-linter: core::0136::prepositions=disabled --)
    rpc GetGroomingQuestionsByQuestionKeys(GetGroomingQuestionsByQuestionKeysRequest) returns (GetGroomingQuestionsByQuestionKeysResponse);
    // GetReportsByUniqueKeys 通过唯一键批量查询报告
    // (-- api-linter: core::0136::prepositions=disabled --)
    rpc GetReportsByUniqueKeys(GetReportsByUniqueKeysRequest) returns (GetReportsByUniqueKeysResponse);
    // GetRecordsByReportKeys 通过报告唯一键批量查询发送记录
    // (-- api-linter: core::0136::prepositions=disabled --)
    rpc GetRecordsByReportKeys(GetRecordsByReportKeysRequest) returns (GetRecordsByReportKeysResponse);
}

// GetFulfillmentReportTemplateRequest
message GetFulfillmentReportTemplateRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    optional int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // care type
    CareType care_type = 3 [(buf.validate.field).enum.defined_only = true];
}

// GetFulfillmentReportTemplateResponse
message GetFulfillmentReportTemplateResponse {
    // template
    FulfillmentReportTemplate template = 1;
}

// UpdateFulfillmentReportTemplateRequest
message UpdateFulfillmentReportTemplateRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    optional int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // staff id
    int64 staff_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // id
    int64 id = 4 [(buf.validate.field) = { int64: { gt: 0 } }];
    // care type
    CareType care_type = 5 [(buf.validate.field).enum.defined_only = true];
    // title
    string title = 6 [(buf.validate.field).string.max_len = 255];
    // theme color
    string theme_color = 7 [(buf.validate.field).string.pattern = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"];
    // light theme color
    string light_theme_color = 8 [(buf.validate.field).string.pattern = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"];
    // theme code
    string theme_code = 9 [(buf.validate.field).string.max_len = 255];
    // thank you message
    string thank_you_message = 10 [(buf.validate.field).string.max_len = 255];
    // show showcase
    bool show_showcase = 11;
    // show overall feedback
    bool show_overall_feedback = 12;
    // show pet condition
    bool show_pet_condition = 13;
    // show staff
    bool show_staff = 14;
    // show customized feedback
    bool show_customized_feedback = 15;
    // show next appointment
    bool show_next_appointment = 16;
    // next appointment date format type
    NextAppointmentDateFormatType next_appointment_date_format_type = 17 [(buf.validate.field).enum.defined_only = true];
    // show review booster
    bool show_review_booster = 18;
    // show yelp review icon
    bool show_yelp_review = 19;
    // yelp review icon jump link
    string yelp_review_link = 20;
    // show google review icon
    bool show_google_review = 21;
    // google review icon jump link
    string google_review_link = 22;
    // show facebook review icon
    bool show_facebook_review = 23;
    // facebook review icon jump link
    string facebook_review_link = 24;
    // questions
    repeated UpdateQuestion questions = 25;
    // delete questions
    repeated int64 delete_question_ids = 26 [(buf.validate.field).repeated.unique = true];

    // question
    message UpdateQuestion {
        // id
        int64 id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
        // question category
        QuestionCategory category = 2 [(buf.validate.field).enum.defined_only = true];
        // question type
        QuestionType type = 3 [(buf.validate.field).enum.defined_only = true];
        // system default question key
        string key = 4 [(buf.validate.field).string.max_len = 255];
        // title
        string title = 5 [(buf.validate.field).string.max_len = 255];
        // is default question
        bool is_default = 6;
        // is required to fill in
        bool is_required = 7;
        // is type editable
        bool is_type_editable = 8;
        // is title editable
        bool is_title_editable = 9;
        // is options editable
        bool is_options_editable = 10;
        // sort value, in descending order
        int32 sort = 11;
        // extra info
        FulfillmentReportTemplateQuestion.ExtraInfo extra = 12;
    }
}

// UpdateFulfillmentReportTemplateResponse
message UpdateFulfillmentReportTemplateResponse {

}

// get fulfillment report
message GetFulfillmentReportRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // report id (if not provided, must provide appointment_id, pet_id, care_type and service_date)
    optional int64 report_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // appointment id
    optional int64 appointment_id = 4 [(buf.validate.field) = { int64: { gt: 0 } }];
    // pet id
    optional int64 pet_id = 5 [(buf.validate.field) = { int64: { gt: 0 } }];
    // care type
    optional CareType care_type = 6 [(buf.validate.field).enum.defined_only = true];
    // service date, if care type is BOARDING/DAYCARE, this field is required
    // (-- api-linter: core::0142::time-field-type=disabled --)
    optional string service_date = 7;
}

// GetFulfillmentReportResponse
message GetFulfillmentReportResponse {
    // fulfillment report
    FulfillmentReport fulfillment_report = 1;
    // is need refresh
    bool is_need_refresh = 2;
}

// update fulfillment report
message UpdateFulfillmentReportRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    optional int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // operator staff id
    int64 staff_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // id
    optional int64 id = 4 [(buf.validate.field) = { int64: { gt: 0 } }];
    // customer id
    int64 customer_id = 5 [(buf.validate.field) = { int64: { gt: 0 } }];
    // appointment id
    int64 appointment_id = 6 [(buf.validate.field) = { int64: { gt: 0 } }];
    // pet id
    int64 pet_id = 7 [(buf.validate.field) = { int64: { gt: 0 } }];
    // care type
    CareType care_type = 8 [(buf.validate.field).enum.defined_only = true];
    // service date, if care type is BOARDING/DAYCARE, this field is required
    // (-- api-linter: core::0142::time-field-type=disabled --)
    optional string service_date = 10;
    // report content
    FulfillmentReportContent content = 11 [(buf.validate.field) = { required: true }];
    // theme code
    optional string theme_code = 12;
}

// UpdateFulfillmentReportResponse
message UpdateFulfillmentReportResponse {
    // fulfillment report
    FulfillmentReport fulfillment_report = 1;
    // is need refresh
    bool is_need_refresh = 2;
}

// get fulfillment report summary info
message GetFulfillmentReportSummaryInfoRequest {
    // fulfillment report id (if not provided, must provide uuid)
    optional int64 fulfillment_report_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // uuid (if not provided, must provide fulfillment report id)
    optional string uuid = 2;
}

// GetFulfillmentReportSummaryInfoResponse
message GetFulfillmentReportSummaryInfoResponse {
    // summary info
    FulfillmentReportCardSummaryInfo summary_info = 1;
}

// GetFulfillmentReportRecordsRequest
message GetFulfillmentReportRecordsRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    optional int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // appointment id
    int64 appointment_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // service date
    // (-- api-linter: core::0142::time-field-type=disabled --)
    optional string service_date = 4;
}

// GetFulfillmentReportRecordsResponse
message GetFulfillmentReportRecordsResponse {
    // fulfillment report records
    repeated FulfillmentReportRecords fulfillment_report_records = 1;

    // FulfillmentReportRecords
    message FulfillmentReportRecords {
        // fulfillment report
        FulfillmentReport fulfillment_report = 1;
        // fulfillment report send records
        repeated FulfillmentReportSendRecord fulfillment_report_send_records = 2;
    }
}

// GetFulfillmentTemplateReportRequest
message GetFulfillmentTemplateReportRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    optional int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // care type
    CareType care_type = 3 [(buf.validate.field).enum.defined_only = true];
    // report id
    optional int64 report_id = 4 [(buf.validate.field) = { int64: { gt: 0 } }];
    // theme code
    optional string theme_code = 5;
}

// GetFulfillmentTemplateReportResponse
message GetFulfillmentTemplateReportResponse {
    // summary info
    FulfillmentReportCardSummaryInfo summary_info = 1;
    // fulfillment report sample value
    FulfillmentReportSampleValue sample_value = 2;
}

// ListFulfillmentThemeConfigRequest
// (-- api-linter: core::0158::request-page-token-field=disabled --)
// (-- api-linter: core::0132::request-parent-required=disabled --)
// (-- api-linter: core::0158::request-page-size-field=disabled --)
message ListFulfillmentThemeConfigRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
}

// ListFulfillmentThemeConfigResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled --)
// (-- api-linter: core::0132::response-unknown-fields=disabled --)
message ListFulfillmentThemeConfigResponse {
    // theme configs
    repeated FulfillmentReportThemeConfig theme_configs = 1;
}

// GenerateMessageContentRequest
message GenerateMessageContentRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // appointment id
    int64 appointment_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // pet id
    int64 pet_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // service date, if care type is BOARDING/DAYCARE, this field is required
    // (-- api-linter: core::0142::time-field-type=disabled --)
    optional string service_date = 4;
    // care type
    CareType care_type = 5 [(buf.validate.field).enum.defined_only = true];
}

// GenerateMessageContentResponse
message GenerateMessageContentResponse {
    // message content
    string message_content = 1;
}

// SendFulfillmentReportRequest
message SendFulfillmentReportRequest {
    // fulfillment report id
    optional int64 fulfillment_report_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 
    // send method
    SendMethod send_method = 2 [(buf.validate.field).enum.defined_only = true];
    // recipient emails, if send method is EMAIL, this field is required
    repeated string recipient_emails = 3 [(buf.validate.field) = { repeated: {
        unique: true
        items: {
            string: {
                email: true
                min_len: 1
            }
        }
    }}];
    // email subject, if send method is EMAIL, this field is required
    optional string email_subject = 4 [(buf.validate.field).string.max_len = 255];
}

// SendFulfillmentReportResponse
message SendFulfillmentReportResponse {
    // send result
    FulfillmentReportSendResult send_result = 1;
}

// GetFulfillmentReportSendHistoryRequest
message GetFulfillmentReportSendHistoryRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // appointment id
    int64 appointment_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // pet id
    int64 pet_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // care type
    CareType care_type = 4 [(buf.validate.field).enum.defined_only = true];
}

// GetFulfillmentReportSendHistoryResponse
message GetFulfillmentReportSendHistoryResponse {
    // send history
    repeated FulfillmentReportSendRecord send_records = 1;
}

// (-- api-linter: core::0158::request-page-token-field=disabled --)
// (-- api-linter: core::0132::request-parent-required=disabled --)
// (-- api-linter: core::0158::request-page-size-field=disabled --)
// ListSendReportRecordsRequest
message ListSendReportRecordsRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // filter
    ListSendReportRecordsFilter filter = 3;
    // pagination
    PaginationRef pagination = 4 [(buf.validate.field) = { required: true }];
}

// ListSendReportRecordsFilter
message ListSendReportRecordsFilter {
    // appointment ids
    repeated int64 appointment_ids = 1 [(buf.validate.field) = { repeated: {
        unique: true
        items: {int64: {gt: 0}}
    }}];
    // pet ids
    repeated int64 pet_ids = 2 [(buf.validate.field) = { repeated: {
        unique: true
        items: {int64: {gt: 0}}
    }}];
    // care types
    repeated CareType care_types = 3 [(buf.validate.field) = { repeated: {
        unique: true
        items: {
            enum: {
                defined_only: true
                not_in: [0]
            }
        }
    }}];
    // start date
    // (-- api-linter: core::0142::time-field-type=disabled --)
    optional string start_date = 4;
    // end date
    // (-- api-linter: core::0142::time-field-type=disabled --)
    optional string end_date = 5;
    // send method
    optional SendMethod send_method = 6 [(buf.validate.field).enum.defined_only = true];
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled --)
// (-- api-linter: core::0132::response-unknown-fields=disabled --)
// ListSendReportRecordsResponse
message ListSendReportRecordsResponse {
    // send history
    repeated FulfillmentReportSendRecord send_records = 1;
    // pagination
    PaginationRef pagination = 2;
    // total
    int32 total = 3;
}

// GetFulfillmentReportSendResultRequest
message GetFulfillmentReportSendResultRequest {
    // fulfillment report id, if not provided, must provide appointment_id, pet_id, care_type and service_date
    optional int64 fulfillment_report_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // appointment id
    optional int64 appointment_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // pet id
    optional int64 pet_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // care type
    optional CareType care_type = 4 [(buf.validate.field).enum.defined_only = true];
    // service date
    // (-- api-linter: core::0142::time-field-type=disabled --)
    optional string service_date = 5;
}

// GetFulfillmentReportSendResultResponse
message GetFulfillmentReportSendResultResponse {
    // send result
    repeated FulfillmentReportSendResult send_results = 1;
}

// ListFulfillmentReportRequest
// (-- api-linter: core::0158::request-page-token-field=disabled --)
// (-- api-linter: core::0132::request-parent-required=disabled --)
// (-- api-linter: core::0158::request-page-size-field=disabled --)
message ListFulfillmentReportRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // filter
    ListFulfillmentReportConfigFilter filter = 3;
    // pagination
    PaginationRef pagination = 4 [(buf.validate.field) = { required: true }];
}

// ListFulfillmentReportResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled --)
// (-- api-linter: core::0132::response-unknown-fields=disabled --)
message ListFulfillmentReportResponse {
    // fulfillment report cards
    repeated FulfillmentReportCard fulfillment_report_cards = 1;
    // 分页信息
    PaginationRef pagination = 2;
    // 总条数
    int32 total = 3;

    // fulfillment report card
    message FulfillmentReportCard {
        // report id
        int64 report_id = 1;
        // pet
        Pet pet = 2;
        // customer id
        int64 customer_id = 3;
        // last update time
        google.protobuf.Timestamp update_time = 4;
        // care type
        CareType care_type = 5;
        // media(image/video) count
        int32 media_count = 6;
        // service date, date of report
        // (-- api-linter: core::0142::time-field-type=disabled --)
        string service_date = 7;
        // uuid
        string uuid = 8;
        // appointment id
        int64 appointment_id = 9;
        // title, template title
        string title = 10;
        // send record
        repeated FulfillmentReportSendRecord send_record = 11;
        // pet
        message Pet {
            // pet id
            int64 pet_id = 1;
            // pet name
            // (-- api-linter: core::0122::name-suffix=disabled --)
            string pet_name = 2;
            // pet avatar
            string avatar_path = 3;
            // pet type
            backend.proto.pet.v1.Pet.PetType pet_type = 4;
        }
    }
}

// list fulfillment report config filter
message ListFulfillmentReportConfigFilter {
    // report card status
    optional ReportStatus status = 1 [(buf.validate.field) = { enum: {
        defined_only: true
        not_in: 0
    }}];
    // care types
    repeated CareType care_types = 2 [(buf.validate.field) = { repeated: {
        unique: true
        items: {
            enum: {
                defined_only: true
                not_in: [0]
            }
        }
    }}];
    // start date
    // (-- api-linter: core::0142::time-field-type=disabled --)
    optional string start_date = 3;
    // end date
    // (-- api-linter: core::0142::time-field-type=disabled --)
    optional string end_date = 4;
    // pet id
    optional int64 pet_id = 5 [(buf.validate.field) = { int64: { gt: 0 } }];
}
// BatchDeleteFulfillmentReportRequest
// (-- api-linter: core::0235::request-names-field=disabled --)
// (-- api-linter: core::0235::request-unknown-fields=disabled --)
message BatchDeleteFulfillmentReportRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // report card ids
    repeated int64 report_card_ids = 3 [(buf.validate.field) = { repeated: {
        unique: true
        items: {int64: {gt: 0}}
    }}];
}

// BatchDeleteFulfillmentReportResponse
// (-- api-linter: core::0235::response-resource-field=disabled --)
message BatchDeleteFulfillmentReportResponse {}

// BatchSendFulfillmentReportRequest
message BatchSendFulfillmentReportRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // report ids
    repeated int64 report_ids = 3 [(buf.validate.field) = { repeated: {
        unique: true
        items: {
            int64: {gt: 0}
        }
    }}];
    // send method
    SendMethod send_method = 4 [(buf.validate.field) = { enum: {
        defined_only: true
        not_in: 0
    }}];
    // staff id
    int64 staff_id = 5 [(buf.validate.field) = { int64: { gt: 0 } }];
}

// BatchSendFulfillmentReportResponse
message BatchSendFulfillmentReportResponse {
    // send results
    repeated FulfillmentReportSendResult send_results = 1;
}

// IncreaseFulfillmentOpenedCountRequest
message IncreaseFulfillmentOpenedCountRequest {
    // fulfillment report id
    string uuid = 1 [(buf.validate.field).string.max_len = 50];
}

// IncreaseFulfillmentOpenedCountResponse
message IncreaseFulfillmentOpenedCountResponse {}

// CountFulfillmentReportRequest
message CountFulfillmentReportRequest {
    // company id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // business id
    int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // filter
    ListFulfillmentReportConfigFilter filter = 3;
}

// CountFulfillmentReportResponse
message CountFulfillmentReportResponse {
    // total
    int32 total = 1;
    // draft count
    int32 draft_count = 2;
    // sent count
    int32 sent_count = 3;
}

// ------------------------------
// 双写接口相关消息定义
// ------------------------------
// 双写专用的模板数据结构，贴近数据库表结构
message FulfillmentReportTemplateSync {
    // 主键 id (用于基于ID的更新和删除)
    optional int64 id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 公司 id
    int64 company_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 业务 id
    int64 business_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 护理类型
    CareType care_type = 4 [(buf.validate.field).enum.defined_only = true];
    // 感谢信息
    string thank_you_message = 5 [(buf.validate.field).string.max_len = 255];
    // 主题颜色
    string theme_color = 6 [(buf.validate.field).string.max_len = 30];
    // 浅色主题颜色
    string light_theme_color = 7 [(buf.validate.field).string.max_len = 30];
    // 是否显示展示区域
    bool show_showcase = 8;
    // 是否显示整体反馈
    bool show_overall_feedback = 9;
    // 是否显示个性化反馈
    bool show_customized_feedback = 10;
    // 是否显示宠物状况
    bool show_pet_condition = 11;
    // 是否显示服务员工姓名
    // (-- api-linter: core::0122::name-suffix=disabled --)
    bool show_service_staff_name = 12;
    // 是否显示下次预约
    bool show_next_appointment = 13;
    // 下次预约日期格式类型
    NextAppointmentDateFormatType next_appointment_date_format_type = 14;
    // 是否显示评价提升器
    bool show_review_booster = 15;
    // 是否显示 Yelp 评价
    bool show_yelp_review = 16;
    // 是否显示 Google 评价
    bool show_google_review = 17;
    // 是否显示 Facebook 评价
    bool show_facebook_review = 18;
    // 最后发布时间
    google.protobuf.Timestamp last_publish_time = 19;
    // 标题
    string title = 20 [(buf.validate.field).string.max_len = 50];
    // 更新者
    // (-- api-linter: core::0140::prepositions=disabled --)
    int64 update_by = 21;
    // 创建时间
    google.protobuf.Timestamp create_time = 22;
    // 更新时间
    google.protobuf.Timestamp update_time = 23;
    // theme code
    string theme_code = 24;
}

// 模板唯一键标识
message FulfillmentReportTemplateUniqueKey {
    // 公司 id
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 业务 id
    int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 护理类型
    CareType care_type = 3 [(buf.validate.field).enum.defined_only = true];
}

// 同步履约报告模板请求
message SyncFulfillmentReportTemplateRequest {
    // 操作类型：CREATE, UPDATE, UPSERT, DELETE
    SyncOperation operation = 1 [(buf.validate.field).enum.defined_only = true];

    // 定位方式：通过ID或唯一键
    oneof identifier {
        // 通过ID定位（适用于已知目标系统记录ID的场景）
        int64 template_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
        // 通过唯一键定位（适用于基于业务语义的双写场景）
        FulfillmentReportTemplateUniqueKey unique_key = 3;
    }

    // 模板数据（CREATE/UPDATE/UPSERT 时必填，DELETE 时可选）
    optional FulfillmentReportTemplateSync template = 4;
}

// 同步履约报告模板响应
message SyncFulfillmentReportTemplateResponse {
    // 同步后的模板 ID
    int64 template_id = 1;
    // 同步状态
    SyncStatus status = 2;
    // 错误消息（如果有）
    string error_message = 3;
}

// 双写专用的问题数据结构，贴近数据库表结构
message FulfillmentReportQuestionSync {
    // 主键 id (用于基于ID的更新和删除)
    optional int64 id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 公司 id
    int64 company_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 业务 id
    int64 business_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // care type
    CareType care_type = 4 [(buf.validate.field).enum.defined_only = true];
    // 问题类别
    QuestionCategory category = 5 [(buf.validate.field).enum.defined_only = true];
    // 问题类型
    QuestionType type = 6 [(buf.validate.field).enum.defined_only = true];
    // 问题唯一键
    string key = 7 [(buf.validate.field).string.max_len = 50];
    // 问题标题
    string title = 8 [(buf.validate.field).string.max_len = 50];
    // 扩展配置 JSON
    string extra_json = 9;
    // 是否默认问题
    bool is_default = 10;
    // 是否必填
    bool is_required = 11;
    // 类型是否可编辑
    bool is_type_editable = 12;
    // 标题是否可编辑
    bool is_title_editable = 13;
    // 选项是否可编辑
    bool is_options_editable = 14;
    // 排序值
    int32 sort = 15;
    // 创建时间
    google.protobuf.Timestamp create_time = 16;
    // 更新时间
    google.protobuf.Timestamp update_time = 17;
}

// 问题模板标识符
message QuestionTemplateIdentifier {
    // 定位方式：通过template_id或唯一键
    oneof identifier {
        // 通过模板ID定位
        int64 template_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
        // 通过模板唯一键定位
        FulfillmentReportTemplateUniqueKey template_unique_key = 2;
    }
}

// 批量同步履约报告问题请求
message BatchSyncFulfillmentReportQuestionsRequest {
    // 操作类型：CREATE, UPDATE, UPSERT, DELETE
    SyncOperation operation = 1 [(buf.validate.field).enum.defined_only = true];

    // 模板标识符（定位所属模板）
    QuestionTemplateIdentifier template_identifier = 2 [(buf.validate.field) = { required: true }];

    // 问题数据列表
    repeated FulfillmentReportQuestionSync questions = 3;
}

// 批量同步履约报告问题响应
message BatchSyncFulfillmentReportQuestionsResponse {
    // 同步状态
    SyncStatus status = 2;
    // 错误消息（如果有）
    string error_message = 3;
    // 关联的模板ID
    int64 template_id = 4;
}

// 双写专用的履约报告数据结构，贴近数据库表结构
message FulfillmentReportSync {
    // 主键 id (用于基于ID的更新和删除)
    optional int64 id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 公司 id
    int64 company_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 业务 id
    int64 business_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 客户 id
    int64 customer_id = 4 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 预约 id
    int64 appointment_id = 5 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 护理类型
    CareType care_type = 6 [(buf.validate.field).enum.defined_only = true];
    // 宠物 id
    int64 pet_id = 7 [(buf.validate.field) = { int64: { gt: 0 } }];
    // pet type id
    int64 pet_type_id = 8 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 唯一标识符
    string uuid = 9 [(buf.validate.field).string.max_len = 50];
    // 模板版本
    // (-- api-linter: core::0142::time-field-names=disabled --)
    google.protobuf.Timestamp template_version = 10;
    // 模板JSON数据
    string template_json = 11;
    // 内容JSON数据
    string content_json = 12;
    // 报告状态
    string status = 13 [(buf.validate.field).string.max_len = 20];
    // 链接打开次数
    int32 link_opened_count = 14;
    // 服务日期
    // (-- api-linter: core::0142::time-field-type=disabled --)
    string service_date = 15;
    // 更新者
    // (-- api-linter: core::0140::prepositions=disabled --)
    int64 update_by = 16;
    // 创建时间
    google.protobuf.Timestamp create_time = 17;
    // 更新时间
    google.protobuf.Timestamp update_time = 18;
    // theme code
    string theme_code = 19;
}

// 履约报告唯一键标识
message FulfillmentReportUniqueKey {
    // 业务 id
    int64 business_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 预约 id
    int64 appointment_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 宠物 id
    int64 pet_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 护理类型
    CareType care_type = 4 [(buf.validate.field).enum.defined_only = true];
    // 服务日期
    // (-- api-linter: core::0142::time-field-type=disabled --)
    string service_date = 5;
}

// 同步履约报告请求
message SyncFulfillmentReportRequest {
    // 操作类型：CREATE, UPDATE, UPSERT, DELETE
    SyncOperation operation = 1 [(buf.validate.field).enum.defined_only = true];

    // 定位方式：通过ID或唯一键
    oneof identifier {
        // 通过ID定位（适用于已知目标系统记录ID的场景）
        int64 report_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
        // 通过唯一键定位（适用于基于业务语义的双写场景）
        FulfillmentReportUniqueKey unique_key = 3;
    }

    // 报告数据（CREATE/UPDATE/UPSERT 时必填，DELETE 时可选）
    optional FulfillmentReportSync report = 4;
}

// 同步履约报告响应
message SyncFulfillmentReportResponse {
    // 同步后的报告 ID
    int64 report_id = 1;
    // 同步状态
    SyncStatus status = 2;
    // 错误消息（如果有）
    string error_message = 3;
}

// 双写专用的发送记录数据结构，贴近数据库表结构
message FulfillmentReportSendRecordSync {
    // 主键 id (用于基于ID的更新和删除)
    optional int64 id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 报告 id
    int64 report_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 公司 id
    int64 company_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 业务 id
    int64 business_id = 4 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 预约 id
    int64 appointment_id = 5 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 内容JSON数据
    string content_json = 6;
    // 宠物 id
    int64 pet_id = 7 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 发送方式
    SendMethod send_method = 8 [(buf.validate.field).enum.defined_only = true];
    // 发送时间
    google.protobuf.Timestamp sent_time = 9;
    // 发送者
    // (-- api-linter: core::0140::prepositions=disabled --)
    int64 sent_by = 10;
    // 错误消息
    string error_message = 11 [(buf.validate.field).string.max_len = 500];
    // 是否发送成功
    bool is_sent_success = 12;
    // 创建时间
    google.protobuf.Timestamp create_time = 13;
    // 更新时间
    google.protobuf.Timestamp update_time = 14;
}

// 发送记录标识符
message SendRecordIdentifier {
    // 发送方式（必须）
    SendMethod send_method = 1 [(buf.validate.field).enum.defined_only = true];
    // 报告唯一键（必须）
    FulfillmentReportUniqueKey report_unique_key = 2 [(buf.validate.field) = { required: true }];
}

// 同步履约报告发送记录请求
message SyncFulfillmentReportSendRecordRequest {
    // 操作类型：CREATE, UPDATE, UPSERT, DELETE
    SyncOperation operation = 1 [(buf.validate.field).enum.defined_only = true];

    // 定位方式：通过记录ID或业务标识符
    oneof identifier {
        // 通过记录ID定位（适用于已知记录ID的场景）
        int64 record_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
        // 通过业务标识符定位（适用于基于业务语义的双写场景）
        SendRecordIdentifier business_identifier = 3;
    }

    // 发送记录数据（CREATE/UPDATE/UPSERT 时必填，DELETE 时可选）
    optional FulfillmentReportSendRecordSync send_record = 4;
}

// 同步履约报告发送记录响应
message SyncFulfillmentReportSendRecordResponse {
    // 同步后的记录 ID
    int64 record_id = 1;
    // 关联的报告 ID
    int64 report_id = 2;
    // 同步状态
    SyncStatus status = 3;
    // 错误消息（如果有）
    string error_message = 4;
}

// ------------------------------
// 批量迁移接口相关消息定义
// ------------------------------
// 批量迁移模板请求
message BatchMigrateTemplatesRequest {
    // 模板数据列表（复用双写接口结构）
    repeated FulfillmentReportTemplateSync templates = 1;
}

// 批量迁移模板响应
message BatchMigrateTemplatesResponse {
    // 成功迁移的模板数量
    int32 success_count = 1;
    // 失败的模板数量
    int32 failed_count = 2;
    // 跳过的模板数量（已存在）
    int32 skipped_count = 3;
}

// 批量迁移问题请求
message BatchMigrateQuestionsRequest {
    // 问题数据列表（复用双写接口结构）
    repeated FulfillmentReportQuestionSync questions = 1 [(buf.validate.field) = { repeated: {
        min_items: 1
        max_items: 2000
    }}];
}

// 批量迁移问题响应
message BatchMigrateQuestionsResponse {
    // 成功迁移的问题数量
    int32 success_count = 1;
    // 失败的问题数量
    int32 failed_count = 2;
    // 跳过的问题数量
    int32 skipped_count = 3;
}

// 批量迁移报告请求
message BatchMigrateReportsRequest {
    // 报告数据列表（复用双写接口结构）
    repeated FulfillmentReportSync reports = 1;
}

// 批量迁移报告响应
message BatchMigrateReportsResponse {
    // 成功迁移的报告数量
    int32 success_count = 1;
    // 失败的报告数量
    int32 failed_count = 2;
    // 跳过的报告数量
    int32 skipped_count = 3;
}

// 批量迁移发送记录请求
message BatchMigrateRecordsRequest {
    // 发送记录数据列表（复用双写接口结构）
    repeated FulfillmentReportSendRecordSync records = 1;
}

// 批量迁移发送记录响应
message BatchMigrateRecordsResponse {
    // 成功迁移的发送记录数量
    int32 success_count = 1;
    // 失败的发送记录数量
    int32 failed_count = 2;
    // 跳过的发送记录数量
    int32 skipped_count = 3;
}

// ------------------------------
// 数据查询接口相关消息定义
// ------------------------------
// 通过唯一键批量查询模板请求
message GetTemplatesByUniqueKeysRequest {
    // 模板唯一键列表
    repeated FulfillmentReportTemplateUniqueKey unique_keys = 1 [(buf.validate.field) = { repeated: {
        min_items: 1
        max_items: 2000
    }}];
}

// 通过唯一键批量查询模板响应
message GetTemplatesByUniqueKeysResponse {
    // 模板数据列表
    repeated FulfillmentReportTemplateSync templates = 1;
}

// 通过模板唯一键批量查询问题请求
message GetQuestionsByTemplateKeysRequest {
    // 模板唯一键列表
    repeated FulfillmentReportTemplateUniqueKey template_keys = 1 [(buf.validate.field) = { repeated: {
        min_items: 1
        max_items: 2000
    }}];
}

// 通过模板唯一键批量查询问题响应
message GetQuestionsByTemplateKeysResponse {
    // 问题数据列表
    repeated FulfillmentReportQuestionSync questions = 1;
}

// 通过问题唯一键批量查询问题请求（grooming迁移专用）
message GetGroomingQuestionsByQuestionKeysRequest {
    // 问题唯一键列表
    repeated GroomingQuestionUniqueKey question_keys = 1 [(buf.validate.field) = { repeated: {
        min_items: 1
        max_items: 2000
    }}];
}

// grooming问题唯一键标识（迁移专用）
message GroomingQuestionUniqueKey {
    // 公司ID
    int64 company_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 业务ID
    int64 business_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // 问题标题
    string title = 3 [(buf.validate.field).string.min_len = 1];
}

// 通过问题唯一键批量查询问题响应（grooming迁移专用）
message GetGroomingQuestionsByQuestionKeysResponse {
    // 问题数据列表
    repeated FulfillmentReportQuestionSync questions = 1;
}

// 通过唯一键批量查询报告请求
message GetReportsByUniqueKeysRequest {
    // 报告唯一键列表
    repeated FulfillmentReportUniqueKey unique_keys = 1 [(buf.validate.field) = { repeated: {
        min_items: 1
        max_items: 2000
    }}];
}

// 通过唯一键批量查询报告响应
message GetReportsByUniqueKeysResponse {
    // 报告数据列表
    repeated FulfillmentReportSync reports = 1;
}

// 通过报告唯一键批量查询发送记录请求
message GetRecordsByReportKeysRequest {
    // 发送记录唯一键列表（每个包含报告唯一键和发送方式）
    repeated SendRecordUniqueKey record_keys = 1 [(buf.validate.field) = { repeated: {
        min_items: 1
        max_items: 2000
    }}];
}

// 发送记录唯一键标识
message SendRecordUniqueKey {
    // 报告唯一键
    FulfillmentReportUniqueKey report_unique_key = 1 [(buf.validate.field) = { required: true }];
    // 发送方式
    SendMethod send_method = 2 [(buf.validate.field).enum.defined_only = true];
}

// 通过报告唯一键批量查询发送记录响应
message GetRecordsByReportKeysResponse {
    // 发送记录数据列表
    repeated FulfillmentReportSendRecordSync records = 1;
}

// 同步操作类型
enum SyncOperation {
    // 未指定
    SYNC_OPERATION_UNSPECIFIED = 0;
    // 创建
    CREATE = 1;
    // 更新
    UPDATE = 2;
    // 创建或更新
    UPSERT = 3;
    // 删除
    DELETE = 4;
}

// 同步状态
// (-- api-linter: core::0216::synonyms=disabled --)
enum SyncStatus {
    // 未指定
    SYNC_STATUS_UNSPECIFIED = 0;
    // 成功
    SUCCESS = 1;
    // 失败
    FAILED = 2;
    // 部分成功
    PARTIAL_SUCCESS = 3;
}
