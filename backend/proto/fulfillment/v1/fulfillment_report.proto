syntax = "proto3";

package backend.proto.fulfillment.v1;

import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "backend/proto/fulfillment/v1/common.proto";
import "backend/proto/fulfillment/v1/appointment.proto";
import "backend/proto/pet/v1/pet.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// fulfillment report template
message FulfillmentReportTemplate {
    // fulfillment report template id
    int64 id = 1;
    // company id
    int64 company_id = 2;
    // business id
    int64 business_id = 3;
    // care type
    CareType care_type = 4 [(buf.validate.field).enum.defined_only = true];
    // title
    string title = 5;
    // theme color
    string theme_color = 6;
    // light theme color
    string light_theme_color = 7;
    // theme code
    string theme_code = 8;
    // thank you message
    string thank_you_message = 9;
    // show showcase
    bool show_showcase = 10;
    // show overall feedback
    bool show_overall_feedback = 11;
    // show pet condition
    bool show_pet_condition = 12;
    // show staff
    bool show_staff = 13;
    // show customized feedback
    bool show_customized_feedback = 14;
    // show next appointment
    bool show_next_appointment = 15;
    // next appointment date format type
    NextAppointmentDateFormatType next_appointment_date_format_type = 16;
    // show review booster
    bool show_review_booster = 17;
    // show yelp review icon
    bool show_yelp_review = 18;
    // yelp review icon jump link
    string yelp_review_link = 19;
    // show google review icon
    bool show_google_review = 20;
    // google review icon jump link
    string google_review_link = 21;
    // show facebook review icon
    bool show_facebook_review = 22;
    // facebook review icon jump link
    string facebook_review_link = 23;
    // last publish time(template version)
    google.protobuf.Timestamp last_publish_time = 24;
    // create time
    google.protobuf.Timestamp create_time = 25;
    // update time
    google.protobuf.Timestamp update_time = 26;
    // update by
    // (-- api-linter: core::0140::prepositions=disabled --)
    int64 update_by = 27;
    // questions
    repeated FulfillmentReportTemplateQuestion questions = 28;
}

// fulfillment report template question
message FulfillmentReportTemplateQuestion {
    // fulfillment report template question id
    int64 id = 1;
    // care type
    CareType care_type = 2;
    // question category
    QuestionCategory category = 3;
    // question type
    QuestionType type = 4;
    // system default question key
    string key = 6;
    // title
    string title = 7;
    // is default question
    bool is_default = 8;
    // is required to fill in
    bool is_required = 9;
    // is type editable
    bool is_type_editable = 10;
    // is title editable
    bool is_title_editable = 11;
    // is options editable
    bool is_options_editable = 12;
    // sort value, in descending order
    int32 sort = 13;
    // create time
    google.protobuf.Timestamp create_time = 14;
    // update time
    google.protobuf.Timestamp update_time = 15;
    // extra info
    ExtraInfo extra = 16;

    // extra info
    message ExtraInfo {
        // default options list, not allow to change
        // (-- api-linter: core::0140::prepositions=disabled --)
        repeated string build_in_options = 1;
        // single_choice/multi_choice question's options list
        repeated string options = 2;
    }
}

// fulfillment report
message FulfillmentReport {
  // id 
  optional int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // company id
  int64 company_id = 2 [(buf.validate.field).int64.gt = 0];
  // business id
  int64 business_id = 3;
  // customer id
  int64 customer_id = 4;
  // appointment id
  int64 appointment_id = 5 [(buf.validate.field).int64.gt = 0];
  // pet id
  int64 pet_id = 6 [(buf.validate.field).int64.gt = 0];
  // pet type id
  int64 pet_type_id = 7;
  // care type
  CareType care_type = 8;
  // service date
  // (-- api-linter: core::0142::time-field-type=disabled --)
  string service_date = 9;
  // report status
  ReportStatus status = 10;
  // uuid
  string uuid = 11;
  // link opened count
  int32 link_opened_count = 12;
  // theme code
  string theme_code = 13;
  // template version
  // (-- api-linter: core::0142::time-field-names=disabled --)
  google.protobuf.Timestamp template_version = 14;
  // create time
  google.protobuf.Timestamp create_time = 15;
  // update time
  google.protobuf.Timestamp update_time = 16;
  // report current template
  FulfillmentReportTemplate template = 17;
  // report content
  FulfillmentReportContent content = 18;
}

// fulfillment report content
message FulfillmentReportContent {
  // photo
  repeated string photos = 1;
  // video
  repeated string videos = 2;
  // feedbacks
  repeated FulfillmentReportQuestion feedbacks = 3;
  // pet condition
  repeated FulfillmentReportQuestion pet_conditions = 4;
  // recommendation
  optional FulfillmentReportRecommendation recommendation = 5;
  // theme color
  string theme_color = 6 [(buf.validate.field).string.pattern = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"];
  // light theme color
  optional string light_theme_color = 7 [(buf.validate.field).string.pattern = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"];
}

// report card question
message FulfillmentReportQuestion {
    // id
    int64 id = 1;
    // fulfillment report id
    int64 fulfillment_report_id = 2;
    // category
    QuestionCategory category = 3;
    // type, 1: single choice, 2: multiple choice, 3: input text, 4: short input text, 5: tag choice
    QuestionType type = 4;
    // key, unique for each question
    string key = 5;
    // title, question title
    string title = 6 ;
    // required
    bool required = 7;
    // is show
    bool is_show = 8;
    // options, only for single choice and multiple choice
    repeated string options = 9;
    // choices, only for single choice and multiple choice
    repeated string choices = 10;
    // custom options
    repeated string custom_options = 11;
    // input text, only for input text
    string input_text = 12;
    // placeholder, input text placeholder
    string placeholder = 13;
    // body view urls
    BodyViewUrl urls = 14;
}

// fulfillment report recommendation
message FulfillmentReportRecommendation {
  // frequency day
  int32 frequency_day = 1;
  // frequency type
  FrequencyType frequency_type = 2;
  // formatted frequency text
  string frequency_text = 3;
  // next appointment date
  // (-- api-linter: core::0142::time-field-type=disabled --)
  string next_appointment_date = 4;
  // next appointment date text
  string next_appointment_date_text = 5;

  // frequency type
  enum FrequencyType {
    // unspecified value
    FREQUENCY_TYPE_UNSPECIFIED = 0;
    // day
    DAY = 1;
    // week
    WEEK = 2;
    // month
    MONTH = 3;
  }
}

// fulfillment report card summary info
message FulfillmentReportCardSummaryInfo {
  // business info
  BusinessInfo business_info = 1;
  // pet info
  PetInfo pet_info = 2;
  // current appointment info
  AppointmentInfo appointment_info = 3;
  // next appointment info
  AppointmentInfo next_appointment_info = 4;
  // fulfillment report
  FulfillmentReport fulfillment_report = 5;
  // review booster config
  ReviewBoosterConfig review_booster_config = 6;
  // review booster record
  ReviewBoosterRecord review_booster_record = 7;
  // theme config
  FulfillmentReportThemeConfig theme_config = 8;
  // presetTags
  repeated string preset_tags = 9;

  // business info
  message BusinessInfo {
    // business id
    int64 business_id = 1;
    // business name
    // (-- api-linter: core::0122::name-suffix=disabled --)
    string business_name = 2;
    // business avatar path
    string avatar_path = 3;
  }

  // pet info
  message PetInfo {
    // pet id
    int64 pet_id = 1;
    // pet name
    // (-- api-linter: core::0122::name-suffix=disabled --)
    string pet_name = 2;
    // pet avatar path
    string avatar_path = 3;
    // pet breed
    string pet_breed = 4;
    // gender
    backend.proto.pet.v1.Pet.PetGender gender = 5;
    // pet type
    backend.proto.pet.v1.Pet.PetType pet_type = 6;
    // weight
    string weight = 7;
    // weight with unit
    // (-- api-linter: core::0140::prepositions=disabled --)
    string weight_with_unit = 8;
  }

  // appointment info 
  message AppointmentInfo {
    // appointment id
    int64 appointment_id = 1;
    // appointment state
    // (-- api-linter: core::0216::state-field-output-only=disabled --)
    AppointmentState state = 2;
    // appointment date
    // (-- api-linter: core::0142::time-field-type=disabled --)
    string appointment_date = 3;
    // appointment time
    // (-- api-linter: core::0142::time-field-type=disabled --)
    int32 appointment_start_time = 4;
    // appointment end time
    // (-- api-linter: core::0142::time-field-type=disabled --)
    int32 appointment_end_time = 5;
    // arrival window before
    // (-- api-linter: core::0140::prepositions=disabled --)
    int32 arrival_window_before = 6;
    // appointment date time text
    string appointment_date_time_text = 7;
    // arrival window after
    // (-- api-linter: core::0140::prepositions=disabled --)
    int32 arrival_window_after = 8;
    // pet service
    repeated PetService pet_service = 9;
  }

  // pet service
  message PetService {
    // pet info
    PetInfo pet_info = 1;
    // pet service list
    repeated PetDetailInfo pet_details = 2;
  }

  // pet detail info
  message PetDetailInfo {
    // pet id
    int64 pet_id = 1;
    // service id
    int64 service_id = 2;
    // service name
    // (-- api-linter: core::0122::name-suffix=disabled --)
    string service_name = 3;
    // service type
    ServiceType service_type = 4;
    // start time
    // (-- api-linter: core::0142::time-field-type=disabled --)
    int32 start_time = 5;
    // service duration
    int32 service_duration = 6;
    // staff info
    StaffInfo staff_info = 7;
  }

  // staff info
  message StaffInfo {
    // staff id
    int64 staff_id = 1;
    // staff first name
    // (-- api-linter: core::0122::name-suffix=disabled --)
    string staff_first_name = 2;
    // staff last name
    // (-- api-linter: core::0122::name-suffix=disabled --)
    string staff_last_name = 3;
    // staff avatar path
    string staff_avatar_path = 4;
  }

  // review booster config
  message ReviewBoosterConfig {
    // positive score
    int32 positive_score = 1;
    // positive yelp
    string positive_yelp = 2;
    // positive facebook
    string positive_facebook = 3;
    // positive google
    string positive_google = 4;
  }
  // review booster record
  message ReviewBoosterRecord {
    // positive score
    int32 positive_score = 1;
    // review content
    string review_content = 2;
    // review time
    // (-- api-linter: core::0142::time-field-type=disabled --)
    int32 review_time = 3;
  }

}

// fulfillment report sample value
message FulfillmentReportSampleValue {
  // comment
  string comment = 1;
  // pet avatar url
  // (-- api-linter: core::0140::uri=disabled --)
  string pet_avatar_url = 2;
  // showcase urls
  repeated string showcase_urls = 3;
  // body view url
  BodyViewUrl urls = 4;
}

// fulfillment report theme config
message FulfillmentReportThemeConfig {
  // name
  string name = 1;
  // code
  string code = 2;
  // color
  string color = 3;
  // light color
  string light_color = 4;
  // img url
  // (-- api-linter: core::0140::uri=disabled --)
  string img_url = 5;
  // icon
  string icon = 6;
  // email bottom img url
  // (-- api-linter: core::0140::uri=disabled --)
  string email_bottom_img_url = 7;
  // recommend
  bool recommend = 8;
  // status
  // (-- api-linter: core::0216::synonyms=disabled --)
  ThemeConfigStatus status = 9;
  // tag
  ThemeConfigTag tag = 10;

  // theme config status
  // (-- api-linter: core::0126::unspecified=disabled --)
  // (-- api-linter: core::0216::synonyms=disabled --)
  enum ThemeConfigStatus {
    // inactive
    INACTIVE = 0;
    // active
    ACTIVE = 1;
    // hide
    HIDE = 2;
  }


  // theme config tag
  // (-- api-linter: core::0126::unspecified=disabled --)
  enum ThemeConfigTag {
    // normal
    NORMAL = 0;
    // need to upgrade to Growth+
    NEED_TO_UPGRADE_TO_GROWTH_PLUS = 1;
  }
}

// body view url
message BodyViewUrl {
  // left
  string left = 1;
  // right
  string right = 2;
}

// fulfillment report send result
message FulfillmentReportSendResult {
  // fulfillment report id
  int64 fulfillment_report_id = 1;
  // send method
  SendMethod send_method = 2;
  // is sent success
  bool is_sent_success = 3;
  // error message
  string error_message = 4;
}

// fulfillment report send record
message FulfillmentReportSendRecord {
  // report id
  int64 report_id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // send method
  SendMethod send_method = 4;
  // service date
  // (-- api-linter: core::0142::time-field-type=disabled --)
  string service_date = 5;
  // care type
  CareType care_type = 6;
  // report status
  ReportStatus report_status = 7;
  // send content
  FulfillmentReportContent send_content = 8;
  // send time
  google.protobuf.Timestamp send_time = 9;
  // is sent success
  bool is_sent_success = 10;
  // error message
  string error_message = 11;
  // uuid
  string uuid = 12;
}

// question category
enum QuestionCategory {
  // unspecified value
  QUESTION_CATEGORY_UNSPECIFIED = 0;
  // feedback
  FEEDBACK = 1;
  // pet condition
  PET_CONDITION = 2;
  // customize feedback
  CUSTOMIZE_FEEDBACK = 3;
}

// question type
enum QuestionType {
  // unspecified value
  QUESTION_TYPE_UNSPECIFIED = 0;
  // single_choice
  SINGLE_CHOICE = 1;
  // multi_choice
  MULTI_CHOICE = 2;
  // text_input( long_text_input )
  TEXT_INPUT = 3;
  // body_view
  BODY_VIEW = 4;
  // short_text_input
  SHORT_TEXT_INPUT = 5;
  // tag_choice
  TAG_CHOICE = 6;
}

// send method
enum SendMethod {
  // unspecified value
  SEND_METHOD_UNSPECIFIED = 0;
  // sms
  SMS = 1;
  // email
  EMAIL = 2;
}

// next appointment date format type
enum NextAppointmentDateFormatType {
  // unspecified value
  NEXT_APPOINTMENT_DATE_FORMAT_TYPE_UNSPECIFIED = 0;
  // only date
  ONLY_DATE = 1;
  // date and time
  DATE_AND_TIME = 2;
}

// report status
// (-- api-linter: core::0216::synonyms=disabled --)
enum ReportStatus {
  // unspecified value
  REPORT_STATUS_UNSPECIFIED = 0;
  // created
  REPORT_STATUS_CREATED = 1;
  // draft
  REPORT_STATUS_DRAFT = 2;
  // ready
  REPORT_STATUS_READY = 3;
  // sent
  REPORT_STATUS_SENT = 4;
}
