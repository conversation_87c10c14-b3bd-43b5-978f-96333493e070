syntax = "proto3";

package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";
import "backend/proto/fulfillment/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// 预约过滤器
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
message AppointmentFilter {
  // 状态列表
  repeated AppointmentState statuses = 1;
}

// 创建预约定义
message CreateAppointmentDef {
  // 商家ID
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 公司ID
  int64 company_id = 3 [(buf.validate.field).int64.gt = 0];
  // 客户ID
  int64 customer_id = 4;
  // 预约状态+1
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: 状态字段需要可写 --)
  AppointmentState status = 5;
  // 颜色代码
  string color_code = 7;
  // 开始时间
  google.protobuf.Timestamp start_time = 8;
  // 结束时间
  google.protobuf.Timestamp end_time = 9;
  // 宠物详情列表
  repeated CreatePetDetailDef pets = 10;
}

// 创建宠物详情定义
message CreatePetDetailDef {
  // 宠物ID
  int64 pet_id = 1;
  // 服务实例列表
  repeated CreateServiceInstanceDef services = 2;
}

// 创建服务实例定义
message CreateServiceInstanceDef {
  // 服务模板ID
  int64 service_id = 1;
  // 服务开始时间
  google.protobuf.Timestamp start_time = 2;
  // 服务结束时间
  google.protobuf.Timestamp end_time = 3;
  // 日期类型
  DateType date_type = 4;
  // 子服务实例列表
  repeated CreateServiceInstanceDef sub_service_instances = 5;
  // 服务费用列表
  repeated ServiceCharge charges = 6;
  // 备注列表
  repeated Note notes = 7;
  // 喂养用药列表
  repeated FeedingMedication feeding_medications = 8;
  // 服务细则（包含所有执行细节）
  optional ServiceDetail service_detail = 9;
}

// 预约单核心数据
message Appointment {
  // 预约ID
  int64 id = 1;
  // 商家ID
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 公司ID
  int64 company_id = 3 [(buf.validate.field).int64.gt = 0];
  // 客户ID
  int64 customer_id = 4;
  // 预约状态
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: 状态字段需要可写 --)
  AppointmentState status = 5;
  // 服务位图
  int32 service_item_type = 6;
  // 颜色代码
  string color_code = 7;
  // 开始时间
  google.protobuf.Timestamp start_time = 8;
  // 结束时间
  google.protobuf.Timestamp end_time = 9;
  // 宠物详情列表
  repeated PetDetail pets = 10;
}

// 宠物信息
message PetDetail {
  // 宠物ID
  int64 pet_id = 1;
  // 服务实例列表
  repeated ServiceInstanceImpl services = 2;
}

// 服务实例
message ServiceInstanceImpl {
  // 服务实例ID
  int64 service_instance_id = 1;
  // 服务模板ID
  int64 service_id = 2;
  // 服务开始时间
  google.protobuf.Timestamp start_time = 3;
  // 服务结束时间
  google.protobuf.Timestamp end_time = 4;
  // 日期类型
  DateType date_type = 5;
  // 子服务实例列表
  repeated ServiceInstanceImpl sub_service_instances = 6;
  // 服务费用列表
  repeated ServiceCharge charges = 7;
  // 备注列表
  repeated Note notes = 8;
  // 喂养用药列表
  repeated FeedingMedication feeding_medications = 9;
  // 服务细则（包含所有执行细节）
  optional ServiceDetail service_detail = 10;
}

// 服务细则
message ServiceDetail{
  // 员工细则列表
  repeated StaffDetail staff_details = 1;
}

// 员工细则 
message StaffDetail {
    // 员工ID
    int64 staff_id = 1;
    // 员工工作开始时间
    google.protobuf.Timestamp start_time = 2;
    // 员工工作结束时间
    google.protobuf.Timestamp end_time = 3;
    // 分配原因
    optional string assignment_reason = 4;
}
  
// 服务费用
message ServiceCharge {
  // 费用名称
  string name = 1;
  // 费用金额
  double amount = 2;
  // 费用描述
  string description = 3;
  // 费用类型
  int32 charge_type = 4;
}

// 备注
message Note {
  // 备注类型
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 type = 1; // 1-alert note,2-ticket comment,3-additional note
  // 备注内容
  string content = 2;
  // 员工ID
  int64 staff_id = 3;
}

// 喂养用药
message FeedingMedication {
  // 喂养规则
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string feeding_rule = 1;
  // 用药规则
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string medication_rule = 2;
  // 用药计划列表
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  repeated MedicationSchedule medications = 3;
}

// 用药计划
message MedicationSchedule {
  // 药品名称
  // (-- api-linter: core::0122::name-suffix=disabled
  //     aip.dev/not-precedent: medication is clear and appropriate --)
  string medication = 1;
  // 剂量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string dosage = 2;
  // 频率(如"每日两次")
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string frequency = 3;
  // 给药方式(如"口服")
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string administration_method = 4;
  // 注意事项
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string notes = 5;
}

// 服务选项
message ServiceOption {
  // 服务选项模板ID
  int64 service_option_template_id = 1;
  // 开始时间
  google.protobuf.Timestamp start_time = 2;
  // 结束时间
  google.protobuf.Timestamp end_time = 3;
  // 选项名称
  string name = 4;
  // 数量
  int32 quantity = 5;
  // 价格
  double price = 6;
  // 税费
  double tax = 7;
  // 每天执行数量
  int64 quantity_per_day = 8;
}

// 元数据
message Metadata{
  // 标签映射
  map<string, string> tags = 1;
}

// 预约单操作结果
message AppointmentOperationResult {
  // 操作类型
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string operation_type = 1; // "start_time", "end_time", "color_code", "status"
  // 操作是否成功
  bool success = 2;
  // 错误消息
  string error_message = 3;

  // 操作前的值（如果更新失败，用于回滚）
  optional google.protobuf.Timestamp old_start_time = 4;
  // 操作前的结束时间
  optional google.protobuf.Timestamp old_end_time = 5;
  // 操作前的颜色代码
  optional string old_color_code = 6;
  // 操作前的状态
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: 状态字段需要可写 --)
  optional AppointmentState old_status = 7;

  // 操作后的值
  optional google.protobuf.Timestamp new_start_time = 8;
  // 操作后的结束时间
  optional google.protobuf.Timestamp new_end_time = 9;
  // 操作后的颜色代码
  optional string new_color_code = 10;
  // 操作后的状态
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: 状态字段需要可写 --)
  optional AppointmentState new_status = 11;
}

// 宠物操作结果
message PetOperationResult {
  // 宠物ID
  int64 pet_id = 1;
  // 操作模式
  OperationMode operation_mode = 2;
  // 操作是否成功
  bool success = 3;
  // 错误消息
  string error_message = 4;

  // 操作详情
  oneof operation_detail {
    // 删除结果
    PetDeleteResult delete_result = 5;
    // 创建结果
    PetCreateResult create_result = 6;
    // 更新结果
    PetUpdateResult update_result = 7;
  }
}

// 宠物删除结果
message PetDeleteResult {
  // 是否删除成功
  bool deleted = 1;
  // 删除的服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 deleted_service_count = 2;
}

// 宠物创建结果
message PetCreateResult {
  // 新宠物ID
  int64 new_pet_id = 1;
  // 创建的服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 created_service_count = 2;
}

// 宠物更新结果
message PetUpdateResult {
  // 更新后的宠物
  PetDetail updated_pet = 1;
  // 更新的服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 updated_service_count = 2;
}

// 服务操作结果
message ServiceOperationResult {
  // 服务实例ID
  int64 service_instance_id = 1;
  // 宠物ID
  int64 pet_id = 2;
  // 操作模式
  OperationMode operation_mode = 3;
  // 操作是否成功
  bool success = 4;
  // 错误消息
  string error_message = 5;

  // 操作详情
  oneof operation_detail {
    // 删除结果
    ServiceDeleteResult delete_result = 6;
    // 创建结果
    ServiceCreateResult create_result = 7;
    // 更新结果
    ServiceUpdateResult update_result = 8;
  }
}

// 服务删除结果
message ServiceDeleteResult {
  // 是否删除成功
  bool deleted = 1;
  // 删除的附加服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 deleted_option_count = 2;
}

// 服务创建结果
message ServiceCreateResult {
  // 新服务实例ID
  int64 new_service_instance_id = 1;
  // 创建的附加服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 created_option_count = 2;
}

// 服务更新结果
message ServiceUpdateResult {
  // 更新后的服务
  ServiceInstanceImpl updated_service = 1;
  // 更新的附加服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 updated_option_count = 2;
}

// 附加服务操作结果
message OptionOperationResult {
  // 服务选项ID
  int64 service_option_id = 1;
  // 服务实例ID
  int64 service_instance_id = 2;
  // 操作模式
  OperationMode operation_mode = 3;
  // 操作是否成功
  bool success = 4;
  // 错误消息
  string error_message = 5;

  // 操作详情
  oneof operation_detail {
    // 删除结果
    OptionDeleteResult delete_result = 6;
    // 创建结果
    OptionCreateResult create_result = 7;
    // 更新结果
    OptionUpdateResult update_result = 8;
  }
}

// 选项删除结果
message OptionDeleteResult {
  // 是否删除成功
  bool deleted = 1;
}

// 选项创建结果
message OptionCreateResult {
  // 新服务选项ID
  int64 new_service_option_id = 1;
}

// 选项更新结果
message OptionUpdateResult {
  // 更新后的选项
  ServiceOption updated_option = 1;
}

// 重复类型
enum RepeatType {
  // 未指定重复类型
  REPEAT_TYPE_UNSPECIFIED = 0;
  // 每日重复
  REPEAT_TYPE_DAILY = 1;
  // 每周重复
  REPEAT_TYPE_WEEKLY = 2;
  // 每月重复
  REPEAT_TYPE_MONTHLY = 3;
}
// 预约单状态枚举
// (-- api-linter: core::0216::nesting=disabled
//     aip.dev/not-precedent: AppointmentState is appropriate as top-level enum --)
enum AppointmentState {
  // 未指定状态
  APPOINTMENT_STATE_UNSPECIFIED = 0;
  // 未确认
  APPOINTMENT_STATE_UNCONFIRMED = 1;
  // 已确认
  APPOINTMENT_STATE_CONFIRMED = 2;
  // 已完成(check out)
  APPOINTMENT_STATE_FINISHED = 3;
  // 已取消
  APPOINTMENT_STATE_CANCELED = 4;
  // 准备就绪
  APPOINTMENT_STATE_READY = 5;
  // 已入住
  APPOINTMENT_STATE_CHECKED_IN = 6;
}

// 操作模式枚举
enum OperationMode {
  // 未指定操作模式
  OPERATION_MODE_UNSPECIFIED = 0;
  // 创建
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  OPERATION_MODE_CREATE = 1;
  // 更新
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  OPERATION_MODE_UPDATE = 2;
  // 删除
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  OPERATION_MODE_DELETE = 3;
}
  