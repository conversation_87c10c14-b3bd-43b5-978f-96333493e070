// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/appointment_service.proto

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 根据ID列表获取预约请求
type GetAppointmentByIDsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 预约ID列表
	AppointmentIds []int64 `protobuf:"varint,1,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetAppointmentByIDsRequest) Reset() {
	*x = GetAppointmentByIDsRequest{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAppointmentByIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentByIDsRequest) ProtoMessage() {}

func (x *GetAppointmentByIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentByIDsRequest.ProtoReflect.Descriptor instead.
func (*GetAppointmentByIDsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetAppointmentByIDsRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

// 根据ID列表获取预约响应
type GetAppointmentByIDsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 预约列表
	Appointments  []*Appointment `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAppointmentByIDsResponse) Reset() {
	*x = GetAppointmentByIDsResponse{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAppointmentByIDsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentByIDsResponse) ProtoMessage() {}

func (x *GetAppointmentByIDsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentByIDsResponse.ProtoReflect.Descriptor instead.
func (*GetAppointmentByIDsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAppointmentByIDsResponse) GetAppointments() []*Appointment {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// 列出预约请求
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: company_id is used as parent in this context --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要page_token --)
//
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 不需要page_size，使用现有的分页机制 --)
type ListAppointmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 查询开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// 查询结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// 过滤条件
	// (-- api-linter: core::0132::request-field-types=disabled
	//
	//	aip.dev/not-precedent: 打平成string不利用协议理解 --)
	Filter *AppointmentFilter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,6,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAppointmentRequest) Reset() {
	*x = ListAppointmentRequest{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentRequest) ProtoMessage() {}

func (x *ListAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentRequest.ProtoReflect.Descriptor instead.
func (*ListAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListAppointmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListAppointmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListAppointmentRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ListAppointmentRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ListAppointmentRequest) GetFilter() *AppointmentFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAppointmentRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 列出预约响应
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要next_page_token字段，使用现有的分页机制 --)
type ListAppointmentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 履约列表
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Appointments []*Appointment `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// 分页信息
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// 是否最后一页
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	IsEnd bool `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	// 总条数
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Total         int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAppointmentResponse) Reset() {
	*x = ListAppointmentResponse{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentResponse) ProtoMessage() {}

func (x *ListAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentResponse.ProtoReflect.Descriptor instead.
func (*ListAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListAppointmentResponse) GetAppointments() []*Appointment {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListAppointmentResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAppointmentResponse) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *ListAppointmentResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 创建预约请求
// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: 不需要Appointment字段，直接使用各个字段 --)
type CreateAppointmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 订单开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 订单结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 宠物详情列表
	Pets []*CreatePetDetailDef `protobuf:"bytes,6,rep,name=pets,proto3" json:"pets,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 来源:1-onlinebooking 2-staff
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Source        int32 `protobuf:"varint,8,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAppointmentRequest) Reset() {
	*x = CreateAppointmentRequest{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentRequest) ProtoMessage() {}

func (x *CreateAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentRequest.ProtoReflect.Descriptor instead.
func (*CreateAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateAppointmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateAppointmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateAppointmentRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateAppointmentRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *CreateAppointmentRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *CreateAppointmentRequest) GetPets() []*CreatePetDetailDef {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *CreateAppointmentRequest) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *CreateAppointmentRequest) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

// 创建预约响应
type CreateAppointmentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 预约ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAppointmentResponse) Reset() {
	*x = CreateAppointmentResponse{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentResponse) ProtoMessage() {}

func (x *CreateAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentResponse.ProtoReflect.Descriptor instead.
func (*CreateAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateAppointmentResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 更新预约请求
type UpdateAppointmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 必须字段
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// 预约操作
	AppointmentOperation *UpdateAppointmentRequest_AppointmentOperation `protobuf:"bytes,2,opt,name=appointment_operation,json=appointmentOperation,proto3,oneof" json:"appointment_operation,omitempty"`
	// 服务操作列表
	ServiceOperations []*UpdateAppointmentRequest_ServiceOperation `protobuf:"bytes,3,rep,name=service_operations,json=serviceOperations,proto3" json:"service_operations,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateAppointmentRequest) Reset() {
	*x = UpdateAppointmentRequest{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest) ProtoMessage() {}

func (x *UpdateAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateAppointmentRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateAppointmentRequest) GetAppointmentOperation() *UpdateAppointmentRequest_AppointmentOperation {
	if x != nil {
		return x.AppointmentOperation
	}
	return nil
}

func (x *UpdateAppointmentRequest) GetServiceOperations() []*UpdateAppointmentRequest_ServiceOperation {
	if x != nil {
		return x.ServiceOperations
	}
	return nil
}

// 更新预约响应
type UpdateAppointmentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 整体更新结果
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// 更新消息
	Message       string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAppointmentResponse) Reset() {
	*x = UpdateAppointmentResponse{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentResponse) ProtoMessage() {}

func (x *UpdateAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentResponse.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateAppointmentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateAppointmentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 订单层面信息更新
type UpdateAppointmentRequest_AppointmentOperation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// 颜色代码
	ColorCode *string `protobuf:"bytes,3,opt,name=color_code,json=colorCode,proto3,oneof" json:"color_code,omitempty"`
	// 新状态
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: 状态字段需要可写 --)
	NewStatus     *AppointmentState `protobuf:"varint,4,opt,name=new_status,json=newStatus,proto3,enum=backend.proto.fulfillment.v1.AppointmentState,oneof" json:"new_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAppointmentRequest_AppointmentOperation) Reset() {
	*x = UpdateAppointmentRequest_AppointmentOperation{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAppointmentRequest_AppointmentOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest_AppointmentOperation) ProtoMessage() {}

func (x *UpdateAppointmentRequest_AppointmentOperation) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest_AppointmentOperation.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest_AppointmentOperation) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *UpdateAppointmentRequest_AppointmentOperation) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *UpdateAppointmentRequest_AppointmentOperation) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *UpdateAppointmentRequest_AppointmentOperation) GetColorCode() string {
	if x != nil && x.ColorCode != nil {
		return *x.ColorCode
	}
	return ""
}

func (x *UpdateAppointmentRequest_AppointmentOperation) GetNewStatus() AppointmentState {
	if x != nil && x.NewStatus != nil {
		return *x.NewStatus
	}
	return AppointmentState_APPOINTMENT_STATE_UNSPECIFIED
}

// 服务维度操作
type UpdateAppointmentRequest_ServiceOperation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作模式
	OperationMode OperationMode `protobuf:"varint,1,opt,name=operation_mode,json=operationMode,proto3,enum=backend.proto.fulfillment.v1.OperationMode" json:"operation_mode,omitempty"`
	// 服务实例ID，operation_mode为REPLACE 或 DELETE 必填
	ServiceInstanceId *int64 `protobuf:"varint,2,opt,name=service_instance_id,json=serviceInstanceId,proto3,oneof" json:"service_instance_id,omitempty"`
	// 服务模板ID，operation_mode为NEW要填
	ServiceId *int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3,oneof" json:"service_id,omitempty"`
	// 父服务实例ID，operation_mode为NEW，并且是sub_service_instance要填
	ParentServiceInstanceId *int64 `protobuf:"varint,4,opt,name=parent_service_instance_id,json=parentServiceInstanceId,proto3,oneof" json:"parent_service_instance_id,omitempty"`
	// 日期类型
	DateType *DateType `protobuf:"varint,5,opt,name=date_type,json=dateType,proto3,enum=backend.proto.fulfillment.v1.DateType,oneof" json:"date_type,omitempty"`
	// 服务开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// 服务结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// 子服务实例列表
	SubServiceInstances []*UpdateAppointmentRequest_ServiceOperation `protobuf:"bytes,8,rep,name=sub_service_instances,json=subServiceInstances,proto3" json:"sub_service_instances,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *UpdateAppointmentRequest_ServiceOperation) Reset() {
	*x = UpdateAppointmentRequest_ServiceOperation{}
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAppointmentRequest_ServiceOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest_ServiceOperation) ProtoMessage() {}

func (x *UpdateAppointmentRequest_ServiceOperation) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest_ServiceOperation.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest_ServiceOperation) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP(), []int{6, 1}
}

func (x *UpdateAppointmentRequest_ServiceOperation) GetOperationMode() OperationMode {
	if x != nil {
		return x.OperationMode
	}
	return OperationMode_OPERATION_MODE_UNSPECIFIED
}

func (x *UpdateAppointmentRequest_ServiceOperation) GetServiceInstanceId() int64 {
	if x != nil && x.ServiceInstanceId != nil {
		return *x.ServiceInstanceId
	}
	return 0
}

func (x *UpdateAppointmentRequest_ServiceOperation) GetServiceId() int64 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *UpdateAppointmentRequest_ServiceOperation) GetParentServiceInstanceId() int64 {
	if x != nil && x.ParentServiceInstanceId != nil {
		return *x.ParentServiceInstanceId
	}
	return 0
}

func (x *UpdateAppointmentRequest_ServiceOperation) GetDateType() DateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return DateType_DATE_TYPE_UNSPECIFIED
}

func (x *UpdateAppointmentRequest_ServiceOperation) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *UpdateAppointmentRequest_ServiceOperation) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *UpdateAppointmentRequest_ServiceOperation) GetSubServiceInstances() []*UpdateAppointmentRequest_ServiceOperation {
	if x != nil {
		return x.SubServiceInstances
	}
	return nil
}

var File_backend_proto_fulfillment_v1_appointment_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_appointment_service_proto_rawDesc = "" +
	"\n" +
	"6backend/proto/fulfillment/v1/appointment_service.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a.backend/proto/fulfillment/v1/appointment.proto\"E\n" +
	"\x1aGetAppointmentByIDsRequest\x12'\n" +
	"\x0fappointment_ids\x18\x01 \x03(\x03R\x0eappointmentIds\"l\n" +
	"\x1bGetAppointmentByIDsResponse\x12M\n" +
	"\fappointments\x18\x01 \x03(\v2).backend.proto.fulfillment.v1.AppointmentR\fappointments\"\xbc\x03\n" +
	"\x16ListAppointmentRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12>\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\tstartTime\x88\x01\x01\x12:\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\aendTime\x88\x01\x01\x12L\n" +
	"\x06filter\x18\x05 \x01(\v2/.backend.proto.fulfillment.v1.AppointmentFilterH\x02R\x06filter\x88\x01\x01\x12P\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefH\x03R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_start_timeB\v\n" +
	"\t_end_timeB\t\n" +
	"\a_filterB\r\n" +
	"\v_pagination\"\xe2\x01\n" +
	"\x17ListAppointmentResponse\x12M\n" +
	"\fappointments\x18\x01 \x03(\v2).backend.proto.fulfillment.v1.AppointmentR\fappointments\x12K\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\x12\x15\n" +
	"\x06is_end\x18\x03 \x01(\bR\x05isEnd\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x05R\x05total\"\xea\x02\n" +
	"\x18CreateAppointmentRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\x03R\n" +
	"businessId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vcustomer_id\x18\x03 \x01(\x03R\n" +
	"customerId\x129\n" +
	"\n" +
	"start_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12D\n" +
	"\x04pets\x18\x06 \x03(\v20.backend.proto.fulfillment.v1.CreatePetDetailDefR\x04pets\x12\x1d\n" +
	"\n" +
	"color_code\x18\a \x01(\tR\tcolorCode\x12\x16\n" +
	"\x06source\x18\b \x01(\x05R\x06source\"+\n" +
	"\x19CreateAppointmentResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xe2\n" +
	"\n" +
	"\x18UpdateAppointmentRequest\x12.\n" +
	"\x0eappointment_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\rappointmentId\x12\x85\x01\n" +
	"\x15appointment_operation\x18\x02 \x01(\v2K.backend.proto.fulfillment.v1.UpdateAppointmentRequest.AppointmentOperationH\x00R\x14appointmentOperation\x88\x01\x01\x12v\n" +
	"\x12service_operations\x18\x03 \x03(\v2G.backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperationR\x11serviceOperations\x1a\xc4\x02\n" +
	"\x14AppointmentOperation\x12>\n" +
	"\n" +
	"start_time\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\tstartTime\x88\x01\x01\x12:\n" +
	"\bend_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\aendTime\x88\x01\x01\x12\"\n" +
	"\n" +
	"color_code\x18\x03 \x01(\tH\x02R\tcolorCode\x88\x01\x01\x12R\n" +
	"\n" +
	"new_status\x18\x04 \x01(\x0e2..backend.proto.fulfillment.v1.AppointmentStateH\x03R\tnewStatus\x88\x01\x01B\r\n" +
	"\v_start_timeB\v\n" +
	"\t_end_timeB\r\n" +
	"\v_color_codeB\r\n" +
	"\v_new_status\x1a\xb4\x05\n" +
	"\x10ServiceOperation\x12R\n" +
	"\x0eoperation_mode\x18\x01 \x01(\x0e2+.backend.proto.fulfillment.v1.OperationModeR\roperationMode\x123\n" +
	"\x13service_instance_id\x18\x02 \x01(\x03H\x00R\x11serviceInstanceId\x88\x01\x01\x12\"\n" +
	"\n" +
	"service_id\x18\x03 \x01(\x03H\x01R\tserviceId\x88\x01\x01\x12@\n" +
	"\x1aparent_service_instance_id\x18\x04 \x01(\x03H\x02R\x17parentServiceInstanceId\x88\x01\x01\x12H\n" +
	"\tdate_type\x18\x05 \x01(\x0e2&.backend.proto.fulfillment.v1.DateTypeH\x03R\bdateType\x88\x01\x01\x12>\n" +
	"\n" +
	"start_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampH\x04R\tstartTime\x88\x01\x01\x12:\n" +
	"\bend_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampH\x05R\aendTime\x88\x01\x01\x12{\n" +
	"\x15sub_service_instances\x18\b \x03(\v2G.backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperationR\x13subServiceInstancesB\x16\n" +
	"\x14_service_instance_idB\r\n" +
	"\v_service_idB\x1d\n" +
	"\x1b_parent_service_instance_idB\f\n" +
	"\n" +
	"_date_typeB\r\n" +
	"\v_start_timeB\v\n" +
	"\t_end_timeB\x18\n" +
	"\x16_appointment_operation\"O\n" +
	"\x19UpdateAppointmentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xaf\x04\n" +
	"\x12AppointmentService\x12\x84\x01\n" +
	"\x11CreateAppointment\x126.backend.proto.fulfillment.v1.CreateAppointmentRequest\x1a7.backend.proto.fulfillment.v1.CreateAppointmentResponse\x12~\n" +
	"\x0fListAppointment\x124.backend.proto.fulfillment.v1.ListAppointmentRequest\x1a5.backend.proto.fulfillment.v1.ListAppointmentResponse\x12\x84\x01\n" +
	"\x11UpdateAppointment\x126.backend.proto.fulfillment.v1.UpdateAppointmentRequest\x1a7.backend.proto.fulfillment.v1.UpdateAppointmentResponse\x12\x8a\x01\n" +
	"\x13GetAppointmentByIDs\x128.backend.proto.fulfillment.v1.GetAppointmentByIDsRequest\x1a9.backend.proto.fulfillment.v1.GetAppointmentByIDsResponseBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_appointment_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_appointment_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_appointment_service_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_backend_proto_fulfillment_v1_appointment_service_proto_goTypes = []any{
	(*GetAppointmentByIDsRequest)(nil),                    // 0: backend.proto.fulfillment.v1.GetAppointmentByIDsRequest
	(*GetAppointmentByIDsResponse)(nil),                   // 1: backend.proto.fulfillment.v1.GetAppointmentByIDsResponse
	(*ListAppointmentRequest)(nil),                        // 2: backend.proto.fulfillment.v1.ListAppointmentRequest
	(*ListAppointmentResponse)(nil),                       // 3: backend.proto.fulfillment.v1.ListAppointmentResponse
	(*CreateAppointmentRequest)(nil),                      // 4: backend.proto.fulfillment.v1.CreateAppointmentRequest
	(*CreateAppointmentResponse)(nil),                     // 5: backend.proto.fulfillment.v1.CreateAppointmentResponse
	(*UpdateAppointmentRequest)(nil),                      // 6: backend.proto.fulfillment.v1.UpdateAppointmentRequest
	(*UpdateAppointmentResponse)(nil),                     // 7: backend.proto.fulfillment.v1.UpdateAppointmentResponse
	(*UpdateAppointmentRequest_AppointmentOperation)(nil), // 8: backend.proto.fulfillment.v1.UpdateAppointmentRequest.AppointmentOperation
	(*UpdateAppointmentRequest_ServiceOperation)(nil),     // 9: backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperation
	(*Appointment)(nil),                                   // 10: backend.proto.fulfillment.v1.Appointment
	(*timestamppb.Timestamp)(nil),                         // 11: google.protobuf.Timestamp
	(*AppointmentFilter)(nil),                             // 12: backend.proto.fulfillment.v1.AppointmentFilter
	(*PaginationRef)(nil),                                 // 13: backend.proto.fulfillment.v1.PaginationRef
	(*CreatePetDetailDef)(nil),                            // 14: backend.proto.fulfillment.v1.CreatePetDetailDef
	(AppointmentState)(0),                                 // 15: backend.proto.fulfillment.v1.AppointmentState
	(OperationMode)(0),                                    // 16: backend.proto.fulfillment.v1.OperationMode
	(DateType)(0),                                         // 17: backend.proto.fulfillment.v1.DateType
}
var file_backend_proto_fulfillment_v1_appointment_service_proto_depIdxs = []int32{
	10, // 0: backend.proto.fulfillment.v1.GetAppointmentByIDsResponse.appointments:type_name -> backend.proto.fulfillment.v1.Appointment
	11, // 1: backend.proto.fulfillment.v1.ListAppointmentRequest.start_time:type_name -> google.protobuf.Timestamp
	11, // 2: backend.proto.fulfillment.v1.ListAppointmentRequest.end_time:type_name -> google.protobuf.Timestamp
	12, // 3: backend.proto.fulfillment.v1.ListAppointmentRequest.filter:type_name -> backend.proto.fulfillment.v1.AppointmentFilter
	13, // 4: backend.proto.fulfillment.v1.ListAppointmentRequest.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	10, // 5: backend.proto.fulfillment.v1.ListAppointmentResponse.appointments:type_name -> backend.proto.fulfillment.v1.Appointment
	13, // 6: backend.proto.fulfillment.v1.ListAppointmentResponse.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	11, // 7: backend.proto.fulfillment.v1.CreateAppointmentRequest.start_time:type_name -> google.protobuf.Timestamp
	11, // 8: backend.proto.fulfillment.v1.CreateAppointmentRequest.end_time:type_name -> google.protobuf.Timestamp
	14, // 9: backend.proto.fulfillment.v1.CreateAppointmentRequest.pets:type_name -> backend.proto.fulfillment.v1.CreatePetDetailDef
	8,  // 10: backend.proto.fulfillment.v1.UpdateAppointmentRequest.appointment_operation:type_name -> backend.proto.fulfillment.v1.UpdateAppointmentRequest.AppointmentOperation
	9,  // 11: backend.proto.fulfillment.v1.UpdateAppointmentRequest.service_operations:type_name -> backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperation
	11, // 12: backend.proto.fulfillment.v1.UpdateAppointmentRequest.AppointmentOperation.start_time:type_name -> google.protobuf.Timestamp
	11, // 13: backend.proto.fulfillment.v1.UpdateAppointmentRequest.AppointmentOperation.end_time:type_name -> google.protobuf.Timestamp
	15, // 14: backend.proto.fulfillment.v1.UpdateAppointmentRequest.AppointmentOperation.new_status:type_name -> backend.proto.fulfillment.v1.AppointmentState
	16, // 15: backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperation.operation_mode:type_name -> backend.proto.fulfillment.v1.OperationMode
	17, // 16: backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperation.date_type:type_name -> backend.proto.fulfillment.v1.DateType
	11, // 17: backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperation.start_time:type_name -> google.protobuf.Timestamp
	11, // 18: backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperation.end_time:type_name -> google.protobuf.Timestamp
	9,  // 19: backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperation.sub_service_instances:type_name -> backend.proto.fulfillment.v1.UpdateAppointmentRequest.ServiceOperation
	4,  // 20: backend.proto.fulfillment.v1.AppointmentService.CreateAppointment:input_type -> backend.proto.fulfillment.v1.CreateAppointmentRequest
	2,  // 21: backend.proto.fulfillment.v1.AppointmentService.ListAppointment:input_type -> backend.proto.fulfillment.v1.ListAppointmentRequest
	6,  // 22: backend.proto.fulfillment.v1.AppointmentService.UpdateAppointment:input_type -> backend.proto.fulfillment.v1.UpdateAppointmentRequest
	0,  // 23: backend.proto.fulfillment.v1.AppointmentService.GetAppointmentByIDs:input_type -> backend.proto.fulfillment.v1.GetAppointmentByIDsRequest
	5,  // 24: backend.proto.fulfillment.v1.AppointmentService.CreateAppointment:output_type -> backend.proto.fulfillment.v1.CreateAppointmentResponse
	3,  // 25: backend.proto.fulfillment.v1.AppointmentService.ListAppointment:output_type -> backend.proto.fulfillment.v1.ListAppointmentResponse
	7,  // 26: backend.proto.fulfillment.v1.AppointmentService.UpdateAppointment:output_type -> backend.proto.fulfillment.v1.UpdateAppointmentResponse
	1,  // 27: backend.proto.fulfillment.v1.AppointmentService.GetAppointmentByIDs:output_type -> backend.proto.fulfillment.v1.GetAppointmentByIDsResponse
	24, // [24:28] is the sub-list for method output_type
	20, // [20:24] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_appointment_service_proto_init() }
func file_backend_proto_fulfillment_v1_appointment_service_proto_init() {
	if File_backend_proto_fulfillment_v1_appointment_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_common_proto_init()
	file_backend_proto_fulfillment_v1_appointment_proto_init()
	file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[6].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[8].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes[9].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_appointment_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_appointment_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_appointment_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_appointment_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_appointment_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_appointment_service_proto = out.File
	file_backend_proto_fulfillment_v1_appointment_service_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_appointment_service_proto_depIdxs = nil
}
