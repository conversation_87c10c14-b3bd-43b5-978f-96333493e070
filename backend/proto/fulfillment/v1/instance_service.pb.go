// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/instance_service.proto

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取服务实例请求
type GetServiceInstanceByIDsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务实例ID列表
	ServiceInstanceIds []int64 `protobuf:"varint,1,rep,packed,name=service_instance_ids,json=serviceInstanceIds,proto3" json:"service_instance_ids,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetServiceInstanceByIDsRequest) Reset() {
	*x = GetServiceInstanceByIDsRequest{}
	mi := &file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInstanceByIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInstanceByIDsRequest) ProtoMessage() {}

func (x *GetServiceInstanceByIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInstanceByIDsRequest.ProtoReflect.Descriptor instead.
func (*GetServiceInstanceByIDsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_instance_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetServiceInstanceByIDsRequest) GetServiceInstanceIds() []int64 {
	if x != nil {
		return x.ServiceInstanceIds
	}
	return nil
}

// 获取服务实例响应
type GetServiceInstanceByIDsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务实例列表
	ServiceInstances []*ServiceInstance `protobuf:"bytes,1,rep,name=service_instances,json=serviceInstances,proto3" json:"service_instances,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetServiceInstanceByIDsResponse) Reset() {
	*x = GetServiceInstanceByIDsResponse{}
	mi := &file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInstanceByIDsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInstanceByIDsResponse) ProtoMessage() {}

func (x *GetServiceInstanceByIDsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInstanceByIDsResponse.ProtoReflect.Descriptor instead.
func (*GetServiceInstanceByIDsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_instance_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetServiceInstanceByIDsResponse) GetServiceInstances() []*ServiceInstance {
	if x != nil {
		return x.ServiceInstances
	}
	return nil
}

// 列出服务实例请求
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: company_id is used as parent in this context --)
//
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 不需要page_size，使用现有的分页机制 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要page_token，使用现有的分页机制 --)
type ListServiceInstanceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 查询开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 查询结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 过滤条件
	// (-- api-linter: core::0132::request-field-types=disabled
	//
	//	aip.dev/not-precedent: 打平成string不利用协议理解 --)
	Filter *ServiceInstanceFilter `protobuf:"bytes,5,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序类型
	SortType SortType `protobuf:"varint,6,opt,name=sort_type,json=sortType,proto3,enum=backend.proto.fulfillment.v1.SortType" json:"sort_type,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServiceInstanceRequest) Reset() {
	*x = ListServiceInstanceRequest{}
	mi := &file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceInstanceRequest) ProtoMessage() {}

func (x *ListServiceInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceInstanceRequest.ProtoReflect.Descriptor instead.
func (*ListServiceInstanceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_instance_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListServiceInstanceRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListServiceInstanceRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListServiceInstanceRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ListServiceInstanceRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ListServiceInstanceRequest) GetFilter() *ServiceInstanceFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListServiceInstanceRequest) GetSortType() SortType {
	if x != nil {
		return x.SortType
	}
	return SortType_SORT_TYPE_UNSPECIFIED
}

func (x *ListServiceInstanceRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 列出服务实例响应
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要next_page_token字段，使用现有的分页机制 --)
type ListServiceInstanceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务实例列表
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	ServiceInstances []*ServiceInstance `protobuf:"bytes,1,rep,name=service_instances,json=serviceInstances,proto3" json:"service_instances,omitempty"`
	// 分页信息
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// 是否最后一页
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	IsEnd bool `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	// 总条数
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Total         int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServiceInstanceResponse) Reset() {
	*x = ListServiceInstanceResponse{}
	mi := &file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServiceInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceInstanceResponse) ProtoMessage() {}

func (x *ListServiceInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceInstanceResponse.ProtoReflect.Descriptor instead.
func (*ListServiceInstanceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_instance_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListServiceInstanceResponse) GetServiceInstances() []*ServiceInstance {
	if x != nil {
		return x.ServiceInstances
	}
	return nil
}

func (x *ListServiceInstanceResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceInstanceResponse) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *ListServiceInstanceResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_backend_proto_fulfillment_v1_instance_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_instance_service_proto_rawDesc = "" +
	"\n" +
	"3backend/proto/fulfillment/v1/instance_service.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a+backend/proto/fulfillment/v1/instance.proto\x1a)backend/proto/fulfillment/v1/common.proto\"R\n" +
	"\x1eGetServiceInstanceByIDsRequest\x120\n" +
	"\x14service_instance_ids\x18\x01 \x03(\x03R\x12serviceInstanceIds\"}\n" +
	"\x1fGetServiceInstanceByIDsResponse\x12Z\n" +
	"\x11service_instances\x18\x01 \x03(\v2-.backend.proto.fulfillment.v1.ServiceInstanceR\x10serviceInstances\"\xbf\x03\n" +
	"\x1aListServiceInstanceRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x129\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12K\n" +
	"\x06filter\x18\x05 \x01(\v23.backend.proto.fulfillment.v1.ServiceInstanceFilterR\x06filter\x12C\n" +
	"\tsort_type\x18\x06 \x01(\x0e2&.backend.proto.fulfillment.v1.SortTypeR\bsortType\x12K\n" +
	"\n" +
	"pagination\x18\a \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\"\xf3\x01\n" +
	"\x1bListServiceInstanceResponse\x12Z\n" +
	"\x11service_instances\x18\x01 \x03(\v2-.backend.proto.fulfillment.v1.ServiceInstanceR\x10serviceInstances\x12K\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\x12\x15\n" +
	"\x06is_end\x18\x03 \x01(\bR\x05isEnd\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x05R\x05total2\xb7\x02\n" +
	"\x0fInstanceService\x12\x8a\x01\n" +
	"\x13ListServiceInstance\x128.backend.proto.fulfillment.v1.ListServiceInstanceRequest\x1a9.backend.proto.fulfillment.v1.ListServiceInstanceResponse\x12\x96\x01\n" +
	"\x17GetServiceInstanceByIDs\x12<.backend.proto.fulfillment.v1.GetServiceInstanceByIDsRequest\x1a=.backend.proto.fulfillment.v1.GetServiceInstanceByIDsResponseBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_instance_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_instance_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_instance_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_instance_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_instance_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_instance_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_instance_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_instance_service_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_fulfillment_v1_instance_service_proto_goTypes = []any{
	(*GetServiceInstanceByIDsRequest)(nil),  // 0: backend.proto.fulfillment.v1.GetServiceInstanceByIDsRequest
	(*GetServiceInstanceByIDsResponse)(nil), // 1: backend.proto.fulfillment.v1.GetServiceInstanceByIDsResponse
	(*ListServiceInstanceRequest)(nil),      // 2: backend.proto.fulfillment.v1.ListServiceInstanceRequest
	(*ListServiceInstanceResponse)(nil),     // 3: backend.proto.fulfillment.v1.ListServiceInstanceResponse
	(*ServiceInstance)(nil),                 // 4: backend.proto.fulfillment.v1.ServiceInstance
	(*timestamppb.Timestamp)(nil),           // 5: google.protobuf.Timestamp
	(*ServiceInstanceFilter)(nil),           // 6: backend.proto.fulfillment.v1.ServiceInstanceFilter
	(SortType)(0),                           // 7: backend.proto.fulfillment.v1.SortType
	(*PaginationRef)(nil),                   // 8: backend.proto.fulfillment.v1.PaginationRef
}
var file_backend_proto_fulfillment_v1_instance_service_proto_depIdxs = []int32{
	4,  // 0: backend.proto.fulfillment.v1.GetServiceInstanceByIDsResponse.service_instances:type_name -> backend.proto.fulfillment.v1.ServiceInstance
	5,  // 1: backend.proto.fulfillment.v1.ListServiceInstanceRequest.start_time:type_name -> google.protobuf.Timestamp
	5,  // 2: backend.proto.fulfillment.v1.ListServiceInstanceRequest.end_time:type_name -> google.protobuf.Timestamp
	6,  // 3: backend.proto.fulfillment.v1.ListServiceInstanceRequest.filter:type_name -> backend.proto.fulfillment.v1.ServiceInstanceFilter
	7,  // 4: backend.proto.fulfillment.v1.ListServiceInstanceRequest.sort_type:type_name -> backend.proto.fulfillment.v1.SortType
	8,  // 5: backend.proto.fulfillment.v1.ListServiceInstanceRequest.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	4,  // 6: backend.proto.fulfillment.v1.ListServiceInstanceResponse.service_instances:type_name -> backend.proto.fulfillment.v1.ServiceInstance
	8,  // 7: backend.proto.fulfillment.v1.ListServiceInstanceResponse.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	2,  // 8: backend.proto.fulfillment.v1.InstanceService.ListServiceInstance:input_type -> backend.proto.fulfillment.v1.ListServiceInstanceRequest
	0,  // 9: backend.proto.fulfillment.v1.InstanceService.GetServiceInstanceByIDs:input_type -> backend.proto.fulfillment.v1.GetServiceInstanceByIDsRequest
	3,  // 10: backend.proto.fulfillment.v1.InstanceService.ListServiceInstance:output_type -> backend.proto.fulfillment.v1.ListServiceInstanceResponse
	1,  // 11: backend.proto.fulfillment.v1.InstanceService.GetServiceInstanceByIDs:output_type -> backend.proto.fulfillment.v1.GetServiceInstanceByIDsResponse
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_instance_service_proto_init() }
func file_backend_proto_fulfillment_v1_instance_service_proto_init() {
	if File_backend_proto_fulfillment_v1_instance_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_instance_proto_init()
	file_backend_proto_fulfillment_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_instance_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_instance_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_instance_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_instance_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_instance_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_instance_service_proto = out.File
	file_backend_proto_fulfillment_v1_instance_service_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_instance_service_proto_depIdxs = nil
}
