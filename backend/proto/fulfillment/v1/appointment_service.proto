syntax = "proto3";

package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";
import "backend/proto/fulfillment/v1/common.proto";
import "backend/proto/fulfillment/v1/appointment.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// 预约服务
service AppointmentService {
  // 创建预约
  // (-- api-linter: core::0133::response-message-name=disabled
  //     aip.dev/not-precedent: CreateAppointmentResponse is appropriate for this use case --)
  rpc CreateAppointment(CreateAppointmentRequest) returns (CreateAppointmentResponse);
  // 列出预约
  rpc ListAppointment(ListAppointmentRequest) returns (ListAppointmentResponse);
  // 更新预约
  // (-- api-linter: core::0134::response-message-name=disabled
  //     aip.dev/not-precedent: UpdateAppointmentResponse is appropriate for this use case --)
  rpc UpdateAppointment(UpdateAppointmentRequest) returns (UpdateAppointmentResponse);

  // 根据ID列表获取预约信息
  // (-- api-linter: core::0131::response-message-name=disabled
  //     aip.dev/not-precedent: GetAppointmentByIdsResponse is appropriate for this use case --)
  // (-- api-linter: core::0136::prepositions=disabled
  //     aip.dev/not-precedent: GetAppointmentByIds is appropriate for this use case --)
  rpc GetAppointmentByIDs(GetAppointmentByIDsRequest) returns (GetAppointmentByIDsResponse);
}

// 根据ID列表获取预约请求
message GetAppointmentByIDsRequest {
  // 预约ID列表
  repeated int64 appointment_ids = 1;
}

// 根据ID列表获取预约响应
message GetAppointmentByIDsResponse {
  // 预约列表
  repeated Appointment appointments = 1;
}

// 列出预约请求
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: company_id is used as parent in this context --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 不需要page_token --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 不需要page_size，使用现有的分页机制 --)
message ListAppointmentRequest {
  // 公司ID
  int64 company_id = 1 [(buf.validate.field).int64.gt = 0];
  // 商家ID
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 查询开始时间
  optional google.protobuf.Timestamp start_time = 3;
  // 查询结束时间
  optional google.protobuf.Timestamp end_time = 4;
  // 过滤条件
  // (-- api-linter: core::0132::request-field-types=disabled
  //     aip.dev/not-precedent: 打平成string不利用协议理解 --)
  optional AppointmentFilter filter = 5;
  // 分页信息
  optional PaginationRef pagination = 6;
}

// 列出预约响应
  // (-- api-linter: core::0158::response-next-page-token-field=disabled
  //     aip.dev/not-precedent: 不需要next_page_token字段，使用现有的分页机制 --)
message ListAppointmentResponse {
  // 履约列表
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  repeated Appointment appointments = 1;
  // 分页信息
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  PaginationRef pagination = 2;
  // 是否最后一页
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  bool is_end = 3;
  // 总条数
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  int32 total = 4;
}

// 创建预约请求
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 不需要Appointment字段，直接使用各个字段 --)
message CreateAppointmentRequest {
  // 商家ID
  int64 business_id = 1;
  // 公司ID
  int64 company_id = 2;
  // 客户ID
  int64 customer_id = 3;
  // 订单开始时间
  google.protobuf.Timestamp start_time = 4;
  // 订单结束时间
  google.protobuf.Timestamp end_time = 5;
  // 宠物详情列表
  repeated CreatePetDetailDef pets = 6;
  // 颜色代码
  string color_code = 7;
  // 来源:1-onlinebooking 2-staff
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 source = 8;
}

// 创建预约响应
message CreateAppointmentResponse {
  // 预约ID
  int64 id = 1;
}

// 更新预约请求
message UpdateAppointmentRequest{
  // 必须字段
  int64 appointment_id = 1 [(buf.validate.field).int64.gt = 0];

  // 订单层面信息更新
  message AppointmentOperation {
    // 开始时间
    optional google.protobuf.Timestamp start_time = 1;
    // 结束时间
    optional google.protobuf.Timestamp end_time = 2;
    // 颜色代码
    optional string color_code = 3;
    // 新状态
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: 状态字段需要可写 --)
    optional AppointmentState new_status = 4;
  }
  // 预约操作
  optional AppointmentOperation appointment_operation = 2;

  // 服务维度操作
  message ServiceOperation {
    // 操作模式
    OperationMode operation_mode = 1;
    // 服务实例ID，operation_mode为REPLACE 或 DELETE 必填
    optional int64 service_instance_id = 2;
    // 服务模板ID，operation_mode为NEW要填
    optional int64 service_id = 3;
    // 父服务实例ID，operation_mode为NEW，并且是sub_service_instance要填
    optional int64 parent_service_instance_id = 4;
    // 日期类型
    optional DateType date_type = 5;
    // 服务开始时间
    optional google.protobuf.Timestamp start_time = 6;
    // 服务结束时间
    optional google.protobuf.Timestamp end_time = 7;
    // 子服务实例列表
    repeated ServiceOperation sub_service_instances = 8;
  }
  // 服务操作列表
  repeated ServiceOperation service_operations = 3;
}

// 更新预约响应
message UpdateAppointmentResponse {
  // 整体更新结果
  bool success = 1;
  // 更新消息
  string message = 2;
}
