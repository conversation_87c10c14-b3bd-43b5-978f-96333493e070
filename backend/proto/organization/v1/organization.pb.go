// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/organization/v1/organization.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Organization type.
type OrganizationType int32

const (
	// Unspecified organization type.
	OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED OrganizationType = 0
	// Enterprise.
	OrganizationType_ORGANIZATION_TYPE_ENTERPRISE OrganizationType = 1
	// Company.
	OrganizationType_ORGANIZATION_TYPE_COMPANY OrganizationType = 2
	// Business.
	OrganizationType_ORGANIZATION_TYPE_BUSINESS OrganizationType = 3
)

// Enum value maps for OrganizationType.
var (
	OrganizationType_name = map[int32]string{
		0: "ORGANIZATION_TYPE_UNSPECIFIED",
		1: "ORGANIZATION_TYPE_ENTERPRISE",
		2: "ORGANIZATION_TYPE_COMPANY",
		3: "ORGANIZATION_TYPE_BUSINESS",
	}
	OrganizationType_value = map[string]int32{
		"ORGANIZATION_TYPE_UNSPECIFIED": 0,
		"ORGANIZATION_TYPE_ENTERPRISE":  1,
		"ORGANIZATION_TYPE_COMPANY":     2,
		"ORGANIZATION_TYPE_BUSINESS":    3,
	}
)

func (x OrganizationType) Enum() *OrganizationType {
	p := new(OrganizationType)
	*p = x
	return p
}

func (x OrganizationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrganizationType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_organization_v1_organization_proto_enumTypes[0].Descriptor()
}

func (OrganizationType) Type() protoreflect.EnumType {
	return &file_backend_proto_organization_v1_organization_proto_enumTypes[0]
}

func (x OrganizationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrganizationType.Descriptor instead.
func (OrganizationType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_organization_v1_organization_proto_rawDescGZIP(), []int{0}
}

var File_backend_proto_organization_v1_organization_proto protoreflect.FileDescriptor

const file_backend_proto_organization_v1_organization_proto_rawDesc = "" +
	"\n" +
	"0backend/proto/organization/v1/organization.proto\x12\x1dbackend.proto.organization.v1*\x96\x01\n" +
	"\x10OrganizationType\x12!\n" +
	"\x1dORGANIZATION_TYPE_UNSPECIFIED\x10\x00\x12 \n" +
	"\x1cORGANIZATION_TYPE_ENTERPRISE\x10\x01\x12\x1d\n" +
	"\x19ORGANIZATION_TYPE_COMPANY\x10\x02\x12\x1e\n" +
	"\x1aORGANIZATION_TYPE_BUSINESS\x10\x03Bw\n" +
	"'com.moego.backend.proto.organization.v1P\x01ZJgithub.com/MoeGolibrary/moego/backend/proto/organization/v1;organizationpbb\x06proto3"

var (
	file_backend_proto_organization_v1_organization_proto_rawDescOnce sync.Once
	file_backend_proto_organization_v1_organization_proto_rawDescData []byte
)

func file_backend_proto_organization_v1_organization_proto_rawDescGZIP() []byte {
	file_backend_proto_organization_v1_organization_proto_rawDescOnce.Do(func() {
		file_backend_proto_organization_v1_organization_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_organization_v1_organization_proto_rawDesc), len(file_backend_proto_organization_v1_organization_proto_rawDesc)))
	})
	return file_backend_proto_organization_v1_organization_proto_rawDescData
}

var file_backend_proto_organization_v1_organization_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_organization_v1_organization_proto_goTypes = []any{
	(OrganizationType)(0), // 0: backend.proto.organization.v1.OrganizationType
}
var file_backend_proto_organization_v1_organization_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_organization_v1_organization_proto_init() }
func file_backend_proto_organization_v1_organization_proto_init() {
	if File_backend_proto_organization_v1_organization_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_organization_v1_organization_proto_rawDesc), len(file_backend_proto_organization_v1_organization_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_organization_v1_organization_proto_goTypes,
		DependencyIndexes: file_backend_proto_organization_v1_organization_proto_depIdxs,
		EnumInfos:         file_backend_proto_organization_v1_organization_proto_enumTypes,
	}.Build()
	File_backend_proto_organization_v1_organization_proto = out.File
	file_backend_proto_organization_v1_organization_proto_goTypes = nil
	file_backend_proto_organization_v1_organization_proto_depIdxs = nil
}
