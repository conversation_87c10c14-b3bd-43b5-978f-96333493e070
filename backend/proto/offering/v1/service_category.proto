syntax = "proto3";

package backend.proto.offering.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "backend/proto/organization/v1/organization.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// Defines the structure for a service category, used to organize services.
message ServiceCategory {
  // Primary key ID of the service category.
  int64 id = 1;

  // The type of the organization (e.g., company, enterprise).
  backend.proto.organization.v1.OrganizationType organization_type = 2;

  // The ID of the organization this category belongs to.
  int64 organization_id = 3;

  // The ID of the care type associated with this category.
  int64 care_type_id = 4;

  // The name of the service category, unique within the same organization and care type.
  string name = 5;

  // The sorting order of the service category.
  int32 sort = 6;

  // The timestamp when the category was created.
  google.protobuf.Timestamp create_time = 7;

  // The timestamp when the category was last updated.
  google.protobuf.Timestamp update_time = 8;

  // The timestamp when the category was soft-deleted. Null if not deleted.
  bool is_deleted = 9;
}