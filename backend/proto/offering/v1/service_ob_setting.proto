syntax = "proto3";

package backend.proto.offering.v1;

import "google/api/field_behavior.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// Defines the structure for a service online booking setting, which controls how a service appears and behaves in online booking.
message ServiceOBSetting {
  // Reference to the service template
  int64 service_id = 4;

  // Whether the service is available for online booking
  bool is_available = 5;

  // Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
  ShowBasePriceMode show_base_price = 6;

  // Whether all staff are available for this service when booking online
  bool is_all_staff = 7;

  // Whether all staff are available for this service when booking online
  repeated int64 staff_ids = 8;

  // Optional display alias for the service in online booking
  string alias = 9;

  // Display price mode for online booking
  enum ShowBasePriceMode {
    // Unspecified show base price mode
    SHOW_BASE_PRICE_MODE_UNSPECIFIED = 0;
    // Do not show price
    SHOW_BASE_PRICE_MODE_DO_NOT_SHOW = 1;
    // Show fixed price
    SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE = 2;
    // Show "starting at" price
    SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT = 3;
    // Show "varies" price
    SHOW_BASE_PRICE_MODE_SHOW_VARIES = 4;
  }
}