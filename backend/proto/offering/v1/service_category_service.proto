syntax = "proto3";

package backend.proto.offering.v1;

import "backend/proto/offering/v1/service_category.proto";
import "backend/proto/offering/v1/common.proto";
import "backend/proto/organization/v1/organization.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// category for managing Service resources.
service ServiceCategoryService {
    // list categories
    rpc ListCategories(ListCategoriesRequest) returns (ListCategoriesResponse);
    // save categories
    rpc SaveCategories(SaveCategoriesRequest) returns (SaveCategoriesResponse);

}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: no need page --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: no need page --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: no need page --)
// list category request
message ListCategoriesRequest{
  // The organization type
  backend.proto.organization.v1.OrganizationType organization_type = 1;
  // The organization ID.
  int64 organization_id = 2;
  // filter
  Filter filter = 18;

  // list filter
  message Filter {
    // category ids
    repeated int64 category_ids = 2;
    // care type ids
    repeated int64 care_type_ids = 3;
  }
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: no need page --)
// list setting services response
message ListCategoriesResponse{
  // category list
  repeated ServiceCategory categories = 1;
}
// save categories request
message SaveCategoriesRequest{
  // The type of the organization (e.g., company, enterprise).
  backend.proto.organization.v1.OrganizationType organization_type = 1;
  // The ID of the organization this category belongs to.
  int64 organization_id = 2;
  // create category list
  repeated CategoryCreateDef create_categories = 3 ;
  // update category list
  repeated CategoryUpdateDef update_categories = 4 ;
  // delete ids category
  repeated int64 delete_ids = 5 ;


  // Defines the structure for a service category, used to organize services.
  message CategoryUpdateDef {
    // Primary key ID of the service category.
    int64 id = 1;
    // The ID of the care type associated with this category.
    int64 care_type_id = 2 ;
    // The name of the service category, unique within the same organization and care type.
    string name = 3 ;
    // sort order of the category
    int64 sort = 4 ;
  }

  // Defines the structure for a service category, used to organize services.
  message CategoryCreateDef {
    // The ID of the care type associated with this category.
    int64 care_type_id = 2 ;
    // The name of the service category, unique within the same organization and care type.
    string name = 3 ;
    // sort order of the category
    int64 sort = 4 ;
  }
}
// save categories response
message SaveCategoriesResponse{
}
