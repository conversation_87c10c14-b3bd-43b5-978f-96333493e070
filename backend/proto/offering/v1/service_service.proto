syntax = "proto3";

package backend.proto.offering.v1;

import "google/api/field_behavior.proto";
import "backend/proto/offering/v1/service.proto";
import "backend/proto/offering/v1/service_ob_setting.proto";
import "backend/proto/organization/v1/organization.proto";
import "backend/proto/offering/v1/common.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// Service for managing Service resources.
service ServiceService {
  // 创建一个服务
  rpc CreateService(CreateServiceRequest) returns (CreateServiceResponse);

  // 获取服务
  rpc GetService(GetServiceRequest) returns (GetServiceResponse);

  // 更新服务
  rpc UpdateService(UpdateServiceRequest) returns (UpdateServiceResponse);

  // 删除服务
  rpc DeleteService(DeleteServiceRequest) returns (DeleteServiceResponse);

  // list services
  rpc ListServices(ListServicesRequest) returns (ListServicesResponse);

  // 更新服务
  rpc UpdateOBService(UpdateOBServiceRequest) returns (UpdateOBServiceResponse);
}

// 删除服务请求
message DeleteServiceRequest {
  // 服务ID
  int64 service_id = 1;
}

// 删除服务响应
message DeleteServiceResponse{

}

// 更新服务请求
message UpdateServiceRequest{
  // 服务
  Service service = 1;
}

// 更新服务响应
message UpdateServiceResponse{
}

// 创建服务请求
message CreateServiceRequest{
  // 服务
  Service service = 1;
}

// 创建服务响应
message CreateServiceResponse{
  // 服务 ID
  int64 service_id = 1;
}

// 获取服务请求
message GetServiceRequest {
  // 服务ID
  int64 service_id = 1;
}

// 获取服务响应
message GetServiceResponse {
  // 服务
  Service service = 1;
}

// 分类服务
message CategoryService {
  // 分类ID
  int64 category_id = 1;
  // 分类名称
  string name = 2;
  // 服务模板列表
  repeated Service services = 3;
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// list services request
// TODO: CR List 和 BatchGet 分开
message ListServicesRequest{
  // The organization type
  backend.proto.organization.v1.OrganizationType organization_type = 1;
  // The organization ID.
  int64 organization_id = 2;
  // filter
  Filter filter = 18 [(google.api.field_behavior) = OPTIONAL];

  // options to include extra info in response
  ExtraInfoOptions extra_info_options = 19;
  // 分页信息
  PaginationRef pagination = 20;

  // list filter
  message Filter {
    // category ids
    repeated int64 category_ids = 4;
    // care type ids
    repeated int64 care_type_ids = 5;
    // is active
    optional bool is_active = 6;
  }
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 PaginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
// list setting services response
message ListServicesResponse{
  // 服务模板列表
  repeated ServiceWithExtraInfo services = 1;
  // 分页信息
  optional PaginationRef pagination = 2;
  // 总数
  int32 total = 3;
}


//UpdateOBServiceRequest
message UpdateOBServiceRequest{
  // 服务
  int64 service_id = 1;
  // Whether the service is available for online booking
  optional bool is_available = 2;
  // Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
  optional ServiceOBSetting.ShowBasePriceMode show_base_price = 3;
  // Whether all staff are available for this service when booking online
  optional bool is_all_staff = 4;
  //staff ids
  repeated int64 staff_ids = 5;
  // Optional display alias for the service in online booking
  optional string alias = 6;
}

//Update OB Service Response
message UpdateOBServiceResponse{
}