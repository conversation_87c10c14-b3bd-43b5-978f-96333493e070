// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_service.proto

package offeringpb

import (
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 删除服务请求
type DeleteServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceRequest) Reset() {
	*x = DeleteServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceRequest) ProtoMessage() {}

func (x *DeleteServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 删除服务响应
type DeleteServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceResponse) Reset() {
	*x = DeleteServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceResponse) ProtoMessage() {}

func (x *DeleteServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{1}
}

// 更新服务请求
type UpdateServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	Service       *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceRequest) Reset() {
	*x = UpdateServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceRequest) ProtoMessage() {}

func (x *UpdateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateServiceRequest) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// 更新服务响应
type UpdateServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceResponse) Reset() {
	*x = UpdateServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceResponse) ProtoMessage() {}

func (x *UpdateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{3}
}

// 创建服务请求
type CreateServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	Service       *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceRequest) Reset() {
	*x = CreateServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceRequest) ProtoMessage() {}

func (x *CreateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateServiceRequest) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// 创建服务响应
type CreateServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务 ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceResponse) Reset() {
	*x = CreateServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceResponse) ProtoMessage() {}

func (x *CreateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateServiceResponse) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 获取服务请求
type GetServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceRequest) Reset() {
	*x = GetServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceRequest) ProtoMessage() {}

func (x *GetServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceRequest.ProtoReflect.Descriptor instead.
func (*GetServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 获取服务响应
type GetServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	Service       *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceResponse) Reset() {
	*x = GetServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceResponse) ProtoMessage() {}

func (x *GetServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceResponse.ProtoReflect.Descriptor instead.
func (*GetServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetServiceResponse) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// 分类服务
type CategoryService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	CategoryId int64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 分类名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 服务模板列表
	Services      []*Service `protobuf:"bytes,3,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CategoryService) Reset() {
	*x = CategoryService{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CategoryService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryService) ProtoMessage() {}

func (x *CategoryService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryService.ProtoReflect.Descriptor instead.
func (*CategoryService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{8}
}

func (x *CategoryService) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CategoryService) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CategoryService) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// list services request
// TODO: CR List 和 BatchGet 分开
type ListServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The organization type
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The organization ID.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// filter
	Filter *ListServicesRequest_Filter `protobuf:"bytes,18,opt,name=filter,proto3" json:"filter,omitempty"`
	// options to include extra info in response
	ExtraInfoOptions *ExtraInfoOptions `protobuf:"bytes,19,opt,name=extra_info_options,json=extraInfoOptions,proto3" json:"extra_info_options,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,20,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesRequest) Reset() {
	*x = ListServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest) ProtoMessage() {}

func (x *ListServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest.ProtoReflect.Descriptor instead.
func (*ListServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListServicesRequest) GetFilter() *ListServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListServicesRequest) GetExtraInfoOptions() *ExtraInfoOptions {
	if x != nil {
		return x.ExtraInfoOptions
	}
	return nil
}

func (x *ListServicesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// list setting services response
type ListServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板列表
	Services []*ServiceWithExtraInfo `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesResponse) Reset() {
	*x = ListServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesResponse) ProtoMessage() {}

func (x *ListServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesResponse.ProtoReflect.Descriptor instead.
func (*ListServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListServicesResponse) GetServices() []*ServiceWithExtraInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ListServicesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServicesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// UpdateOBServiceRequest
type UpdateOBServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Whether the service is available for online booking
	IsAvailable *bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3,oneof" json:"is_available,omitempty"`
	// Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
	ShowBasePrice *ServiceOBSetting_ShowBasePriceMode `protobuf:"varint,3,opt,name=show_base_price,json=showBasePrice,proto3,enum=backend.proto.offering.v1.ServiceOBSetting_ShowBasePriceMode,oneof" json:"show_base_price,omitempty"`
	// Whether all staff are available for this service when booking online
	IsAllStaff *bool `protobuf:"varint,4,opt,name=is_all_staff,json=isAllStaff,proto3,oneof" json:"is_all_staff,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,5,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// Optional display alias for the service in online booking
	Alias         *string `protobuf:"bytes,6,opt,name=alias,proto3,oneof" json:"alias,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOBServiceRequest) Reset() {
	*x = UpdateOBServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOBServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBServiceRequest) ProtoMessage() {}

func (x *UpdateOBServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateOBServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateOBServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *UpdateOBServiceRequest) GetIsAvailable() bool {
	if x != nil && x.IsAvailable != nil {
		return *x.IsAvailable
	}
	return false
}

func (x *UpdateOBServiceRequest) GetShowBasePrice() ServiceOBSetting_ShowBasePriceMode {
	if x != nil && x.ShowBasePrice != nil {
		return *x.ShowBasePrice
	}
	return ServiceOBSetting_SHOW_BASE_PRICE_MODE_UNSPECIFIED
}

func (x *UpdateOBServiceRequest) GetIsAllStaff() bool {
	if x != nil && x.IsAllStaff != nil {
		return *x.IsAllStaff
	}
	return false
}

func (x *UpdateOBServiceRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *UpdateOBServiceRequest) GetAlias() string {
	if x != nil && x.Alias != nil {
		return *x.Alias
	}
	return ""
}

// Update OB Service Response
type UpdateOBServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOBServiceResponse) Reset() {
	*x = UpdateOBServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOBServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBServiceResponse) ProtoMessage() {}

func (x *UpdateOBServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateOBServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{12}
}

// list filter
type ListServicesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// category ids
	CategoryIds []int64 `protobuf:"varint,4,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// care type ids
	CareTypeIds []int64 `protobuf:"varint,5,rep,packed,name=care_type_ids,json=careTypeIds,proto3" json:"care_type_ids,omitempty"`
	// is active
	IsActive      *bool `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesRequest_Filter) Reset() {
	*x = ListServicesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest_Filter) ProtoMessage() {}

func (x *ListServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListServicesRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetCareTypeIds() []int64 {
	if x != nil {
		return x.CareTypeIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

var File_backend_proto_offering_v1_service_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_service_proto_rawDesc = "" +
	"\n" +
	"/backend/proto/offering/v1/service_service.proto\x12\x19backend.proto.offering.v1\x1a\x1fgoogle/api/field_behavior.proto\x1a'backend/proto/offering/v1/service.proto\x1a2backend/proto/offering/v1/service_ob_setting.proto\x1a0backend/proto/organization/v1/organization.proto\x1a&backend/proto/offering/v1/common.proto\"5\n" +
	"\x14DeleteServiceRequest\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\"\x17\n" +
	"\x15DeleteServiceResponse\"T\n" +
	"\x14UpdateServiceRequest\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\"\x17\n" +
	"\x15UpdateServiceResponse\"T\n" +
	"\x14CreateServiceRequest\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\"6\n" +
	"\x15CreateServiceResponse\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\"2\n" +
	"\x11GetServiceRequest\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\"R\n" +
	"\x12GetServiceResponse\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\"\x86\x01\n" +
	"\x0fCategoryService\x12\x1f\n" +
	"\vcategory_id\x18\x01 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12>\n" +
	"\bservices\x18\x03 \x03(\v2\".backend.proto.offering.v1.ServiceR\bservices\"\x96\x04\n" +
	"\x13ListServicesRequest\x12\\\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12R\n" +
	"\x06filter\x18\x12 \x01(\v25.backend.proto.offering.v1.ListServicesRequest.FilterB\x03\xe0A\x01R\x06filter\x12Y\n" +
	"\x12extra_info_options\x18\x13 \x01(\v2+.backend.proto.offering.v1.ExtraInfoOptionsR\x10extraInfoOptions\x12H\n" +
	"\n" +
	"pagination\x18\x14 \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\x1a\x7f\n" +
	"\x06Filter\x12!\n" +
	"\fcategory_ids\x18\x04 \x03(\x03R\vcategoryIds\x12\"\n" +
	"\rcare_type_ids\x18\x05 \x03(\x03R\vcareTypeIds\x12 \n" +
	"\tis_active\x18\x06 \x01(\bH\x00R\bisActive\x88\x01\x01B\f\n" +
	"\n" +
	"_is_active\"\xd7\x01\n" +
	"\x14ListServicesResponse\x12K\n" +
	"\bservices\x18\x01 \x03(\v2/.backend.proto.offering.v1.ServiceWithExtraInfoR\bservices\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x00R\n" +
	"pagination\x88\x01\x01\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05totalB\r\n" +
	"\v_pagination\"\xea\x02\n" +
	"\x16UpdateOBServiceRequest\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\x12&\n" +
	"\fis_available\x18\x02 \x01(\bH\x00R\visAvailable\x88\x01\x01\x12j\n" +
	"\x0fshow_base_price\x18\x03 \x01(\x0e2=.backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceModeH\x01R\rshowBasePrice\x88\x01\x01\x12%\n" +
	"\fis_all_staff\x18\x04 \x01(\bH\x02R\n" +
	"isAllStaff\x88\x01\x01\x12\x1b\n" +
	"\tstaff_ids\x18\x05 \x03(\x03R\bstaffIds\x12\x19\n" +
	"\x05alias\x18\x06 \x01(\tH\x03R\x05alias\x88\x01\x01B\x0f\n" +
	"\r_is_availableB\x12\n" +
	"\x10_show_base_priceB\x0f\n" +
	"\r_is_all_staffB\b\n" +
	"\x06_alias\"\x19\n" +
	"\x17UpdateOBServiceResponse2\xc2\x05\n" +
	"\x0eServiceService\x12r\n" +
	"\rCreateService\x12/.backend.proto.offering.v1.CreateServiceRequest\x1a0.backend.proto.offering.v1.CreateServiceResponse\x12i\n" +
	"\n" +
	"GetService\x12,.backend.proto.offering.v1.GetServiceRequest\x1a-.backend.proto.offering.v1.GetServiceResponse\x12r\n" +
	"\rUpdateService\x12/.backend.proto.offering.v1.UpdateServiceRequest\x1a0.backend.proto.offering.v1.UpdateServiceResponse\x12r\n" +
	"\rDeleteService\x12/.backend.proto.offering.v1.DeleteServiceRequest\x1a0.backend.proto.offering.v1.DeleteServiceResponse\x12o\n" +
	"\fListServices\x12..backend.proto.offering.v1.ListServicesRequest\x1a/.backend.proto.offering.v1.ListServicesResponse\x12x\n" +
	"\x0fUpdateOBService\x121.backend.proto.offering.v1.UpdateOBServiceRequest\x1a2.backend.proto.offering.v1.UpdateOBServiceResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_backend_proto_offering_v1_service_service_proto_goTypes = []any{
	(*DeleteServiceRequest)(nil),            // 0: backend.proto.offering.v1.DeleteServiceRequest
	(*DeleteServiceResponse)(nil),           // 1: backend.proto.offering.v1.DeleteServiceResponse
	(*UpdateServiceRequest)(nil),            // 2: backend.proto.offering.v1.UpdateServiceRequest
	(*UpdateServiceResponse)(nil),           // 3: backend.proto.offering.v1.UpdateServiceResponse
	(*CreateServiceRequest)(nil),            // 4: backend.proto.offering.v1.CreateServiceRequest
	(*CreateServiceResponse)(nil),           // 5: backend.proto.offering.v1.CreateServiceResponse
	(*GetServiceRequest)(nil),               // 6: backend.proto.offering.v1.GetServiceRequest
	(*GetServiceResponse)(nil),              // 7: backend.proto.offering.v1.GetServiceResponse
	(*CategoryService)(nil),                 // 8: backend.proto.offering.v1.CategoryService
	(*ListServicesRequest)(nil),             // 9: backend.proto.offering.v1.ListServicesRequest
	(*ListServicesResponse)(nil),            // 10: backend.proto.offering.v1.ListServicesResponse
	(*UpdateOBServiceRequest)(nil),          // 11: backend.proto.offering.v1.UpdateOBServiceRequest
	(*UpdateOBServiceResponse)(nil),         // 12: backend.proto.offering.v1.UpdateOBServiceResponse
	(*ListServicesRequest_Filter)(nil),      // 13: backend.proto.offering.v1.ListServicesRequest.Filter
	(*Service)(nil),                         // 14: backend.proto.offering.v1.Service
	(v1.OrganizationType)(0),                // 15: backend.proto.organization.v1.OrganizationType
	(*ExtraInfoOptions)(nil),                // 16: backend.proto.offering.v1.ExtraInfoOptions
	(*PaginationRef)(nil),                   // 17: backend.proto.offering.v1.PaginationRef
	(*ServiceWithExtraInfo)(nil),            // 18: backend.proto.offering.v1.ServiceWithExtraInfo
	(ServiceOBSetting_ShowBasePriceMode)(0), // 19: backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
}
var file_backend_proto_offering_v1_service_service_proto_depIdxs = []int32{
	14, // 0: backend.proto.offering.v1.UpdateServiceRequest.service:type_name -> backend.proto.offering.v1.Service
	14, // 1: backend.proto.offering.v1.CreateServiceRequest.service:type_name -> backend.proto.offering.v1.Service
	14, // 2: backend.proto.offering.v1.GetServiceResponse.service:type_name -> backend.proto.offering.v1.Service
	14, // 3: backend.proto.offering.v1.CategoryService.services:type_name -> backend.proto.offering.v1.Service
	15, // 4: backend.proto.offering.v1.ListServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	13, // 5: backend.proto.offering.v1.ListServicesRequest.filter:type_name -> backend.proto.offering.v1.ListServicesRequest.Filter
	16, // 6: backend.proto.offering.v1.ListServicesRequest.extra_info_options:type_name -> backend.proto.offering.v1.ExtraInfoOptions
	17, // 7: backend.proto.offering.v1.ListServicesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	18, // 8: backend.proto.offering.v1.ListServicesResponse.services:type_name -> backend.proto.offering.v1.ServiceWithExtraInfo
	17, // 9: backend.proto.offering.v1.ListServicesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	19, // 10: backend.proto.offering.v1.UpdateOBServiceRequest.show_base_price:type_name -> backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	4,  // 11: backend.proto.offering.v1.ServiceService.CreateService:input_type -> backend.proto.offering.v1.CreateServiceRequest
	6,  // 12: backend.proto.offering.v1.ServiceService.GetService:input_type -> backend.proto.offering.v1.GetServiceRequest
	2,  // 13: backend.proto.offering.v1.ServiceService.UpdateService:input_type -> backend.proto.offering.v1.UpdateServiceRequest
	0,  // 14: backend.proto.offering.v1.ServiceService.DeleteService:input_type -> backend.proto.offering.v1.DeleteServiceRequest
	9,  // 15: backend.proto.offering.v1.ServiceService.ListServices:input_type -> backend.proto.offering.v1.ListServicesRequest
	11, // 16: backend.proto.offering.v1.ServiceService.UpdateOBService:input_type -> backend.proto.offering.v1.UpdateOBServiceRequest
	5,  // 17: backend.proto.offering.v1.ServiceService.CreateService:output_type -> backend.proto.offering.v1.CreateServiceResponse
	7,  // 18: backend.proto.offering.v1.ServiceService.GetService:output_type -> backend.proto.offering.v1.GetServiceResponse
	3,  // 19: backend.proto.offering.v1.ServiceService.UpdateService:output_type -> backend.proto.offering.v1.UpdateServiceResponse
	1,  // 20: backend.proto.offering.v1.ServiceService.DeleteService:output_type -> backend.proto.offering.v1.DeleteServiceResponse
	10, // 21: backend.proto.offering.v1.ServiceService.ListServices:output_type -> backend.proto.offering.v1.ListServicesResponse
	12, // 22: backend.proto.offering.v1.ServiceService.UpdateOBService:output_type -> backend.proto.offering.v1.UpdateOBServiceResponse
	17, // [17:23] is the sub-list for method output_type
	11, // [11:17] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_service_proto_init() }
func file_backend_proto_offering_v1_service_service_proto_init() {
	if File_backend_proto_offering_v1_service_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_service_proto_init()
	file_backend_proto_offering_v1_service_ob_setting_proto_init()
	file_backend_proto_offering_v1_common_proto_init()
	file_backend_proto_offering_v1_service_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[11].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[13].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_service_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_service_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_service_proto = out.File
	file_backend_proto_offering_v1_service_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_service_proto_depIdxs = nil
}
