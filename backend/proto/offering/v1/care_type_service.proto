syntax = "proto3";

package backend.proto.offering.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/field_mask.proto";
import "backend/proto/offering/v1/care_type.proto";
import "backend/proto/offering/v1/common.proto";
import "backend/proto/organization/v1/organization.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 统一用 Response message. --)
// Service for managing CareType configurations .
service CareTypeService {
  // Creates a new care type.
  rpc CreateCareType(CreateCareTypeRequest) returns (CreateCareTypeResponse);

  // Gets a single care type.
  rpc GetCareType(GetCareTypeRequest) returns (GetCareTypeResponse);

  // Lists care types.
  rpc ListCareTypes(ListCareTypesRequest) returns (ListCareTypesResponse);

  // Updates a care type.
  rpc UpdateCareType(UpdateCareTypeRequest) returns (UpdateCareTypeResponse);

  // Deletes a care type.
  rpc DeleteCareType(DeleteCareTypeRequest) returns (DeleteCareTypeResponse);

  // Creates a new care type attribute.
  rpc CreateCareTypeAttribute(CreateCareTypeAttributeRequest) returns (CreateCareTypeAttributeResponse);

  // Lists care type attributes.
  rpc ListCareTypeAttributes(ListCareTypeAttributesRequest) returns (ListCareTypeAttributesResponse);

  // Deletes a care type attribute.
  rpc DeleteCareTypeAttribute(DeleteCareTypeAttributeRequest) returns (DeleteCareTypeAttributeResponse);
}

// Request message for CreateCareType.
message CreateCareTypeRequest {
  // The care type to create.
  CareType care_type = 1;
}

// Response message for CreateCareType.
message CreateCareTypeResponse {
  // The created care type.
  CareType care_type = 1;
}

// Request message for GetCareType.
message GetCareTypeRequest {
  // The ID of the care type to retrieve.
  int64 id = 1;
}

// Response message for GetCareType.
message GetCareTypeResponse {
  // The retrieved care type.
  CareType care_type = 1;
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 paginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 paginationRef 替代 --)
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//     aip.dev/not-precedent: 使用 paginationRef 替代 --)
// Request message for ListCareTypes.
message ListCareTypesRequest {
  // The organization type
  backend.proto.organization.v1.OrganizationType organization_type = 1;
  // The organization ID.
  int64 organization_id = 2;
  // Pagination options. If omitted, all records will be returned.
  PaginationRef pagination = 3;
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 paginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
// Response message for ListCareTypes.
message ListCareTypesResponse {
  // A list of care types.
  repeated CareType care_types = 1;
  // Pagination options. If omitted, all records will be returned.
  PaginationRef pagination = 2 [(google.api.field_behavior) = OPTIONAL];
  // Total number of care types.
  int32 total = 3;
}

// Request message for UpdateCareType.
message UpdateCareTypeRequest {
  // The care type to update. Its `id` must be set.
  CareType care_type = 1;
  // Field mask to specify which fields to update.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for UpdateCareType.
message UpdateCareTypeResponse {
  // The updated care type.
  CareType care_type = 1;
}

// Request message for DeleteCareType.
message DeleteCareTypeRequest {
  // The ID of the care type to delete.
  int64 id = 1;
}

// Response message for DeleteCareType.
message DeleteCareTypeResponse {}

// Request message for CreateCareTypeAttribute.
message CreateCareTypeAttributeRequest {
  // The care type attribute to create.
  CareTypeAttribute care_type_attribute = 1;
}

// Response message for CreateCareTypeAttribute.
message CreateCareTypeAttributeResponse {
  // The created care type attribute.
  CareTypeAttribute care_type_attribute = 1;
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 使用 paginationRef 替代 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 使用 paginationRef 替代 --)
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//     aip.dev/not-precedent: 使用 paginationRef 替代 --)
// Request message for ListCareTypeAttributes.
message ListCareTypeAttributesRequest {
  // The ID of the care type to list attributes for.
  int64 care_type_id = 1;
  // Pagination options. If omitted, all records will be returned.
  PaginationRef pagination = 2;
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用 paginationRef 替代 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 额外返回 total. --)
// Response message for ListCareTypeAttributes.
message ListCareTypeAttributesResponse {
  // A list of care type attributes.
  repeated CareTypeAttribute care_type_attributes = 1;
  // Pagination options. If omitted, all records will be returned.
  PaginationRef pagination = 2 [(google.api.field_behavior) = OPTIONAL];
  // Total number of care type attributes.
  int32 total = 3;
}


// Request message for DeleteCareTypeAttribute.
message DeleteCareTypeAttributeRequest {
  // The ID of the care type attribute to delete.
  int64 id = 1;
}

// Response message for DeleteCareTypeAttribute.
message DeleteCareTypeAttributeResponse {}
