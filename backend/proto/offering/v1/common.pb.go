// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/offering/v1/common.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 分页信息
type PaginationRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 偏移量，默认0
	Offset int32 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// 每页数量，默认200
	Limit         int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRef) Reset() {
	*x = PaginationRef{}
	mi := &file_backend_proto_offering_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRef) ProtoMessage() {}

func (x *PaginationRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRef.ProtoReflect.Descriptor instead.
func (*PaginationRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *PaginationRef) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *PaginationRef) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

var File_backend_proto_offering_v1_common_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_common_proto_rawDesc = "" +
	"\n" +
	"&backend/proto/offering/v1/common.proto\x12\x19backend.proto.offering.v1\"=\n" +
	"\rPaginationRef\x12\x16\n" +
	"\x06offset\x18\x01 \x01(\x05R\x06offset\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limitBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_common_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_common_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_common_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_common_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_common_proto_rawDesc), len(file_backend_proto_offering_v1_common_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_common_proto_rawDescData
}

var file_backend_proto_offering_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_offering_v1_common_proto_goTypes = []any{
	(*PaginationRef)(nil), // 0: backend.proto.offering.v1.PaginationRef
}
var file_backend_proto_offering_v1_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_common_proto_init() }
func file_backend_proto_offering_v1_common_proto_init() {
	if File_backend_proto_offering_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_common_proto_rawDesc), len(file_backend_proto_offering_v1_common_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_common_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_common_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_common_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_common_proto = out.File
	file_backend_proto_offering_v1_common_proto_goTypes = nil
	file_backend_proto_offering_v1_common_proto_depIdxs = nil
}
