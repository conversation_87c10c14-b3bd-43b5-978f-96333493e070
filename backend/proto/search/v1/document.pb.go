// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/search/v1/document.proto

package searchpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 操作类型枚举
type OperationType int32

const (
	// 未指定操作类型
	OperationType_OPERATION_TYPE_UNSPECIFIED OperationType = 0
	// 索引文档
	OperationType_INDEX OperationType = 1
	// 创建文档
	OperationType_CREATE OperationType = 2
	// 更新文档
	OperationType_UPDATE OperationType = 3
	// 删除文档
	OperationType_DELETE OperationType = 4
)

// Enum value maps for OperationType.
var (
	OperationType_name = map[int32]string{
		0: "OPERATION_TYPE_UNSPECIFIED",
		1: "INDEX",
		2: "CREATE",
		3: "UPDATE",
		4: "DELETE",
	}
	OperationType_value = map[string]int32{
		"OPERATION_TYPE_UNSPECIFIED": 0,
		"INDEX":                      1,
		"CREATE":                     2,
		"UPDATE":                     3,
		"DELETE":                     4,
	}
)

func (x OperationType) Enum() *OperationType {
	p := new(OperationType)
	*p = x
	return p
}

func (x OperationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperationType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_search_v1_document_proto_enumTypes[0].Descriptor()
}

func (OperationType) Type() protoreflect.EnumType {
	return &file_backend_proto_search_v1_document_proto_enumTypes[0]
}

func (x OperationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperationType.Descriptor instead.
func (OperationType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_document_proto_rawDescGZIP(), []int{0}
}

// 批量文档操作请求
type BulkDocumentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 批量操作列表（至少需要一个操作）
	Operations    []*BulkDocumentRequest_BulkOperation `protobuf:"bytes,1,rep,name=operations,proto3" json:"operations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDocumentRequest) Reset() {
	*x = BulkDocumentRequest{}
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDocumentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDocumentRequest) ProtoMessage() {}

func (x *BulkDocumentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDocumentRequest.ProtoReflect.Descriptor instead.
func (*BulkDocumentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_document_proto_rawDescGZIP(), []int{0}
}

func (x *BulkDocumentRequest) GetOperations() []*BulkDocumentRequest_BulkOperation {
	if x != nil {
		return x.Operations
	}
	return nil
}

// 批量操作响应
type BulkDocumentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 批量操作中是否存在任何错误
	HasErrors bool `protobuf:"varint,1,opt,name=has_errors,json=hasErrors,proto3" json:"has_errors,omitempty"`
	// 所有操作的结果列表
	Results []*BulkDocumentResponse_OperationResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`
	// 整个批量操作耗时（毫秒）
	Took          int32 `protobuf:"varint,3,opt,name=took,proto3" json:"took,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDocumentResponse) Reset() {
	*x = BulkDocumentResponse{}
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDocumentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDocumentResponse) ProtoMessage() {}

func (x *BulkDocumentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDocumentResponse.ProtoReflect.Descriptor instead.
func (*BulkDocumentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_document_proto_rawDescGZIP(), []int{1}
}

func (x *BulkDocumentResponse) GetHasErrors() bool {
	if x != nil {
		return x.HasErrors
	}
	return false
}

func (x *BulkDocumentResponse) GetResults() []*BulkDocumentResponse_OperationResult {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *BulkDocumentResponse) GetTook() int32 {
	if x != nil {
		return x.Took
	}
	return 0
}

// 批量操作
type BulkDocumentRequest_BulkOperation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作类型
	OperationType OperationType `protobuf:"varint,1,opt,name=operation_type,json=operationType,proto3,enum=backend.proto.search.v1.OperationType" json:"operation_type,omitempty"`
	// 目标信息, 指引到具体的索引
	Target *BulkDocumentRequest_BulkOperation_BulkTarget `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	// 文档内容JSON格式字符串
	// 对于DELETE操作，此字段应为空
	// 对于其他操作，此字段必填
	//
	//	type User struct {
	//	    Age   int    `json:"age"`
	//	    Name  string `json:"name"`
	//	    Wages int    `json:"wages"`
	//	}
	//
	// 从数据库读取到 user 后
	//
	// st, err := structpb.NewStruct(user)
	Document      *structpb.Struct `protobuf:"bytes,3,opt,name=document,proto3,oneof" json:"document,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDocumentRequest_BulkOperation) Reset() {
	*x = BulkDocumentRequest_BulkOperation{}
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDocumentRequest_BulkOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDocumentRequest_BulkOperation) ProtoMessage() {}

func (x *BulkDocumentRequest_BulkOperation) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDocumentRequest_BulkOperation.ProtoReflect.Descriptor instead.
func (*BulkDocumentRequest_BulkOperation) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_document_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BulkDocumentRequest_BulkOperation) GetOperationType() OperationType {
	if x != nil {
		return x.OperationType
	}
	return OperationType_OPERATION_TYPE_UNSPECIFIED
}

func (x *BulkDocumentRequest_BulkOperation) GetTarget() *BulkDocumentRequest_BulkOperation_BulkTarget {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *BulkDocumentRequest_BulkOperation) GetDocument() *structpb.Struct {
	if x != nil {
		return x.Document
	}
	return nil
}

// 操作目标
type BulkDocumentRequest_BulkOperation_BulkTarget struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 索引名称（必填）
	Index string `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	// 文档ID（可选）
	Id            *string `protobuf:"bytes,2,opt,name=id,proto3,oneof" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDocumentRequest_BulkOperation_BulkTarget) Reset() {
	*x = BulkDocumentRequest_BulkOperation_BulkTarget{}
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDocumentRequest_BulkOperation_BulkTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDocumentRequest_BulkOperation_BulkTarget) ProtoMessage() {}

func (x *BulkDocumentRequest_BulkOperation_BulkTarget) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDocumentRequest_BulkOperation_BulkTarget.ProtoReflect.Descriptor instead.
func (*BulkDocumentRequest_BulkOperation_BulkTarget) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_document_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BulkDocumentRequest_BulkOperation_BulkTarget) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *BulkDocumentRequest_BulkOperation_BulkTarget) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

// 错误信息
// 只在操作失败时出现（status >= 400）
// 此时 shards 字段将不存在
type BulkDocumentResponse_Error struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 错误类型，例如：
	// - document_missing_exception: 文档不存在
	// - version_conflict_engine_exception: 版本冲突
	// - mapper_parsing_exception: 字段映射错误
	// - 更多错误类型请参考 OpenSearch 文档
	ErrorType string `protobuf:"bytes,1,opt,name=error_type,json=errorType,proto3" json:"error_type,omitempty"`
	// 错误具体原因描述
	ErrorReason   string `protobuf:"bytes,2,opt,name=error_reason,json=errorReason,proto3" json:"error_reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDocumentResponse_Error) Reset() {
	*x = BulkDocumentResponse_Error{}
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDocumentResponse_Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDocumentResponse_Error) ProtoMessage() {}

func (x *BulkDocumentResponse_Error) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDocumentResponse_Error.ProtoReflect.Descriptor instead.
func (*BulkDocumentResponse_Error) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_document_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BulkDocumentResponse_Error) GetErrorType() string {
	if x != nil {
		return x.ErrorType
	}
	return ""
}

func (x *BulkDocumentResponse_Error) GetErrorReason() string {
	if x != nil {
		return x.ErrorReason
	}
	return ""
}

// 分片信息
// 只在操作成功时出现（status < 400）
// 此时 error 字段将不存在
type BulkDocumentResponse_Shards struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 总分片数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 成功分片数
	Successful int32 `protobuf:"varint,2,opt,name=successful,proto3" json:"successful,omitempty"`
	// 失败分片数
	Failed        int32 `protobuf:"varint,3,opt,name=failed,proto3" json:"failed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDocumentResponse_Shards) Reset() {
	*x = BulkDocumentResponse_Shards{}
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDocumentResponse_Shards) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDocumentResponse_Shards) ProtoMessage() {}

func (x *BulkDocumentResponse_Shards) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDocumentResponse_Shards.ProtoReflect.Descriptor instead.
func (*BulkDocumentResponse_Shards) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_document_proto_rawDescGZIP(), []int{1, 1}
}

func (x *BulkDocumentResponse_Shards) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *BulkDocumentResponse_Shards) GetSuccessful() int32 {
	if x != nil {
		return x.Successful
	}
	return 0
}

func (x *BulkDocumentResponse_Shards) GetFailed() int32 {
	if x != nil {
		return x.Failed
	}
	return 0
}

// 单个操作的结果
type BulkDocumentResponse_OperationResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作类型
	OperationType OperationType `protobuf:"varint,1,opt,name=operation_type,json=operationType,proto3,enum=backend.proto.search.v1.OperationType" json:"operation_type,omitempty"`
	// 索引名称
	Index string `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`
	// 文档ID
	DocumentId string `protobuf:"bytes,3,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`
	// 操作返回的状态码:
	// - 200: 成功（更新）
	// - 201: 成功（创建）
	// - 404: 文档不存在
	// - 409: 版本冲突
	Status int32 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	// 操作结果描述:
	// - created: 文档被创建
	// - updated: 文档被更新
	// - deleted: 文档被删除
	// - noop: 文档未发生变化
	// - not_found: 文档不存在
	Result string `protobuf:"bytes,5,opt,name=result,proto3" json:"result,omitempty"`
	// 文档版本号
	// 预期每次更新都会增加
	Version int32 `protobuf:"varint,6,opt,name=version,proto3" json:"version,omitempty"`
	// 错误信息（如果有）
	// 只在操作失败时存在（status >= 400）
	Error *BulkDocumentResponse_Error `protobuf:"bytes,7,opt,name=error,proto3,oneof" json:"error,omitempty"`
	// 分片信息
	// 只在操作成功时存在（status < 400）
	Shards        *BulkDocumentResponse_Shards `protobuf:"bytes,8,opt,name=shards,proto3" json:"shards,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDocumentResponse_OperationResult) Reset() {
	*x = BulkDocumentResponse_OperationResult{}
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDocumentResponse_OperationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDocumentResponse_OperationResult) ProtoMessage() {}

func (x *BulkDocumentResponse_OperationResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_document_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDocumentResponse_OperationResult.ProtoReflect.Descriptor instead.
func (*BulkDocumentResponse_OperationResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_document_proto_rawDescGZIP(), []int{1, 2}
}

func (x *BulkDocumentResponse_OperationResult) GetOperationType() OperationType {
	if x != nil {
		return x.OperationType
	}
	return OperationType_OPERATION_TYPE_UNSPECIFIED
}

func (x *BulkDocumentResponse_OperationResult) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *BulkDocumentResponse_OperationResult) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *BulkDocumentResponse_OperationResult) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BulkDocumentResponse_OperationResult) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *BulkDocumentResponse_OperationResult) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *BulkDocumentResponse_OperationResult) GetError() *BulkDocumentResponse_Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BulkDocumentResponse_OperationResult) GetShards() *BulkDocumentResponse_Shards {
	if x != nil {
		return x.Shards
	}
	return nil
}

var File_backend_proto_search_v1_document_proto protoreflect.FileDescriptor

const file_backend_proto_search_v1_document_proto_rawDesc = "" +
	"\n" +
	"&backend/proto/search/v1/document.proto\x12\x17backend.proto.search.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\"\xf0\x03\n" +
	"\x13BulkDocumentRequest\x12j\n" +
	"\n" +
	"operations\x18\x01 \x03(\v2:.backend.proto.search.v1.BulkDocumentRequest.BulkOperationB\x0e\xe0A\x02\xbaH\b\x92\x01\x05\b\x01\x10\x88'R\n" +
	"operations\x1a\xec\x02\n" +
	"\rBulkOperation\x12\\\n" +
	"\x0eoperation_type\x18\x01 \x01(\x0e2&.backend.proto.search.v1.OperationTypeB\r\xe0A\x02\xbaH\a\x82\x01\x04\x10\x01 \x00R\roperationType\x12h\n" +
	"\x06target\x18\x02 \x01(\v2E.backend.proto.search.v1.BulkDocumentRequest.BulkOperation.BulkTargetB\t\xe0A\x02\xbaH\x03\xc8\x01\x01R\x06target\x128\n" +
	"\bdocument\x18\x03 \x01(\v2\x17.google.protobuf.StructH\x00R\bdocument\x88\x01\x01\x1aL\n" +
	"\n" +
	"BulkTarget\x12\"\n" +
	"\x05index\x18\x01 \x01(\tB\f\xe0A\x02\xbaH\x06r\x04\x10\x01\x18dR\x05index\x12\x13\n" +
	"\x02id\x18\x02 \x01(\tH\x00R\x02id\x88\x01\x01B\x05\n" +
	"\x03_idB\v\n" +
	"\t_document\"\xd1\x05\n" +
	"\x14BulkDocumentResponse\x12\x1d\n" +
	"\n" +
	"has_errors\x18\x01 \x01(\bR\thasErrors\x12W\n" +
	"\aresults\x18\x02 \x03(\v2=.backend.proto.search.v1.BulkDocumentResponse.OperationResultR\aresults\x12\x12\n" +
	"\x04took\x18\x03 \x01(\x05R\x04took\x1aI\n" +
	"\x05Error\x12\x1d\n" +
	"\n" +
	"error_type\x18\x01 \x01(\tR\terrorType\x12!\n" +
	"\ferror_reason\x18\x02 \x01(\tR\verrorReason\x1aV\n" +
	"\x06Shards\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x05R\x05total\x12\x1e\n" +
	"\n" +
	"successful\x18\x02 \x01(\x05R\n" +
	"successful\x12\x16\n" +
	"\x06failed\x18\x03 \x01(\x05R\x06failed\x1a\x89\x03\n" +
	"\x0fOperationResult\x12M\n" +
	"\x0eoperation_type\x18\x01 \x01(\x0e2&.backend.proto.search.v1.OperationTypeR\roperationType\x12\x14\n" +
	"\x05index\x18\x02 \x01(\tR\x05index\x12\x1f\n" +
	"\vdocument_id\x18\x03 \x01(\tR\n" +
	"documentId\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\x12\x16\n" +
	"\x06result\x18\x05 \x01(\tR\x06result\x12\x18\n" +
	"\aversion\x18\x06 \x01(\x05R\aversion\x12N\n" +
	"\x05error\x18\a \x01(\v23.backend.proto.search.v1.BulkDocumentResponse.ErrorH\x00R\x05error\x88\x01\x01\x12L\n" +
	"\x06shards\x18\b \x01(\v24.backend.proto.search.v1.BulkDocumentResponse.ShardsR\x06shardsB\b\n" +
	"\x06_error*^\n" +
	"\rOperationType\x12\x1e\n" +
	"\x1aOPERATION_TYPE_UNSPECIFIED\x10\x00\x12\t\n" +
	"\x05INDEX\x10\x01\x12\n" +
	"\n" +
	"\x06CREATE\x10\x02\x12\n" +
	"\n" +
	"\x06UPDATE\x10\x03\x12\n" +
	"\n" +
	"\x06DELETE\x10\x04Be\n" +
	"!com.moego.backend.proto.search.v1P\x01Z>github.com/MoeGolibrary/moego/backend/proto/search/v1;searchpbb\x06proto3"

var (
	file_backend_proto_search_v1_document_proto_rawDescOnce sync.Once
	file_backend_proto_search_v1_document_proto_rawDescData []byte
)

func file_backend_proto_search_v1_document_proto_rawDescGZIP() []byte {
	file_backend_proto_search_v1_document_proto_rawDescOnce.Do(func() {
		file_backend_proto_search_v1_document_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_search_v1_document_proto_rawDesc), len(file_backend_proto_search_v1_document_proto_rawDesc)))
	})
	return file_backend_proto_search_v1_document_proto_rawDescData
}

var file_backend_proto_search_v1_document_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_search_v1_document_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_backend_proto_search_v1_document_proto_goTypes = []any{
	(OperationType)(0),                                   // 0: backend.proto.search.v1.OperationType
	(*BulkDocumentRequest)(nil),                          // 1: backend.proto.search.v1.BulkDocumentRequest
	(*BulkDocumentResponse)(nil),                         // 2: backend.proto.search.v1.BulkDocumentResponse
	(*BulkDocumentRequest_BulkOperation)(nil),            // 3: backend.proto.search.v1.BulkDocumentRequest.BulkOperation
	(*BulkDocumentRequest_BulkOperation_BulkTarget)(nil), // 4: backend.proto.search.v1.BulkDocumentRequest.BulkOperation.BulkTarget
	(*BulkDocumentResponse_Error)(nil),                   // 5: backend.proto.search.v1.BulkDocumentResponse.Error
	(*BulkDocumentResponse_Shards)(nil),                  // 6: backend.proto.search.v1.BulkDocumentResponse.Shards
	(*BulkDocumentResponse_OperationResult)(nil),         // 7: backend.proto.search.v1.BulkDocumentResponse.OperationResult
	(*structpb.Struct)(nil),                              // 8: google.protobuf.Struct
}
var file_backend_proto_search_v1_document_proto_depIdxs = []int32{
	3, // 0: backend.proto.search.v1.BulkDocumentRequest.operations:type_name -> backend.proto.search.v1.BulkDocumentRequest.BulkOperation
	7, // 1: backend.proto.search.v1.BulkDocumentResponse.results:type_name -> backend.proto.search.v1.BulkDocumentResponse.OperationResult
	0, // 2: backend.proto.search.v1.BulkDocumentRequest.BulkOperation.operation_type:type_name -> backend.proto.search.v1.OperationType
	4, // 3: backend.proto.search.v1.BulkDocumentRequest.BulkOperation.target:type_name -> backend.proto.search.v1.BulkDocumentRequest.BulkOperation.BulkTarget
	8, // 4: backend.proto.search.v1.BulkDocumentRequest.BulkOperation.document:type_name -> google.protobuf.Struct
	0, // 5: backend.proto.search.v1.BulkDocumentResponse.OperationResult.operation_type:type_name -> backend.proto.search.v1.OperationType
	5, // 6: backend.proto.search.v1.BulkDocumentResponse.OperationResult.error:type_name -> backend.proto.search.v1.BulkDocumentResponse.Error
	6, // 7: backend.proto.search.v1.BulkDocumentResponse.OperationResult.shards:type_name -> backend.proto.search.v1.BulkDocumentResponse.Shards
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_backend_proto_search_v1_document_proto_init() }
func file_backend_proto_search_v1_document_proto_init() {
	if File_backend_proto_search_v1_document_proto != nil {
		return
	}
	file_backend_proto_search_v1_document_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_search_v1_document_proto_msgTypes[3].OneofWrappers = []any{}
	file_backend_proto_search_v1_document_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_search_v1_document_proto_rawDesc), len(file_backend_proto_search_v1_document_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_search_v1_document_proto_goTypes,
		DependencyIndexes: file_backend_proto_search_v1_document_proto_depIdxs,
		EnumInfos:         file_backend_proto_search_v1_document_proto_enumTypes,
		MessageInfos:      file_backend_proto_search_v1_document_proto_msgTypes,
	}.Build()
	File_backend_proto_search_v1_document_proto = out.File
	file_backend_proto_search_v1_document_proto_goTypes = nil
	file_backend_proto_search_v1_document_proto_depIdxs = nil
}
