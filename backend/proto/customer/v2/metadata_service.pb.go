// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/customer/v2/metadata_service.proto

package customerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	phone_number "google.golang.org/genproto/googleapis/type/phone_number"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/fieldmaskpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// customer
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 119500
	// 客户不存在
	ErrCode_ERR_CODE_CUSTOMER_NOT_FOUND ErrCode = 119501
	// 客户已存在
	ErrCode_ERR_CODE_CUSTOMER_ALREADY_EXISTS ErrCode = 119502
	// 无效的客户ID
	ErrCode_ERR_CODE_INVALID_CUSTOMER_ID ErrCode = 119503
	// 无效的客户名称
	ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME ErrCode = 119504
	// 客户已删除
	ErrCode_ERR_CODE_CUSTOMER_DELETED ErrCode = 119505
	// 创建客户失败
	ErrCode_ERR_CODE_CREATE_CUSTOMER_FAILED ErrCode = 119506
	// address
	// 地址不存在
	ErrCode_ERR_CODE_ADDRESS_NOT_FOUND ErrCode = 119510
	// 无效的地址信息
	ErrCode_ERR_CODE_INVALID_ADDRESS ErrCode = 119511
	// 超出地址数量限制
	ErrCode_ERR_CODE_ADDRESS_LIMIT_EXCEEDED ErrCode = 119512
	// 不允许重复设置主地址
	ErrCode_ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS ErrCode = 119513
	// task
	// 任务不存在
	ErrCode_ERR_CODE_TASK_NOT_FOUND ErrCode = 119520
	// 任务已完成
	ErrCode_ERR_CODE_TASK_ALREADY_COMPLETED ErrCode = 119521
	// 无效的任务状态
	ErrCode_ERR_CODE_INVALID_TASK_STATUS ErrCode = 119522
	// source
	// Action State Name 已经存在
	ErrCode_ERR_CODE_ACTION_STATE_NAME_EXIST ErrCode = 119525
	// Life Cycle Name 已经存在
	ErrCode_ERR_CODE_LIFE_CYCLE_NAME_EXIST ErrCode = 119526
	// View Name 已经存在
	ErrCode_ERR_CODE_VIEW_NAME_EXIST ErrCode = 119527
	// Source Name 已经存在
	ErrCode_ERR_CODE_SOURCE_NAME_EXIST ErrCode = 119528
	// Tag Name 已经存在
	ErrCode_ERR_CODE_TAG_NAME_EXIST ErrCode = 119529
	// contact
	// 联系人不存在
	ErrCode_ERR_CODE_CONTACT_NOT_FOUND ErrCode = 119535
	// 联系人已存在
	ErrCode_ERR_CODE_CONTACT_ALREADY_EXISTS ErrCode = 119536
	// 无效的联系人信息
	ErrCode_ERR_CODE_INVALID_CONTACT ErrCode = 119537
	// 更新的内容与该联系方式记录的内容不符 (即contact记录email就只能更新email)
	ErrCode_ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH ErrCode = 119538
	// lead
	// 线索不存在
	ErrCode_ERR_CODE_LEAD_NOT_FOUND ErrCode = 119540
	// 创建线索失败
	ErrCode_ERR_CODE_CREATE_LEAD_FAILED ErrCode = 119541
	// contact tag
	// 标签不存在
	ErrCode_ERR_CODE_CONTACT_TAG_NOT_FOUND ErrCode = 119545
	// 创建标签失败
	ErrCode_ERR_CODE_CREATE_CONTACT_TAG_FAILED ErrCode = 119546
	// custom field
	// 自定义字段不存在
	ErrCode_ERR_CODE_CUSTOM_FIELD_NOT_FOUND ErrCode = 119550
	// 创建自定义字段失败
	ErrCode_ERR_CODE_CREATE_CUSTOM_FIELD_FAILED ErrCode = 119551
	// 自定义字段已存在
	ErrCode_ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS ErrCode = 119552
	// 自定义字段已删除
	ErrCode_ERR_CODE_CUSTOM_FIELD_DELETED ErrCode = 119553
	// 自定义字段选项不存在
	ErrCode_ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND ErrCode = 119554
	// 自定义字段选项已删除
	ErrCode_ERR_CODE_CUSTOM_FIELD_OPTION_DELETED ErrCode = 119555
	// customer related data
	// 客户关联数据不存在
	ErrCode_ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND ErrCode = 119560
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		119500: "ERR_CODE_UNSPECIFIED",
		119501: "ERR_CODE_CUSTOMER_NOT_FOUND",
		119502: "ERR_CODE_CUSTOMER_ALREADY_EXISTS",
		119503: "ERR_CODE_INVALID_CUSTOMER_ID",
		119504: "ERR_CODE_INVALID_CUSTOMER_NAME",
		119505: "ERR_CODE_CUSTOMER_DELETED",
		119506: "ERR_CODE_CREATE_CUSTOMER_FAILED",
		119510: "ERR_CODE_ADDRESS_NOT_FOUND",
		119511: "ERR_CODE_INVALID_ADDRESS",
		119512: "ERR_CODE_ADDRESS_LIMIT_EXCEEDED",
		119513: "ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS",
		119520: "ERR_CODE_TASK_NOT_FOUND",
		119521: "ERR_CODE_TASK_ALREADY_COMPLETED",
		119522: "ERR_CODE_INVALID_TASK_STATUS",
		119525: "ERR_CODE_ACTION_STATE_NAME_EXIST",
		119526: "ERR_CODE_LIFE_CYCLE_NAME_EXIST",
		119527: "ERR_CODE_VIEW_NAME_EXIST",
		119528: "ERR_CODE_SOURCE_NAME_EXIST",
		119529: "ERR_CODE_TAG_NAME_EXIST",
		119535: "ERR_CODE_CONTACT_NOT_FOUND",
		119536: "ERR_CODE_CONTACT_ALREADY_EXISTS",
		119537: "ERR_CODE_INVALID_CONTACT",
		119538: "ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH",
		119540: "ERR_CODE_LEAD_NOT_FOUND",
		119541: "ERR_CODE_CREATE_LEAD_FAILED",
		119545: "ERR_CODE_CONTACT_TAG_NOT_FOUND",
		119546: "ERR_CODE_CREATE_CONTACT_TAG_FAILED",
		119550: "ERR_CODE_CUSTOM_FIELD_NOT_FOUND",
		119551: "ERR_CODE_CREATE_CUSTOM_FIELD_FAILED",
		119552: "ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS",
		119553: "ERR_CODE_CUSTOM_FIELD_DELETED",
		119554: "ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND",
		119555: "ERR_CODE_CUSTOM_FIELD_OPTION_DELETED",
		119560: "ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":                               0,
		"ERR_CODE_UNSPECIFIED":                      119500,
		"ERR_CODE_CUSTOMER_NOT_FOUND":               119501,
		"ERR_CODE_CUSTOMER_ALREADY_EXISTS":          119502,
		"ERR_CODE_INVALID_CUSTOMER_ID":              119503,
		"ERR_CODE_INVALID_CUSTOMER_NAME":            119504,
		"ERR_CODE_CUSTOMER_DELETED":                 119505,
		"ERR_CODE_CREATE_CUSTOMER_FAILED":           119506,
		"ERR_CODE_ADDRESS_NOT_FOUND":                119510,
		"ERR_CODE_INVALID_ADDRESS":                  119511,
		"ERR_CODE_ADDRESS_LIMIT_EXCEEDED":           119512,
		"ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS":    119513,
		"ERR_CODE_TASK_NOT_FOUND":                   119520,
		"ERR_CODE_TASK_ALREADY_COMPLETED":           119521,
		"ERR_CODE_INVALID_TASK_STATUS":              119522,
		"ERR_CODE_ACTION_STATE_NAME_EXIST":          119525,
		"ERR_CODE_LIFE_CYCLE_NAME_EXIST":            119526,
		"ERR_CODE_VIEW_NAME_EXIST":                  119527,
		"ERR_CODE_SOURCE_NAME_EXIST":                119528,
		"ERR_CODE_TAG_NAME_EXIST":                   119529,
		"ERR_CODE_CONTACT_NOT_FOUND":                119535,
		"ERR_CODE_CONTACT_ALREADY_EXISTS":           119536,
		"ERR_CODE_INVALID_CONTACT":                  119537,
		"ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH": 119538,
		"ERR_CODE_LEAD_NOT_FOUND":                   119540,
		"ERR_CODE_CREATE_LEAD_FAILED":               119541,
		"ERR_CODE_CONTACT_TAG_NOT_FOUND":            119545,
		"ERR_CODE_CREATE_CONTACT_TAG_FAILED":        119546,
		"ERR_CODE_CUSTOM_FIELD_NOT_FOUND":           119550,
		"ERR_CODE_CREATE_CUSTOM_FIELD_FAILED":       119551,
		"ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS":      119552,
		"ERR_CODE_CUSTOM_FIELD_DELETED":             119553,
		"ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND":    119554,
		"ERR_CODE_CUSTOM_FIELD_OPTION_DELETED":      119555,
		"ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND":  119560,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{0}
}

// sorting field
type ListCustomersRequest_Sorting_Field int32

const (
	// 默认排序
	ListCustomersRequest_Sorting_FIELD_UNSPECIFIED ListCustomersRequest_Sorting_Field = 0
	// 客户ID
	ListCustomersRequest_Sorting_ID ListCustomersRequest_Sorting_Field = 1
	// 创建时间
	ListCustomersRequest_Sorting_CREATED_TIME ListCustomersRequest_Sorting_Field = 2
	// 更新时间
	ListCustomersRequest_Sorting_UPDATED_TIME ListCustomersRequest_Sorting_Field = 3
)

// Enum value maps for ListCustomersRequest_Sorting_Field.
var (
	ListCustomersRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListCustomersRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListCustomersRequest_Sorting_Field) Enum() *ListCustomersRequest_Sorting_Field {
	p := new(ListCustomersRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListCustomersRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomersRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[1].Descriptor()
}

func (ListCustomersRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[1]
}

func (x ListCustomersRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomersRequest_Sorting_Field.Descriptor instead.
func (ListCustomersRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{2, 1, 0}
}

// sorting direction
type ListCustomersRequest_Sorting_Direction int32

const (
	// 默认排序
	ListCustomersRequest_Sorting_DIRECTION_UNSPECIFIED ListCustomersRequest_Sorting_Direction = 0
	// 升序
	ListCustomersRequest_Sorting_ASC ListCustomersRequest_Sorting_Direction = 1
	// 降序
	ListCustomersRequest_Sorting_DESC ListCustomersRequest_Sorting_Direction = 2
)

// Enum value maps for ListCustomersRequest_Sorting_Direction.
var (
	ListCustomersRequest_Sorting_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	ListCustomersRequest_Sorting_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"ASC":                   1,
		"DESC":                  2,
	}
)

func (x ListCustomersRequest_Sorting_Direction) Enum() *ListCustomersRequest_Sorting_Direction {
	p := new(ListCustomersRequest_Sorting_Direction)
	*p = x
	return p
}

func (x ListCustomersRequest_Sorting_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomersRequest_Sorting_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[2].Descriptor()
}

func (ListCustomersRequest_Sorting_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[2]
}

func (x ListCustomersRequest_Sorting_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomersRequest_Sorting_Direction.Descriptor instead.
func (ListCustomersRequest_Sorting_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{2, 1, 1}
}

// 排序字段
type ListContactsRequest_Sorting_Field int32

const (
	// 默认排序
	ListContactsRequest_Sorting_FIELD_UNSPECIFIED ListContactsRequest_Sorting_Field = 0
	// 联系人ID
	ListContactsRequest_Sorting_ID ListContactsRequest_Sorting_Field = 1
	// 创建时间
	ListContactsRequest_Sorting_CREATED_TIME ListContactsRequest_Sorting_Field = 2
	// 更新时间
	ListContactsRequest_Sorting_UPDATED_TIME ListContactsRequest_Sorting_Field = 3
)

// Enum value maps for ListContactsRequest_Sorting_Field.
var (
	ListContactsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListContactsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListContactsRequest_Sorting_Field) Enum() *ListContactsRequest_Sorting_Field {
	p := new(ListContactsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListContactsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListContactsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[3].Descriptor()
}

func (ListContactsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[3]
}

func (x ListContactsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListContactsRequest_Sorting_Field.Descriptor instead.
func (ListContactsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{8, 1, 0}
}

// 排序方向
type ListContactsRequest_Sorting_Direction int32

const (
	// 默认排序
	ListContactsRequest_Sorting_DIRECTION_UNSPECIFIED ListContactsRequest_Sorting_Direction = 0
	// 升序
	ListContactsRequest_Sorting_ASC ListContactsRequest_Sorting_Direction = 1
	// 降序
	ListContactsRequest_Sorting_DESC ListContactsRequest_Sorting_Direction = 2
)

// Enum value maps for ListContactsRequest_Sorting_Direction.
var (
	ListContactsRequest_Sorting_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	ListContactsRequest_Sorting_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"ASC":                   1,
		"DESC":                  2,
	}
)

func (x ListContactsRequest_Sorting_Direction) Enum() *ListContactsRequest_Sorting_Direction {
	p := new(ListContactsRequest_Sorting_Direction)
	*p = x
	return p
}

func (x ListContactsRequest_Sorting_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListContactsRequest_Sorting_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[4].Descriptor()
}

func (ListContactsRequest_Sorting_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[4]
}

func (x ListContactsRequest_Sorting_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListContactsRequest_Sorting_Direction.Descriptor instead.
func (ListContactsRequest_Sorting_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{8, 1, 1}
}

// 排序字段
type ListCustomerRelatedDataRequest_Sorting_Field int32

const (
	// 默认排序
	ListCustomerRelatedDataRequest_Sorting_FIELD_UNSPECIFIED ListCustomerRelatedDataRequest_Sorting_Field = 0
	// 客户相关数据ID
	ListCustomerRelatedDataRequest_Sorting_ID ListCustomerRelatedDataRequest_Sorting_Field = 1
	// 创建时间
	ListCustomerRelatedDataRequest_Sorting_CREATED_TIME ListCustomerRelatedDataRequest_Sorting_Field = 2
	// 更新时间
	ListCustomerRelatedDataRequest_Sorting_UPDATED_TIME ListCustomerRelatedDataRequest_Sorting_Field = 3
)

// Enum value maps for ListCustomerRelatedDataRequest_Sorting_Field.
var (
	ListCustomerRelatedDataRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListCustomerRelatedDataRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListCustomerRelatedDataRequest_Sorting_Field) Enum() *ListCustomerRelatedDataRequest_Sorting_Field {
	p := new(ListCustomerRelatedDataRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListCustomerRelatedDataRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomerRelatedDataRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[5].Descriptor()
}

func (ListCustomerRelatedDataRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[5]
}

func (x ListCustomerRelatedDataRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomerRelatedDataRequest_Sorting_Field.Descriptor instead.
func (ListCustomerRelatedDataRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{14, 1, 0}
}

// 排序方向
type ListCustomerRelatedDataRequest_Sorting_Direction int32

const (
	// 默认排序
	ListCustomerRelatedDataRequest_Sorting_DIRECTION_UNSPECIFIED ListCustomerRelatedDataRequest_Sorting_Direction = 0
	// 升序
	ListCustomerRelatedDataRequest_Sorting_ASC ListCustomerRelatedDataRequest_Sorting_Direction = 1
	// 降序
	ListCustomerRelatedDataRequest_Sorting_DESC ListCustomerRelatedDataRequest_Sorting_Direction = 2
)

// Enum value maps for ListCustomerRelatedDataRequest_Sorting_Direction.
var (
	ListCustomerRelatedDataRequest_Sorting_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	ListCustomerRelatedDataRequest_Sorting_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"ASC":                   1,
		"DESC":                  2,
	}
)

func (x ListCustomerRelatedDataRequest_Sorting_Direction) Enum() *ListCustomerRelatedDataRequest_Sorting_Direction {
	p := new(ListCustomerRelatedDataRequest_Sorting_Direction)
	*p = x
	return p
}

func (x ListCustomerRelatedDataRequest_Sorting_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomerRelatedDataRequest_Sorting_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[6].Descriptor()
}

func (ListCustomerRelatedDataRequest_Sorting_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[6]
}

func (x ListCustomerRelatedDataRequest_Sorting_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomerRelatedDataRequest_Sorting_Direction.Descriptor instead.
func (ListCustomerRelatedDataRequest_Sorting_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{14, 1, 1}
}

// 排序字段
type ListContactTagsRequest_Sorting_Field int32

const (
	// 默认排序
	ListContactTagsRequest_Sorting_FIELD_UNSPECIFIED ListContactTagsRequest_Sorting_Field = 0
	// 标签ID
	ListContactTagsRequest_Sorting_ID ListContactTagsRequest_Sorting_Field = 1
	// 创建时间
	ListContactTagsRequest_Sorting_CREATED_TIME ListContactTagsRequest_Sorting_Field = 2
	// 更新时间
	ListContactTagsRequest_Sorting_UPDATED_TIME ListContactTagsRequest_Sorting_Field = 3
)

// Enum value maps for ListContactTagsRequest_Sorting_Field.
var (
	ListContactTagsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListContactTagsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListContactTagsRequest_Sorting_Field) Enum() *ListContactTagsRequest_Sorting_Field {
	p := new(ListContactTagsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListContactTagsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListContactTagsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[7].Descriptor()
}

func (ListContactTagsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[7]
}

func (x ListContactTagsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListContactTagsRequest_Sorting_Field.Descriptor instead.
func (ListContactTagsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{20, 1, 0}
}

// 排序方向
type ListContactTagsRequest_Sorting_Direction int32

const (
	// 默认排序
	ListContactTagsRequest_Sorting_DIRECTION_UNSPECIFIED ListContactTagsRequest_Sorting_Direction = 0
	// 升序
	ListContactTagsRequest_Sorting_ASC ListContactTagsRequest_Sorting_Direction = 1
	// 降序
	ListContactTagsRequest_Sorting_DESC ListContactTagsRequest_Sorting_Direction = 2
)

// Enum value maps for ListContactTagsRequest_Sorting_Direction.
var (
	ListContactTagsRequest_Sorting_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	ListContactTagsRequest_Sorting_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"ASC":                   1,
		"DESC":                  2,
	}
)

func (x ListContactTagsRequest_Sorting_Direction) Enum() *ListContactTagsRequest_Sorting_Direction {
	p := new(ListContactTagsRequest_Sorting_Direction)
	*p = x
	return p
}

func (x ListContactTagsRequest_Sorting_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListContactTagsRequest_Sorting_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[8].Descriptor()
}

func (ListContactTagsRequest_Sorting_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[8]
}

func (x ListContactTagsRequest_Sorting_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListContactTagsRequest_Sorting_Direction.Descriptor instead.
func (ListContactTagsRequest_Sorting_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{20, 1, 1}
}

// 排序字段
type ListLeadsRequest_Sorting_Field int32

const (
	// 默认排序
	ListLeadsRequest_Sorting_FIELD_UNSPECIFIED ListLeadsRequest_Sorting_Field = 0
	// 线索ID
	ListLeadsRequest_Sorting_ID ListLeadsRequest_Sorting_Field = 1
	// 创建时间
	ListLeadsRequest_Sorting_CREATED_TIME ListLeadsRequest_Sorting_Field = 2
	// 更新时间
	ListLeadsRequest_Sorting_UPDATED_TIME ListLeadsRequest_Sorting_Field = 3
)

// Enum value maps for ListLeadsRequest_Sorting_Field.
var (
	ListLeadsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListLeadsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListLeadsRequest_Sorting_Field) Enum() *ListLeadsRequest_Sorting_Field {
	p := new(ListLeadsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListLeadsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListLeadsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[9].Descriptor()
}

func (ListLeadsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[9]
}

func (x ListLeadsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListLeadsRequest_Sorting_Field.Descriptor instead.
func (ListLeadsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{26, 1, 0}
}

// 排序方向
type ListLeadsRequest_Sorting_Direction int32

const (
	// 默认排序
	ListLeadsRequest_Sorting_DIRECTION_UNSPECIFIED ListLeadsRequest_Sorting_Direction = 0
	// 升序
	ListLeadsRequest_Sorting_ASC ListLeadsRequest_Sorting_Direction = 1
	// 降序
	ListLeadsRequest_Sorting_DESC ListLeadsRequest_Sorting_Direction = 2
)

// Enum value maps for ListLeadsRequest_Sorting_Direction.
var (
	ListLeadsRequest_Sorting_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	ListLeadsRequest_Sorting_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"ASC":                   1,
		"DESC":                  2,
	}
)

func (x ListLeadsRequest_Sorting_Direction) Enum() *ListLeadsRequest_Sorting_Direction {
	p := new(ListLeadsRequest_Sorting_Direction)
	*p = x
	return p
}

func (x ListLeadsRequest_Sorting_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListLeadsRequest_Sorting_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[10].Descriptor()
}

func (ListLeadsRequest_Sorting_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[10]
}

func (x ListLeadsRequest_Sorting_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListLeadsRequest_Sorting_Direction.Descriptor instead.
func (ListLeadsRequest_Sorting_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{26, 1, 1}
}

// 排序字段
type ListAddressesRequest_Sorting_Field int32

const (
	// 默认排序
	ListAddressesRequest_Sorting_FIELD_UNSPECIFIED ListAddressesRequest_Sorting_Field = 0
	// 地址ID
	ListAddressesRequest_Sorting_ID ListAddressesRequest_Sorting_Field = 1
	// 创建时间
	ListAddressesRequest_Sorting_CREATED_TIME ListAddressesRequest_Sorting_Field = 2
	// 更新时间
	ListAddressesRequest_Sorting_UPDATED_TIME ListAddressesRequest_Sorting_Field = 3
)

// Enum value maps for ListAddressesRequest_Sorting_Field.
var (
	ListAddressesRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListAddressesRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListAddressesRequest_Sorting_Field) Enum() *ListAddressesRequest_Sorting_Field {
	p := new(ListAddressesRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListAddressesRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAddressesRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[11].Descriptor()
}

func (ListAddressesRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[11]
}

func (x ListAddressesRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAddressesRequest_Sorting_Field.Descriptor instead.
func (ListAddressesRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{32, 1, 0}
}

// 排序方向
type ListAddressesRequest_Sorting_Direction int32

const (
	// 默认排序
	ListAddressesRequest_Sorting_DIRECTION_UNSPECIFIED ListAddressesRequest_Sorting_Direction = 0
	// 升序
	ListAddressesRequest_Sorting_ASC ListAddressesRequest_Sorting_Direction = 1
	// 降序
	ListAddressesRequest_Sorting_DESC ListAddressesRequest_Sorting_Direction = 2
)

// Enum value maps for ListAddressesRequest_Sorting_Direction.
var (
	ListAddressesRequest_Sorting_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	ListAddressesRequest_Sorting_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"ASC":                   1,
		"DESC":                  2,
	}
)

func (x ListAddressesRequest_Sorting_Direction) Enum() *ListAddressesRequest_Sorting_Direction {
	p := new(ListAddressesRequest_Sorting_Direction)
	*p = x
	return p
}

func (x ListAddressesRequest_Sorting_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAddressesRequest_Sorting_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[12].Descriptor()
}

func (ListAddressesRequest_Sorting_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[12]
}

func (x ListAddressesRequest_Sorting_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAddressesRequest_Sorting_Direction.Descriptor instead.
func (ListAddressesRequest_Sorting_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{32, 1, 1}
}

// 排序字段
type ListCustomFieldsRequest_Sorting_Field int32

const (
	// 默认排序
	ListCustomFieldsRequest_Sorting_FIELD_UNSPECIFIED ListCustomFieldsRequest_Sorting_Field = 0
	// 自定义字段ID
	ListCustomFieldsRequest_Sorting_ID ListCustomFieldsRequest_Sorting_Field = 1
	// 创建时间
	ListCustomFieldsRequest_Sorting_CREATED_TIME ListCustomFieldsRequest_Sorting_Field = 2
	// 更新时间
	ListCustomFieldsRequest_Sorting_UPDATED_TIME ListCustomFieldsRequest_Sorting_Field = 3
)

// Enum value maps for ListCustomFieldsRequest_Sorting_Field.
var (
	ListCustomFieldsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListCustomFieldsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListCustomFieldsRequest_Sorting_Field) Enum() *ListCustomFieldsRequest_Sorting_Field {
	p := new(ListCustomFieldsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListCustomFieldsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomFieldsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[13].Descriptor()
}

func (ListCustomFieldsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[13]
}

func (x ListCustomFieldsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomFieldsRequest_Sorting_Field.Descriptor instead.
func (ListCustomFieldsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{38, 1, 0}
}

// 排序方向
type ListCustomFieldsRequest_Sorting_Direction int32

const (
	// 默认排序
	ListCustomFieldsRequest_Sorting_DIRECTION_UNSPECIFIED ListCustomFieldsRequest_Sorting_Direction = 0
	// 升序
	ListCustomFieldsRequest_Sorting_ASC ListCustomFieldsRequest_Sorting_Direction = 1
	// 降序
	ListCustomFieldsRequest_Sorting_DESC ListCustomFieldsRequest_Sorting_Direction = 2
)

// Enum value maps for ListCustomFieldsRequest_Sorting_Direction.
var (
	ListCustomFieldsRequest_Sorting_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	ListCustomFieldsRequest_Sorting_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"ASC":                   1,
		"DESC":                  2,
	}
)

func (x ListCustomFieldsRequest_Sorting_Direction) Enum() *ListCustomFieldsRequest_Sorting_Direction {
	p := new(ListCustomFieldsRequest_Sorting_Direction)
	*p = x
	return p
}

func (x ListCustomFieldsRequest_Sorting_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomFieldsRequest_Sorting_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[14].Descriptor()
}

func (ListCustomFieldsRequest_Sorting_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[14]
}

func (x ListCustomFieldsRequest_Sorting_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomFieldsRequest_Sorting_Direction.Descriptor instead.
func (ListCustomFieldsRequest_Sorting_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{38, 1, 1}
}

// ==================== Customer Request/Response Messages ====================
// CreateCustomerRequest 创建客户请求
type CreateCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户信息
	Customer      *Customer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerRequest) Reset() {
	*x = CreateCustomerRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerRequest) ProtoMessage() {}

func (x *CreateCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCustomerRequest) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

// GetCustomerRequest 获取客户请求
type GetCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerRequest) Reset() {
	*x = GetCustomerRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRequest) ProtoMessage() {}

func (x *GetCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCustomerRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ListCustomersRequest 列出客户请求
type ListCustomersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomersRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListCustomersRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// 是否返回总数量, 默认不返回, 会影响性能
	ReturnTotalSize bool `protobuf:"varint,5,opt,name=return_total_size,json=returnTotalSize,proto3" json:"return_total_size,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListCustomersRequest) Reset() {
	*x = ListCustomersRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest) ProtoMessage() {}

func (x *ListCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListCustomersRequest) GetFilter() *ListCustomersRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomersRequest) GetSorting() *ListCustomersRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListCustomersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCustomersRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListCustomersRequest) GetReturnTotalSize() bool {
	if x != nil {
		return x.ReturnTotalSize
	}
	return false
}

// ListCustomersResponse 列出客户响应
type ListCustomersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户列表
	Customers []*Customer `protobuf:"bytes,1,rep,name=customers,proto3" json:"customers,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量, 需要设置 return_total_size 为 true 才会返回
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomersResponse) Reset() {
	*x = ListCustomersResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersResponse) ProtoMessage() {}

func (x *ListCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersResponse.ProtoReflect.Descriptor instead.
func (*ListCustomersResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListCustomersResponse) GetCustomers() []*Customer {
	if x != nil {
		return x.Customers
	}
	return nil
}

func (x *ListCustomersResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListCustomersResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateCustomerRequest 更新客户请求
type UpdateCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ref
	Ref           *UpdateCustomerRequest_UpdateRef `protobuf:"bytes,2,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerRequest) Reset() {
	*x = UpdateCustomerRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRequest) ProtoMessage() {}

func (x *UpdateCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateCustomerRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerRequest) GetRef() *UpdateCustomerRequest_UpdateRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

// DeleteCustomerRequest 删除客户请求
type DeleteCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerRequest) Reset() {
	*x = DeleteCustomerRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerRequest) ProtoMessage() {}

func (x *DeleteCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteCustomerRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ==================== Contact Request/Response Messages ====================
// CreateContactRequest 创建联系人请求
type CreateContactRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人信息
	Contact       *Contact `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateContactRequest) Reset() {
	*x = CreateContactRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateContactRequest) ProtoMessage() {}

func (x *CreateContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateContactRequest.ProtoReflect.Descriptor instead.
func (*CreateContactRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{6}
}

func (x *CreateContactRequest) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

// GetContactRequest 获取联系人请求
type GetContactRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactRequest) Reset() {
	*x = GetContactRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactRequest) ProtoMessage() {}

func (x *GetContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactRequest.ProtoReflect.Descriptor instead.
func (*GetContactRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetContactRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ListContactsRequest 列出联系人请求
type ListContactsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListContactsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListContactsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// 是否返回总数量, 默认不返回, 会影响性能
	ReturnTotalSize bool `protobuf:"varint,5,opt,name=return_total_size,json=returnTotalSize,proto3" json:"return_total_size,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListContactsRequest) Reset() {
	*x = ListContactsRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsRequest) ProtoMessage() {}

func (x *ListContactsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsRequest.ProtoReflect.Descriptor instead.
func (*ListContactsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{8}
}

func (x *ListContactsRequest) GetFilter() *ListContactsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListContactsRequest) GetSorting() *ListContactsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListContactsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListContactsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListContactsRequest) GetReturnTotalSize() bool {
	if x != nil {
		return x.ReturnTotalSize
	}
	return false
}

// ListContactsResponse 列出联系人响应
type ListContactsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人列表
	Contacts []*Contact `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量, 需要设置 return_total_size 为 true 才会返回
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactsResponse) Reset() {
	*x = ListContactsResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsResponse) ProtoMessage() {}

func (x *ListContactsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsResponse.ProtoReflect.Descriptor instead.
func (*ListContactsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListContactsResponse) GetContacts() []*Contact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *ListContactsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListContactsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateContactRequest 更新联系人请求
type UpdateContactRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ref
	Ref           *UpdateContactRequest_UpdateRef `protobuf:"bytes,2,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactRequest) Reset() {
	*x = UpdateContactRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactRequest) ProtoMessage() {}

func (x *UpdateContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactRequest.ProtoReflect.Descriptor instead.
func (*UpdateContactRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateContactRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateContactRequest) GetRef() *UpdateContactRequest_UpdateRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

// DeleteContactRequest 删除联系人请求
type DeleteContactRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteContactRequest) Reset() {
	*x = DeleteContactRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteContactRequest) ProtoMessage() {}

func (x *DeleteContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteContactRequest.ProtoReflect.Descriptor instead.
func (*DeleteContactRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteContactRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ==================== CustomerRelatedData Request/Response Messages ====================
// CreateCustomerRelatedDataRequest 创建客户相关数据请求
type CreateCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据信息
	CustomerRelatedData *CustomerRelatedData `protobuf:"bytes,1,opt,name=customer_related_data,json=customerRelatedData,proto3" json:"customer_related_data,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CreateCustomerRelatedDataRequest) Reset() {
	*x = CreateCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerRelatedDataRequest) ProtoMessage() {}

func (x *CreateCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{12}
}

func (x *CreateCustomerRelatedDataRequest) GetCustomerRelatedData() *CustomerRelatedData {
	if x != nil {
		return x.CustomerRelatedData
	}
	return nil
}

// GetCustomerRelatedDataRequest 获取客户相关数据请求
type GetCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据ID
	CustomerId    int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerRelatedDataRequest) Reset() {
	*x = GetCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRelatedDataRequest) ProtoMessage() {}

func (x *GetCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetCustomerRelatedDataRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// ListCustomerRelatedDataRequest 列出客户相关数据请求
type ListCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomerRelatedDataRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListCustomerRelatedDataRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// 是否返回总数量, 默认不返回, 会影响性能
	ReturnTotalSize bool `protobuf:"varint,5,opt,name=return_total_size,json=returnTotalSize,proto3" json:"return_total_size,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListCustomerRelatedDataRequest) Reset() {
	*x = ListCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerRelatedDataRequest) ProtoMessage() {}

func (x *ListCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{14}
}

func (x *ListCustomerRelatedDataRequest) GetFilter() *ListCustomerRelatedDataRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest) GetSorting() *ListCustomerRelatedDataRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCustomerRelatedDataRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListCustomerRelatedDataRequest) GetReturnTotalSize() bool {
	if x != nil {
		return x.ReturnTotalSize
	}
	return false
}

// ListCustomerRelatedDataResponse 列出客户相关数据响应
type ListCustomerRelatedDataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据列表
	CustomerRelatedData []*CustomerRelatedData `protobuf:"bytes,1,rep,name=customer_related_data,json=customerRelatedData,proto3" json:"customer_related_data,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量, 需要设置 return_total_size 为 true 才会返回
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerRelatedDataResponse) Reset() {
	*x = ListCustomerRelatedDataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerRelatedDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerRelatedDataResponse) ProtoMessage() {}

func (x *ListCustomerRelatedDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerRelatedDataResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerRelatedDataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListCustomerRelatedDataResponse) GetCustomerRelatedData() []*CustomerRelatedData {
	if x != nil {
		return x.CustomerRelatedData
	}
	return nil
}

func (x *ListCustomerRelatedDataResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListCustomerRelatedDataResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateCustomerRelatedDataRequest 更新客户相关数据请求
type UpdateCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ref
	Ref           *UpdateCustomerRelatedDataRequest_UpdateRef `protobuf:"bytes,2,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerRelatedDataRequest) Reset() {
	*x = UpdateCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRelatedDataRequest) ProtoMessage() {}

func (x *UpdateCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateCustomerRelatedDataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetRef() *UpdateCustomerRelatedDataRequest_UpdateRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

// DeleteCustomerRelatedDataRequest 删除客户相关数据请求
type DeleteCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerRelatedDataRequest) Reset() {
	*x = DeleteCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerRelatedDataRequest) ProtoMessage() {}

func (x *DeleteCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteCustomerRelatedDataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ==================== Contact Tag Request/Response Messages ====================
// CreateContactTagRequest 创建标签请求
type CreateContactTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签信息
	ContactTag    *ContactTag `protobuf:"bytes,1,opt,name=contact_tag,json=contactTag,proto3" json:"contact_tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateContactTagRequest) Reset() {
	*x = CreateContactTagRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateContactTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateContactTagRequest) ProtoMessage() {}

func (x *CreateContactTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateContactTagRequest.ProtoReflect.Descriptor instead.
func (*CreateContactTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{18}
}

func (x *CreateContactTagRequest) GetContactTag() *ContactTag {
	if x != nil {
		return x.ContactTag
	}
	return nil
}

// GetContactTagRequest 获取标签请求
type GetContactTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactTagRequest) Reset() {
	*x = GetContactTagRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactTagRequest) ProtoMessage() {}

func (x *GetContactTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactTagRequest.ProtoReflect.Descriptor instead.
func (*GetContactTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetContactTagRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ListContactTagsRequest 列出标签请求
type ListContactTagsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListContactTagsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListContactTagsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// 是否返回总数量, 默认不返回, 会影响性能
	ReturnTotalSize bool `protobuf:"varint,5,opt,name=return_total_size,json=returnTotalSize,proto3" json:"return_total_size,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListContactTagsRequest) Reset() {
	*x = ListContactTagsRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactTagsRequest) ProtoMessage() {}

func (x *ListContactTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactTagsRequest.ProtoReflect.Descriptor instead.
func (*ListContactTagsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{20}
}

func (x *ListContactTagsRequest) GetFilter() *ListContactTagsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListContactTagsRequest) GetSorting() *ListContactTagsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListContactTagsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListContactTagsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListContactTagsRequest) GetReturnTotalSize() bool {
	if x != nil {
		return x.ReturnTotalSize
	}
	return false
}

// ListContactTagsResponse 列出标签响应
type ListContactTagsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签列表
	ContactTags []*ContactTag `protobuf:"bytes,1,rep,name=contact_tags,json=contactTags,proto3" json:"contact_tags,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量, 需要设置 return_total_size 为 true 才会返回
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactTagsResponse) Reset() {
	*x = ListContactTagsResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactTagsResponse) ProtoMessage() {}

func (x *ListContactTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactTagsResponse.ProtoReflect.Descriptor instead.
func (*ListContactTagsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{21}
}

func (x *ListContactTagsResponse) GetContactTags() []*ContactTag {
	if x != nil {
		return x.ContactTags
	}
	return nil
}

func (x *ListContactTagsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListContactTagsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateContactTagRequest 更新标签请求
type UpdateContactTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ref
	Ref           *UpdateContactTagRequest_UpdateRef `protobuf:"bytes,2,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactTagRequest) Reset() {
	*x = UpdateContactTagRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactTagRequest) ProtoMessage() {}

func (x *UpdateContactTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactTagRequest.ProtoReflect.Descriptor instead.
func (*UpdateContactTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateContactTagRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateContactTagRequest) GetRef() *UpdateContactTagRequest_UpdateRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

// DeleteContactTagRequest 删除标签请求
type DeleteContactTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteContactTagRequest) Reset() {
	*x = DeleteContactTagRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteContactTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteContactTagRequest) ProtoMessage() {}

func (x *DeleteContactTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteContactTagRequest.ProtoReflect.Descriptor instead.
func (*DeleteContactTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteContactTagRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ==================== Lead Request/Response Messages ====================
// CreateLeadRequest 创建线索请求
type CreateLeadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索信息
	Lead          *Lead `protobuf:"bytes,1,opt,name=lead,proto3" json:"lead,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLeadRequest) Reset() {
	*x = CreateLeadRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLeadRequest) ProtoMessage() {}

func (x *CreateLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLeadRequest.ProtoReflect.Descriptor instead.
func (*CreateLeadRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{24}
}

func (x *CreateLeadRequest) GetLead() *Lead {
	if x != nil {
		return x.Lead
	}
	return nil
}

// GetLeadRequest 获取线索请求
type GetLeadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeadRequest) Reset() {
	*x = GetLeadRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeadRequest) ProtoMessage() {}

func (x *GetLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeadRequest.ProtoReflect.Descriptor instead.
func (*GetLeadRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetLeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ListLeadsRequest 列出线索请求
type ListLeadsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListLeadsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListLeadsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// 是否返回总数量, 默认不返回, 会影响性能
	ReturnTotalSize bool `protobuf:"varint,5,opt,name=return_total_size,json=returnTotalSize,proto3" json:"return_total_size,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListLeadsRequest) Reset() {
	*x = ListLeadsRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsRequest) ProtoMessage() {}

func (x *ListLeadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsRequest.ProtoReflect.Descriptor instead.
func (*ListLeadsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListLeadsRequest) GetFilter() *ListLeadsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListLeadsRequest) GetSorting() *ListLeadsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListLeadsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListLeadsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListLeadsRequest) GetReturnTotalSize() bool {
	if x != nil {
		return x.ReturnTotalSize
	}
	return false
}

// ListLeadsResponse 列出线索响应
type ListLeadsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索列表
	Leads []*Lead `protobuf:"bytes,1,rep,name=leads,proto3" json:"leads,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量, 需要设置 return_total_size 为 true 才会返回
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLeadsResponse) Reset() {
	*x = ListLeadsResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsResponse) ProtoMessage() {}

func (x *ListLeadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsResponse.ProtoReflect.Descriptor instead.
func (*ListLeadsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{27}
}

func (x *ListLeadsResponse) GetLeads() []*Lead {
	if x != nil {
		return x.Leads
	}
	return nil
}

func (x *ListLeadsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListLeadsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateLeadRequest 更新线索请求
type UpdateLeadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ref
	Ref           *UpdateLeadRequest_UpdateRef `protobuf:"bytes,2,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLeadRequest) Reset() {
	*x = UpdateLeadRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadRequest) ProtoMessage() {}

func (x *UpdateLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadRequest.ProtoReflect.Descriptor instead.
func (*UpdateLeadRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateLeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLeadRequest) GetRef() *UpdateLeadRequest_UpdateRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

// DeleteLeadRequest 删除线索请求
type DeleteLeadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteLeadRequest) Reset() {
	*x = DeleteLeadRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLeadRequest) ProtoMessage() {}

func (x *DeleteLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLeadRequest.ProtoReflect.Descriptor instead.
func (*DeleteLeadRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{29}
}

func (x *DeleteLeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ==================== Address Request/Response Messages ====================
// CreateAddressRequest 创建地址请求
type CreateAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址信息
	Address       *Address `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAddressRequest) Reset() {
	*x = CreateAddressRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAddressRequest) ProtoMessage() {}

func (x *CreateAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAddressRequest.ProtoReflect.Descriptor instead.
func (*CreateAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{30}
}

func (x *CreateAddressRequest) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

// GetAddressRequest 获取地址请求
type GetAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddressRequest) Reset() {
	*x = GetAddressRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressRequest) ProtoMessage() {}

func (x *GetAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressRequest.ProtoReflect.Descriptor instead.
func (*GetAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetAddressRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ListAddressesRequest 列出地址请求
type ListAddressesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListAddressesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListAddressesRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// 是否返回总数量, 默认不返回, 会影响性能
	ReturnTotalSize bool `protobuf:"varint,5,opt,name=return_total_size,json=returnTotalSize,proto3" json:"return_total_size,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListAddressesRequest) Reset() {
	*x = ListAddressesRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesRequest) ProtoMessage() {}

func (x *ListAddressesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesRequest.ProtoReflect.Descriptor instead.
func (*ListAddressesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{32}
}

func (x *ListAddressesRequest) GetFilter() *ListAddressesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAddressesRequest) GetSorting() *ListAddressesRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListAddressesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAddressesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListAddressesRequest) GetReturnTotalSize() bool {
	if x != nil {
		return x.ReturnTotalSize
	}
	return false
}

// ListAddressesResponse 列出地址响应
type ListAddressesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址列表
	Addresses []*Address `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量, 需要设置 return_total_size 为 true 才会返回
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressesResponse) Reset() {
	*x = ListAddressesResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesResponse) ProtoMessage() {}

func (x *ListAddressesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesResponse.ProtoReflect.Descriptor instead.
func (*ListAddressesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{33}
}

func (x *ListAddressesResponse) GetAddresses() []*Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *ListAddressesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListAddressesResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateAddressRequest 更新地址请求
type UpdateAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ref
	Ref           *UpdateAddressRequest_UpdateRef `protobuf:"bytes,2,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAddressRequest) Reset() {
	*x = UpdateAddressRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddressRequest) ProtoMessage() {}

func (x *UpdateAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddressRequest.ProtoReflect.Descriptor instead.
func (*UpdateAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{34}
}

func (x *UpdateAddressRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAddressRequest) GetRef() *UpdateAddressRequest_UpdateRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

// DeleteAddressRequest 删除地址请求
type DeleteAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAddressRequest) Reset() {
	*x = DeleteAddressRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddressRequest) ProtoMessage() {}

func (x *DeleteAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddressRequest.ProtoReflect.Descriptor instead.
func (*DeleteAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{35}
}

func (x *DeleteAddressRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ==================== Custom Field Request/Response Messages ====================
// CreateCustomFieldRequest 创建自定义字段请求
type CreateCustomFieldRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段信息
	CustomField   *CustomField `protobuf:"bytes,1,opt,name=custom_field,json=customField,proto3" json:"custom_field,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomFieldRequest) Reset() {
	*x = CreateCustomFieldRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomFieldRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomFieldRequest) ProtoMessage() {}

func (x *CreateCustomFieldRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomFieldRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomFieldRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{36}
}

func (x *CreateCustomFieldRequest) GetCustomField() *CustomField {
	if x != nil {
		return x.CustomField
	}
	return nil
}

// GetCustomFieldRequest 获取自定义字段请求
type GetCustomFieldRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomFieldRequest) Reset() {
	*x = GetCustomFieldRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomFieldRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomFieldRequest) ProtoMessage() {}

func (x *GetCustomFieldRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomFieldRequest.ProtoReflect.Descriptor instead.
func (*GetCustomFieldRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetCustomFieldRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ListCustomFieldsRequest 列出自定义字段请求
type ListCustomFieldsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomFieldsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListCustomFieldsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// 是否返回总数量, 默认不返回, 会影响性能
	ReturnTotalSize bool `protobuf:"varint,5,opt,name=return_total_size,json=returnTotalSize,proto3" json:"return_total_size,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListCustomFieldsRequest) Reset() {
	*x = ListCustomFieldsRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomFieldsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomFieldsRequest) ProtoMessage() {}

func (x *ListCustomFieldsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomFieldsRequest.ProtoReflect.Descriptor instead.
func (*ListCustomFieldsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{38}
}

func (x *ListCustomFieldsRequest) GetFilter() *ListCustomFieldsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomFieldsRequest) GetSorting() *ListCustomFieldsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListCustomFieldsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCustomFieldsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListCustomFieldsRequest) GetReturnTotalSize() bool {
	if x != nil {
		return x.ReturnTotalSize
	}
	return false
}

// ListCustomFieldsResponse 列出自定义字段响应
type ListCustomFieldsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段列表
	CustomFields []*CustomField `protobuf:"bytes,1,rep,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量, 需要设置 return_total_size 为 true 才会返回
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomFieldsResponse) Reset() {
	*x = ListCustomFieldsResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomFieldsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomFieldsResponse) ProtoMessage() {}

func (x *ListCustomFieldsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomFieldsResponse.ProtoReflect.Descriptor instead.
func (*ListCustomFieldsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{39}
}

func (x *ListCustomFieldsResponse) GetCustomFields() []*CustomField {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *ListCustomFieldsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListCustomFieldsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateCustomFieldRequest 更新自定义字段请求
type UpdateCustomFieldRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ref
	Ref           *UpdateCustomFieldRequest_UpdateRef `protobuf:"bytes,2,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomFieldRequest) Reset() {
	*x = UpdateCustomFieldRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomFieldRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomFieldRequest) ProtoMessage() {}

func (x *UpdateCustomFieldRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomFieldRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomFieldRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{40}
}

func (x *UpdateCustomFieldRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomFieldRequest) GetRef() *UpdateCustomFieldRequest_UpdateRef {
	if x != nil {
		return x.Ref
	}
	return nil
}

// DeleteCustomFieldRequest 删除自定义字段请求
type DeleteCustomFieldRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomFieldRequest) Reset() {
	*x = DeleteCustomFieldRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomFieldRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomFieldRequest) ProtoMessage() {}

func (x *DeleteCustomFieldRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomFieldRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomFieldRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{41}
}

func (x *DeleteCustomFieldRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// filter
type ListCustomersRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// customer id list
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for filtering, not for output --)
	//
	// 状态
	// 如果states为空, 则返回所有状态的客户, 没有默认值
	States []Customer_State `protobuf:"varint,3,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.Customer_State" json:"states,omitempty"`
	// 生命周期ID
	LifecycleIds []int64 `protobuf:"varint,4,rep,packed,name=lifecycle_ids,json=lifecycleIds,proto3" json:"lifecycle_ids,omitempty"`
	// 负责人员工ID
	OwnerStaffIds []int64 `protobuf:"varint,5,rep,packed,name=owner_staff_ids,json=ownerStaffIds,proto3" json:"owner_staff_ids,omitempty"`
	// 推荐来源ID
	ReferralSourceIds []int64 `protobuf:"varint,6,rep,packed,name=referral_source_ids,json=referralSourceIds,proto3" json:"referral_source_ids,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ListCustomersRequest_Filter) Reset() {
	*x = ListCustomersRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest_Filter) ProtoMessage() {}

func (x *ListCustomersRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ListCustomersRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetStates() []Customer_State {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetLifecycleIds() []int64 {
	if x != nil {
		return x.LifecycleIds
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetOwnerStaffIds() []int64 {
	if x != nil {
		return x.OwnerStaffIds
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetReferralSourceIds() []int64 {
	if x != nil {
		return x.ReferralSourceIds
	}
	return nil
}

// sorting
type ListCustomersRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListCustomersRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListCustomersRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     ListCustomersRequest_Sorting_Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.ListCustomersRequest_Sorting_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomersRequest_Sorting) Reset() {
	*x = ListCustomersRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest_Sorting) ProtoMessage() {}

func (x *ListCustomersRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{2, 1}
}

func (x *ListCustomersRequest_Sorting) GetField() ListCustomersRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListCustomersRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListCustomersRequest_Sorting) GetDirection() ListCustomersRequest_Sorting_Direction {
	if x != nil {
		return x.Direction
	}
	return ListCustomersRequest_Sorting_DIRECTION_UNSPECIFIED
}

// update ref
type UpdateCustomerRequest_UpdateRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户名称
	GivenName *string `protobuf:"bytes,1,opt,name=given_name,json=givenName,proto3,oneof" json:"given_name,omitempty"`
	// 客户姓氏
	FamilyName *string `protobuf:"bytes,2,opt,name=family_name,json=familyName,proto3,oneof" json:"family_name,omitempty"`
	// 客户自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,3,opt,name=custom_fields,json=customFields,proto3,oneof" json:"custom_fields,omitempty"`
	// 客户生命周期ID
	LifecycleId *int64 `protobuf:"varint,4,opt,name=lifecycle_id,json=lifecycleId,proto3,oneof" json:"lifecycle_id,omitempty"`
	// 负责人员工ID
	OwnerStaffId *int64 `protobuf:"varint,5,opt,name=owner_staff_id,json=ownerStaffId,proto3,oneof" json:"owner_staff_id,omitempty"`
	// 行动状态ID
	ActionStateId *int64 `protobuf:"varint,6,opt,name=action_state_id,json=actionStateId,proto3,oneof" json:"action_state_id,omitempty"`
	// 头像路径
	AvatarPath *string `protobuf:"bytes,7,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// 推荐来源ID
	ReferralSourceId *int64 `protobuf:"varint,8,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// state
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for filtering, not for output --)
	State         *Customer_State `protobuf:"varint,9,opt,name=state,proto3,enum=backend.proto.customer.v2.Customer_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerRequest_UpdateRef) Reset() {
	*x = UpdateCustomerRequest_UpdateRef{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRequest_UpdateRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRequest_UpdateRef) ProtoMessage() {}

func (x *UpdateCustomerRequest_UpdateRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRequest_UpdateRef.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRequest_UpdateRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *UpdateCustomerRequest_UpdateRef) GetGivenName() string {
	if x != nil && x.GivenName != nil {
		return *x.GivenName
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateRef) GetFamilyName() string {
	if x != nil && x.FamilyName != nil {
		return *x.FamilyName
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateRef) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *UpdateCustomerRequest_UpdateRef) GetLifecycleId() int64 {
	if x != nil && x.LifecycleId != nil {
		return *x.LifecycleId
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateRef) GetOwnerStaffId() int64 {
	if x != nil && x.OwnerStaffId != nil {
		return *x.OwnerStaffId
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateRef) GetActionStateId() int64 {
	if x != nil && x.ActionStateId != nil {
		return *x.ActionStateId
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateRef) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateRef) GetReferralSourceId() int64 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateRef) GetState() Customer_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Customer_STATE_UNSPECIFIED
}

// filter
type ListContactsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 所属客户ID列表
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for filtering, not for output --)
	//
	// 状态
	// 如果states为空, 则返回所有状态的联系人, 没有默认值
	States        []Contact_State `protobuf:"varint,3,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.Contact_State" json:"states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactsRequest_Filter) Reset() {
	*x = ListContactsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsRequest_Filter) ProtoMessage() {}

func (x *ListContactsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListContactsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListContactsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListContactsRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListContactsRequest_Filter) GetStates() []Contact_State {
	if x != nil {
		return x.States
	}
	return nil
}

// sorting
type ListContactsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListContactsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListContactsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     ListContactsRequest_Sorting_Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.ListContactsRequest_Sorting_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactsRequest_Sorting) Reset() {
	*x = ListContactsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsRequest_Sorting) ProtoMessage() {}

func (x *ListContactsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListContactsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{8, 1}
}

func (x *ListContactsRequest_Sorting) GetField() ListContactsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListContactsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListContactsRequest_Sorting) GetDirection() ListContactsRequest_Sorting_Direction {
	if x != nil {
		return x.Direction
	}
	return ListContactsRequest_Sorting_DIRECTION_UNSPECIFIED
}

// update ref
type UpdateContactRequest_UpdateRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 名字
	GivenName *string `protobuf:"bytes,1,opt,name=given_name,json=givenName,proto3,oneof" json:"given_name,omitempty"`
	// 姓氏
	FamilyName *string `protobuf:"bytes,2,opt,name=family_name,json=familyName,proto3,oneof" json:"family_name,omitempty"`
	// 邮箱
	Email *string `protobuf:"bytes,3,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// 手机号
	Phone *phone_number.PhoneNumber `protobuf:"bytes,4,opt,name=phone,proto3,oneof" json:"phone,omitempty"`
	// 标签列表
	TagIds []int64 `protobuf:"varint,5,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// 备注
	Note *string `protobuf:"bytes,6,opt,name=note,proto3,oneof" json:"note,omitempty"`
	// state
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for updating, not for output --)
	State         *Contact_State `protobuf:"varint,7,opt,name=state,proto3,enum=backend.proto.customer.v2.Contact_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactRequest_UpdateRef) Reset() {
	*x = UpdateContactRequest_UpdateRef{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactRequest_UpdateRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactRequest_UpdateRef) ProtoMessage() {}

func (x *UpdateContactRequest_UpdateRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactRequest_UpdateRef.ProtoReflect.Descriptor instead.
func (*UpdateContactRequest_UpdateRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *UpdateContactRequest_UpdateRef) GetGivenName() string {
	if x != nil && x.GivenName != nil {
		return *x.GivenName
	}
	return ""
}

func (x *UpdateContactRequest_UpdateRef) GetFamilyName() string {
	if x != nil && x.FamilyName != nil {
		return *x.FamilyName
	}
	return ""
}

func (x *UpdateContactRequest_UpdateRef) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *UpdateContactRequest_UpdateRef) GetPhone() *phone_number.PhoneNumber {
	if x != nil {
		return x.Phone
	}
	return nil
}

func (x *UpdateContactRequest_UpdateRef) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *UpdateContactRequest_UpdateRef) GetNote() string {
	if x != nil && x.Note != nil {
		return *x.Note
	}
	return ""
}

func (x *UpdateContactRequest_UpdateRef) GetState() Contact_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Contact_STATE_UNSPECIFIED
}

// filter
type ListCustomerRelatedDataRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,3,opt,name=organization,proto3" json:"organization,omitempty"`
	// 商家ID列表
	BusinessIds []int32 `protobuf:"varint,4,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// 公司ID列表
	CompanyIds []int64 `protobuf:"varint,5,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
	// 状态列表
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for filtering, not for output --)
	States        []CustomerRelatedData_State `protobuf:"varint,6,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.CustomerRelatedData_State" json:"states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerRelatedDataRequest_Filter) Reset() {
	*x = ListCustomerRelatedDataRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerRelatedDataRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerRelatedDataRequest_Filter) ProtoMessage() {}

func (x *ListCustomerRelatedDataRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerRelatedDataRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomerRelatedDataRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ListCustomerRelatedDataRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetBusinessIds() []int32 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetStates() []CustomerRelatedData_State {
	if x != nil {
		return x.States
	}
	return nil
}

// sorting
type ListCustomerRelatedDataRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListCustomerRelatedDataRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListCustomerRelatedDataRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     ListCustomerRelatedDataRequest_Sorting_Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.ListCustomerRelatedDataRequest_Sorting_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerRelatedDataRequest_Sorting) Reset() {
	*x = ListCustomerRelatedDataRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerRelatedDataRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerRelatedDataRequest_Sorting) ProtoMessage() {}

func (x *ListCustomerRelatedDataRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerRelatedDataRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListCustomerRelatedDataRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{14, 1}
}

func (x *ListCustomerRelatedDataRequest_Sorting) GetField() ListCustomerRelatedDataRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListCustomerRelatedDataRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListCustomerRelatedDataRequest_Sorting) GetDirection() ListCustomerRelatedDataRequest_Sorting_Direction {
	if x != nil {
		return x.Direction
	}
	return ListCustomerRelatedDataRequest_Sorting_DIRECTION_UNSPECIFIED
}

// update ref
type UpdateCustomerRelatedDataRequest_UpdateRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户颜色
	ClientColor *string `protobuf:"bytes,1,opt,name=client_color,json=clientColor,proto3,oneof" json:"client_color,omitempty"`
	// 是否屏蔽消息
	IsBlockMessage *int32 `protobuf:"varint,2,opt,name=is_block_message,json=isBlockMessage,proto3,oneof" json:"is_block_message,omitempty"`
	// 是否屏蔽在线预约
	IsBlockOnlineBooking *int32 `protobuf:"varint,3,opt,name=is_block_online_booking,json=isBlockOnlineBooking,proto3,oneof" json:"is_block_online_booking,omitempty"`
	// 登录邮箱
	LoginEmail *string `protobuf:"bytes,4,opt,name=login_email,json=loginEmail,proto3,oneof" json:"login_email,omitempty"`
	// 推荐来源ID
	ReferralSourceId *int32 `protobuf:"varint,5,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// 推荐来源描述
	ReferralSourceDesc *string `protobuf:"bytes,6,opt,name=referral_source_desc,json=referralSourceDesc,proto3,oneof" json:"referral_source_desc,omitempty"`
	// 发送自动邮件
	SendAutoEmail *int32 `protobuf:"varint,7,opt,name=send_auto_email,json=sendAutoEmail,proto3,oneof" json:"send_auto_email,omitempty"`
	// 发送自动短信
	SendAutoMessage *int32 `protobuf:"varint,8,opt,name=send_auto_message,json=sendAutoMessage,proto3,oneof" json:"send_auto_message,omitempty"`
	// 发送app自动短信
	SendAppAutoMessage *int32 `protobuf:"varint,9,opt,name=send_app_auto_message,json=sendAppAutoMessage,proto3,oneof" json:"send_app_auto_message,omitempty"`
	// 首选美容师ID
	PreferredGroomerId *int32 `protobuf:"varint,10,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3,oneof" json:"preferred_groomer_id,omitempty"`
	// 首选频率天数
	PreferredFrequencyDay *int32 `protobuf:"varint,11,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3,oneof" json:"preferred_frequency_day,omitempty"`
	// 首选频率类型
	PreferredFrequencyType *int32 `protobuf:"varint,12,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3,oneof" json:"preferred_frequency_type,omitempty"`
	// 首选天
	PreferredDay *string `protobuf:"bytes,13,opt,name=preferred_day,json=preferredDay,proto3,oneof" json:"preferred_day,omitempty"`
	// 首选时间
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: this is json string not a time --)
	PreferredTime *string `protobuf:"bytes,14,opt,name=preferred_time,json=preferredTime,proto3,oneof" json:"preferred_time,omitempty"`
	// 是否退订
	IsUnsubscribed *bool `protobuf:"varint,15,opt,name=is_unsubscribed,json=isUnsubscribed,proto3,oneof" json:"is_unsubscribed,omitempty"`
	// 生日
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	aip.dev/not-precedent: this is not a time field --)
	Birthday *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// 自定义生命周期ID
	CustomizeLifeCycleId *int64 `protobuf:"varint,17,opt,name=customize_life_cycle_id,json=customizeLifeCycleId,proto3,oneof" json:"customize_life_cycle_id,omitempty"`
	// 自定义行动状态ID
	CustomizeActionStateId *int64 `protobuf:"varint,18,opt,name=customize_action_state_id,json=customizeActionStateId,proto3,oneof" json:"customize_action_state_id,omitempty"`
	// state
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for updating, not for output --)
	State         *CustomerRelatedData_State `protobuf:"varint,19,opt,name=state,proto3,enum=backend.proto.customer.v2.CustomerRelatedData_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) Reset() {
	*x = UpdateCustomerRelatedDataRequest_UpdateRef{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRelatedDataRequest_UpdateRef) ProtoMessage() {}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRelatedDataRequest_UpdateRef.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRelatedDataRequest_UpdateRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetClientColor() string {
	if x != nil && x.ClientColor != nil {
		return *x.ClientColor
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetIsBlockMessage() int32 {
	if x != nil && x.IsBlockMessage != nil {
		return *x.IsBlockMessage
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetIsBlockOnlineBooking() int32 {
	if x != nil && x.IsBlockOnlineBooking != nil {
		return *x.IsBlockOnlineBooking
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetLoginEmail() string {
	if x != nil && x.LoginEmail != nil {
		return *x.LoginEmail
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetReferralSourceId() int32 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetReferralSourceDesc() string {
	if x != nil && x.ReferralSourceDesc != nil {
		return *x.ReferralSourceDesc
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetSendAutoEmail() int32 {
	if x != nil && x.SendAutoEmail != nil {
		return *x.SendAutoEmail
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetSendAutoMessage() int32 {
	if x != nil && x.SendAutoMessage != nil {
		return *x.SendAutoMessage
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetSendAppAutoMessage() int32 {
	if x != nil && x.SendAppAutoMessage != nil {
		return *x.SendAppAutoMessage
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetPreferredGroomerId() int32 {
	if x != nil && x.PreferredGroomerId != nil {
		return *x.PreferredGroomerId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetPreferredFrequencyDay() int32 {
	if x != nil && x.PreferredFrequencyDay != nil {
		return *x.PreferredFrequencyDay
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetPreferredFrequencyType() int32 {
	if x != nil && x.PreferredFrequencyType != nil {
		return *x.PreferredFrequencyType
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetPreferredDay() string {
	if x != nil && x.PreferredDay != nil {
		return *x.PreferredDay
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetPreferredTime() string {
	if x != nil && x.PreferredTime != nil {
		return *x.PreferredTime
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetIsUnsubscribed() bool {
	if x != nil && x.IsUnsubscribed != nil {
		return *x.IsUnsubscribed
	}
	return false
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetCustomizeLifeCycleId() int64 {
	if x != nil && x.CustomizeLifeCycleId != nil {
		return *x.CustomizeLifeCycleId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetCustomizeActionStateId() int64 {
	if x != nil && x.CustomizeActionStateId != nil {
		return *x.CustomizeActionStateId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest_UpdateRef) GetState() CustomerRelatedData_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return CustomerRelatedData_STATE_UNSPECIFIED
}

// 过滤条件
type ListContactTagsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 组织引用 (必填, 如果为空会报错)
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// name, is fuzzy search
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// state
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for filtering, not for output --)
	States        []ContactTag_State `protobuf:"varint,4,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.ContactTag_State" json:"states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactTagsRequest_Filter) Reset() {
	*x = ListContactTagsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactTagsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactTagsRequest_Filter) ProtoMessage() {}

func (x *ListContactTagsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactTagsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListContactTagsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ListContactTagsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListContactTagsRequest_Filter) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *ListContactTagsRequest_Filter) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListContactTagsRequest_Filter) GetStates() []ContactTag_State {
	if x != nil {
		return x.States
	}
	return nil
}

// sorting
type ListContactTagsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListContactTagsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListContactTagsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     ListContactTagsRequest_Sorting_Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.ListContactTagsRequest_Sorting_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactTagsRequest_Sorting) Reset() {
	*x = ListContactTagsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactTagsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactTagsRequest_Sorting) ProtoMessage() {}

func (x *ListContactTagsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactTagsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListContactTagsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{20, 1}
}

func (x *ListContactTagsRequest_Sorting) GetField() ListContactTagsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListContactTagsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListContactTagsRequest_Sorting) GetDirection() ListContactTagsRequest_Sorting_Direction {
	if x != nil {
		return x.Direction
	}
	return ListContactTagsRequest_Sorting_DIRECTION_UNSPECIFIED
}

// update ref
type UpdateContactTagRequest_UpdateRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// color
	Color *string `protobuf:"bytes,2,opt,name=color,proto3,oneof" json:"color,omitempty"`
	// sort order
	SortOrder *int32 `protobuf:"varint,3,opt,name=sort_order,json=sortOrder,proto3,oneof" json:"sort_order,omitempty"`
	// state
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for updating, not for output --)
	State         *ContactTag_State `protobuf:"varint,4,opt,name=state,proto3,enum=backend.proto.customer.v2.ContactTag_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactTagRequest_UpdateRef) Reset() {
	*x = UpdateContactTagRequest_UpdateRef{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactTagRequest_UpdateRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactTagRequest_UpdateRef) ProtoMessage() {}

func (x *UpdateContactTagRequest_UpdateRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactTagRequest_UpdateRef.ProtoReflect.Descriptor instead.
func (*UpdateContactTagRequest_UpdateRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{22, 0}
}

func (x *UpdateContactTagRequest_UpdateRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateContactTagRequest_UpdateRef) GetColor() string {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return ""
}

func (x *UpdateContactTagRequest_UpdateRef) GetSortOrder() int32 {
	if x != nil && x.SortOrder != nil {
		return *x.SortOrder
	}
	return 0
}

func (x *UpdateContactTagRequest_UpdateRef) GetState() ContactTag_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ContactTag_STATE_UNSPECIFIED
}

// filter
type ListLeadsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for filtering, not for output --)
	//
	// 状态
	// 如果states为空, 则返回所有状态的lead, 没有默认值
	States []Lead_State `protobuf:"varint,3,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.Lead_State" json:"states,omitempty"`
	// 生命周期ID
	LifecycleIds []int64 `protobuf:"varint,4,rep,packed,name=lifecycle_ids,json=lifecycleIds,proto3" json:"lifecycle_ids,omitempty"`
	// 负责人员工ID
	OwnerStaffIds []int64 `protobuf:"varint,5,rep,packed,name=owner_staff_ids,json=ownerStaffIds,proto3" json:"owner_staff_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLeadsRequest_Filter) Reset() {
	*x = ListLeadsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsRequest_Filter) ProtoMessage() {}

func (x *ListLeadsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListLeadsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{26, 0}
}

func (x *ListLeadsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetStates() []Lead_State {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetLifecycleIds() []int64 {
	if x != nil {
		return x.LifecycleIds
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetOwnerStaffIds() []int64 {
	if x != nil {
		return x.OwnerStaffIds
	}
	return nil
}

// sorting
type ListLeadsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListLeadsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListLeadsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     ListLeadsRequest_Sorting_Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.ListLeadsRequest_Sorting_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLeadsRequest_Sorting) Reset() {
	*x = ListLeadsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsRequest_Sorting) ProtoMessage() {}

func (x *ListLeadsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListLeadsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{26, 1}
}

func (x *ListLeadsRequest_Sorting) GetField() ListLeadsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListLeadsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListLeadsRequest_Sorting) GetDirection() ListLeadsRequest_Sorting_Direction {
	if x != nil {
		return x.Direction
	}
	return ListLeadsRequest_Sorting_DIRECTION_UNSPECIFIED
}

// update ref
type UpdateLeadRequest_UpdateRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索名称
	GivenName *string `protobuf:"bytes,1,opt,name=given_name,json=givenName,proto3,oneof" json:"given_name,omitempty"`
	// 线索姓氏
	FamilyName *string `protobuf:"bytes,2,opt,name=family_name,json=familyName,proto3,oneof" json:"family_name,omitempty"`
	// 线索自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,3,opt,name=custom_fields,json=customFields,proto3,oneof" json:"custom_fields,omitempty"`
	// 生命周期ID
	LifecycleId *int64 `protobuf:"varint,4,opt,name=lifecycle_id,json=lifecycleId,proto3,oneof" json:"lifecycle_id,omitempty"`
	// 负责人员工ID
	OwnerStaffId *int64 `protobuf:"varint,5,opt,name=owner_staff_id,json=ownerStaffId,proto3,oneof" json:"owner_staff_id,omitempty"`
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for updating, not for output --)
	//
	// 状态
	State         *Lead_State `protobuf:"varint,6,opt,name=state,proto3,enum=backend.proto.customer.v2.Lead_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLeadRequest_UpdateRef) Reset() {
	*x = UpdateLeadRequest_UpdateRef{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLeadRequest_UpdateRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadRequest_UpdateRef) ProtoMessage() {}

func (x *UpdateLeadRequest_UpdateRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadRequest_UpdateRef.ProtoReflect.Descriptor instead.
func (*UpdateLeadRequest_UpdateRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{28, 0}
}

func (x *UpdateLeadRequest_UpdateRef) GetGivenName() string {
	if x != nil && x.GivenName != nil {
		return *x.GivenName
	}
	return ""
}

func (x *UpdateLeadRequest_UpdateRef) GetFamilyName() string {
	if x != nil && x.FamilyName != nil {
		return *x.FamilyName
	}
	return ""
}

func (x *UpdateLeadRequest_UpdateRef) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *UpdateLeadRequest_UpdateRef) GetLifecycleId() int64 {
	if x != nil && x.LifecycleId != nil {
		return *x.LifecycleId
	}
	return 0
}

func (x *UpdateLeadRequest_UpdateRef) GetOwnerStaffId() int64 {
	if x != nil && x.OwnerStaffId != nil {
		return *x.OwnerStaffId
	}
	return 0
}

func (x *UpdateLeadRequest_UpdateRef) GetState() Lead_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Lead_STATE_UNSPECIFIED
}

// filter
type ListAddressesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 所有者ID
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for filtering, not for output --)
	//
	// state
	// 如果states为空, 则返回所有状态的地址, 没有默认值
	States        []Address_State `protobuf:"varint,3,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.Address_State" json:"states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressesRequest_Filter) Reset() {
	*x = ListAddressesRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesRequest_Filter) ProtoMessage() {}

func (x *ListAddressesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAddressesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{32, 0}
}

func (x *ListAddressesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListAddressesRequest_Filter) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListAddressesRequest_Filter) GetStates() []Address_State {
	if x != nil {
		return x.States
	}
	return nil
}

// sorting
type ListAddressesRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListAddressesRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListAddressesRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     ListAddressesRequest_Sorting_Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.ListAddressesRequest_Sorting_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressesRequest_Sorting) Reset() {
	*x = ListAddressesRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesRequest_Sorting) ProtoMessage() {}

func (x *ListAddressesRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListAddressesRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{32, 1}
}

func (x *ListAddressesRequest_Sorting) GetField() ListAddressesRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListAddressesRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListAddressesRequest_Sorting) GetDirection() ListAddressesRequest_Sorting_Direction {
	if x != nil {
		return x.Direction
	}
	return ListAddressesRequest_Sorting_DIRECTION_UNSPECIFIED
}

// update ref
type UpdateAddressRequest_UpdateRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// address
	Address *postaladdress.PostalAddress `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	// type
	Type *Address_Type `protobuf:"varint,2,opt,name=type,proto3,enum=backend.proto.customer.v2.Address_Type,oneof" json:"type,omitempty"`
	// state
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for updating, not for output --)
	State         *Address_State `protobuf:"varint,3,opt,name=state,proto3,enum=backend.proto.customer.v2.Address_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAddressRequest_UpdateRef) Reset() {
	*x = UpdateAddressRequest_UpdateRef{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAddressRequest_UpdateRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddressRequest_UpdateRef) ProtoMessage() {}

func (x *UpdateAddressRequest_UpdateRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddressRequest_UpdateRef.ProtoReflect.Descriptor instead.
func (*UpdateAddressRequest_UpdateRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{34, 0}
}

func (x *UpdateAddressRequest_UpdateRef) GetAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *UpdateAddressRequest_UpdateRef) GetType() Address_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return Address_TYPE_UNSPECIFIED
}

func (x *UpdateAddressRequest_UpdateRef) GetState() Address_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Address_STATE_UNSPECIFIED
}

// filter
type ListCustomFieldsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// organization
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// 实体所有者
	AssociationType CustomField_AssociationType `protobuf:"varint,3,opt,name=association_type,json=associationType,proto3,enum=backend.proto.customer.v2.CustomField_AssociationType" json:"association_type,omitempty"`
	// 字段类型
	Types []CustomField_Type `protobuf:"varint,4,rep,packed,name=types,proto3,enum=backend.proto.customer.v2.CustomField_Type" json:"types,omitempty"`
	// 是否必填
	IsRequired *bool `protobuf:"varint,5,opt,name=is_required,json=isRequired,proto3,oneof" json:"is_required,omitempty"`
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for filtering, not for output --)
	//
	// 状态
	States        []CustomField_State `protobuf:"varint,6,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.CustomField_State" json:"states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomFieldsRequest_Filter) Reset() {
	*x = ListCustomFieldsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomFieldsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomFieldsRequest_Filter) ProtoMessage() {}

func (x *ListCustomFieldsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomFieldsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomFieldsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{38, 0}
}

func (x *ListCustomFieldsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListCustomFieldsRequest_Filter) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *ListCustomFieldsRequest_Filter) GetAssociationType() CustomField_AssociationType {
	if x != nil {
		return x.AssociationType
	}
	return CustomField_ASSOCIATION_TYPE_UNSPECIFIED
}

func (x *ListCustomFieldsRequest_Filter) GetTypes() []CustomField_Type {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *ListCustomFieldsRequest_Filter) GetIsRequired() bool {
	if x != nil && x.IsRequired != nil {
		return *x.IsRequired
	}
	return false
}

func (x *ListCustomFieldsRequest_Filter) GetStates() []CustomField_State {
	if x != nil {
		return x.States
	}
	return nil
}

// sorting
type ListCustomFieldsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListCustomFieldsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListCustomFieldsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     ListCustomFieldsRequest_Sorting_Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.ListCustomFieldsRequest_Sorting_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomFieldsRequest_Sorting) Reset() {
	*x = ListCustomFieldsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomFieldsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomFieldsRequest_Sorting) ProtoMessage() {}

func (x *ListCustomFieldsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomFieldsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListCustomFieldsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{38, 1}
}

func (x *ListCustomFieldsRequest_Sorting) GetField() ListCustomFieldsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListCustomFieldsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListCustomFieldsRequest_Sorting) GetDirection() ListCustomFieldsRequest_Sorting_Direction {
	if x != nil {
		return x.Direction
	}
	return ListCustomFieldsRequest_Sorting_DIRECTION_UNSPECIFIED
}

// update ref
type UpdateCustomFieldRequest_UpdateRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段名称
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 字段类型
	Type *CustomField_Type `protobuf:"varint,2,opt,name=type,proto3,enum=backend.proto.customer.v2.CustomField_Type,oneof" json:"type,omitempty"`
	// 是否必填
	IsRequired *bool `protobuf:"varint,3,opt,name=is_required,json=isRequired,proto3,oneof" json:"is_required,omitempty"`
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: this field is used for updating, not for output --)
	//
	// 状态
	State         *CustomField_State `protobuf:"varint,4,opt,name=state,proto3,enum=backend.proto.customer.v2.CustomField_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomFieldRequest_UpdateRef) Reset() {
	*x = UpdateCustomFieldRequest_UpdateRef{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomFieldRequest_UpdateRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomFieldRequest_UpdateRef) ProtoMessage() {}

func (x *UpdateCustomFieldRequest_UpdateRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomFieldRequest_UpdateRef.ProtoReflect.Descriptor instead.
func (*UpdateCustomFieldRequest_UpdateRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{40, 0}
}

func (x *UpdateCustomFieldRequest_UpdateRef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateCustomFieldRequest_UpdateRef) GetType() CustomField_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return CustomField_TYPE_UNSPECIFIED
}

func (x *UpdateCustomFieldRequest_UpdateRef) GetIsRequired() bool {
	if x != nil && x.IsRequired != nil {
		return *x.IsRequired
	}
	return false
}

func (x *UpdateCustomFieldRequest_UpdateRef) GetState() CustomField_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return CustomField_STATE_UNSPECIFIED
}

var File_backend_proto_customer_v2_metadata_service_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_metadata_service_proto_rawDesc = "" +
	"\n" +
	"0backend/proto/customer/v2/metadata_service.proto\x12\x19backend.proto.customer.v2\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/field_mask.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a(backend/proto/customer/v2/metadata.proto\x1a&backend/proto/customer/v2/common.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1egoogle/type/phone_number.proto\x1a google/type/postal_address.proto\"]\n" +
	"\x15CreateCustomerRequest\x12D\n" +
	"\bcustomer\x18\x01 \x01(\v2#.backend.proto.customer.v2.CustomerB\x03\xe0A\x02R\bcustomer\"-\n" +
	"\x12GetCustomerRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\xb3\a\n" +
	"\x14ListCustomersRequest\x12V\n" +
	"\x06filter\x18\x01 \x01(\v26.backend.proto.customer.v2.ListCustomersRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12Q\n" +
	"\asorting\x18\x02 \x01(\v27.backend.proto.customer.v2.ListCustomersRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x12*\n" +
	"\x11return_total_size\x18\x05 \x01(\bR\x0freturnTotalSize\x1a\xb2\x02\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12V\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x06\xbaH\x03\xc8\x01\x01R\forganization\x12A\n" +
	"\x06states\x18\x03 \x03(\x0e2).backend.proto.customer.v2.Customer.StateR\x06states\x12#\n" +
	"\rlifecycle_ids\x18\x04 \x03(\x03R\flifecycleIds\x12&\n" +
	"\x0fowner_staff_ids\x18\x05 \x03(\x03R\rownerStaffIds\x12.\n" +
	"\x13referral_source_ids\x18\x06 \x03(\x03R\x11referralSourceIds\x1a\xc6\x02\n" +
	"\aSorting\x12S\n" +
	"\x05field\x18\x01 \x01(\x0e2=.backend.proto.customer.v2.ListCustomersRequest.Sorting.FieldR\x05field\x12_\n" +
	"\tdirection\x18\x02 \x01(\x0e2A.backend.proto.customer.v2.ListCustomersRequest.Sorting.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"9\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02\"\xb5\x01\n" +
	"\x15ListCustomersResponse\x12A\n" +
	"\tcustomers\x18\x01 \x03(\v2#.backend.proto.customer.v2.CustomerR\tcustomers\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xed\x05\n" +
	"\x15UpdateCustomerRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12T\n" +
	"\x03ref\x18\x02 \x01(\v2:.backend.proto.customer.v2.UpdateCustomerRequest.UpdateRefB\x06\xbaH\x03\xc8\x01\x01R\x03ref\x1a\xe4\x04\n" +
	"\tUpdateRef\x12\"\n" +
	"\n" +
	"given_name\x18\x01 \x01(\tH\x00R\tgivenName\x88\x01\x01\x12$\n" +
	"\vfamily_name\x18\x02 \x01(\tH\x01R\n" +
	"familyName\x88\x01\x01\x12A\n" +
	"\rcustom_fields\x18\x03 \x01(\v2\x17.google.protobuf.StructH\x02R\fcustomFields\x88\x01\x01\x12&\n" +
	"\flifecycle_id\x18\x04 \x01(\x03H\x03R\vlifecycleId\x88\x01\x01\x12)\n" +
	"\x0eowner_staff_id\x18\x05 \x01(\x03H\x04R\fownerStaffId\x88\x01\x01\x12+\n" +
	"\x0faction_state_id\x18\x06 \x01(\x03H\x05R\ractionStateId\x88\x01\x01\x12.\n" +
	"\vavatar_path\x18\a \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x06R\n" +
	"avatarPath\x88\x01\x01\x12:\n" +
	"\x12referral_source_id\x18\b \x01(\x03B\a\xbaH\x04\"\x02 \x00H\aR\x10referralSourceId\x88\x01\x01\x12D\n" +
	"\x05state\x18\t \x01(\x0e2).backend.proto.customer.v2.Customer.StateH\bR\x05state\x88\x01\x01B\r\n" +
	"\v_given_nameB\x0e\n" +
	"\f_family_nameB\x10\n" +
	"\x0e_custom_fieldsB\x0f\n" +
	"\r_lifecycle_idB\x11\n" +
	"\x0f_owner_staff_idB\x12\n" +
	"\x10_action_state_idB\x0e\n" +
	"\f_avatar_pathB\x15\n" +
	"\x13_referral_source_idB\b\n" +
	"\x06_state\"0\n" +
	"\x15DeleteCustomerRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"Y\n" +
	"\x14CreateContactRequest\x12A\n" +
	"\acontact\x18\x01 \x01(\v2\".backend.proto.customer.v2.ContactB\x03\xe0A\x02R\acontact\",\n" +
	"\x11GetContactRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\xfa\x05\n" +
	"\x13ListContactsRequest\x12U\n" +
	"\x06filter\x18\x01 \x01(\v25.backend.proto.customer.v2.ListContactsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12P\n" +
	"\asorting\x18\x02 \x01(\v26.backend.proto.customer.v2.ListContactsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x12*\n" +
	"\x11return_total_size\x18\x05 \x01(\bR\x0freturnTotalSize\x1a\x7f\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\x12@\n" +
	"\x06states\x18\x03 \x03(\x0e2(.backend.proto.customer.v2.Contact.StateR\x06states\x1a\xc4\x02\n" +
	"\aSorting\x12R\n" +
	"\x05field\x18\x01 \x01(\x0e2<.backend.proto.customer.v2.ListContactsRequest.Sorting.FieldR\x05field\x12^\n" +
	"\tdirection\x18\x02 \x01(\<EMAIL>\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"9\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02\"\xb1\x01\n" +
	"\x14ListContactsResponse\x12>\n" +
	"\bcontacts\x18\x01 \x03(\v2\".backend.proto.customer.v2.ContactR\bcontacts\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xe9\x03\n" +
	"\x14UpdateContactRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12S\n" +
	"\x03ref\x18\x02 \x01(\v29.backend.proto.customer.v2.UpdateContactRequest.UpdateRefB\x06\xbaH\x03\xc8\x01\x01R\x03ref\x1a\xe2\x02\n" +
	"\tUpdateRef\x12\"\n" +
	"\n" +
	"given_name\x18\x01 \x01(\tH\x00R\tgivenName\x88\x01\x01\x12$\n" +
	"\vfamily_name\x18\x02 \x01(\tH\x01R\n" +
	"familyName\x88\x01\x01\x12\x19\n" +
	"\x05email\x18\x03 \x01(\tH\x02R\x05email\x88\x01\x01\x123\n" +
	"\x05phone\x18\x04 \x01(\v2\x18.google.type.PhoneNumberH\x03R\x05phone\x88\x01\x01\x12\x17\n" +
	"\atag_ids\x18\x05 \x03(\x03R\x06tagIds\x12\x17\n" +
	"\x04note\x18\x06 \x01(\tH\x04R\x04note\x88\x01\x01\x12C\n" +
	"\x05state\x18\a \x01(\x0e2(.backend.proto.customer.v2.Contact.StateH\x05R\x05state\x88\x01\x01B\r\n" +
	"\v_given_nameB\x0e\n" +
	"\f_family_nameB\b\n" +
	"\x06_emailB\b\n" +
	"\x06_phoneB\a\n" +
	"\x05_noteB\b\n" +
	"\x06_state\"/\n" +
	"\x14DeleteContactRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x8b\x01\n" +
	" CreateCustomerRelatedDataRequest\x12g\n" +
	"\x15customer_related_data\x18\x01 \x01(\v2..backend.proto.customer.v2.CustomerRelatedDataB\x03\xe0A\x02R\x13customerRelatedData\"I\n" +
	"\x1dGetCustomerRelatedDataRequest\x12(\n" +
	"\vcustomer_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"customerId\"\xd2\a\n" +
	"\x1eListCustomerRelatedDataRequest\x12`\n" +
	"\x06filter\x18\x01 \x01(\<EMAIL>\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12[\n" +
	"\asorting\x18\x02 \x01(\v2A.backend.proto.customer.v2.ListCustomerRelatedDataRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x12*\n" +
	"\x11return_total_size\x18\x05 \x01(\bR\x0freturnTotalSize\x1a\x9f\x02\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\x12N\n" +
	"\forganization\x18\x03 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12!\n" +
	"\fbusiness_ids\x18\x04 \x03(\x05R\vbusinessIds\x12\x1f\n" +
	"\vcompany_ids\x18\x05 \x03(\x03R\n" +
	"companyIds\x12L\n" +
	"\x06states\x18\x06 \x03(\x0e24.backend.proto.customer.v2.CustomerRelatedData.StateR\x06states\x1a\xda\x02\n" +
	"\aSorting\x12]\n" +
	"\x05field\x18\x01 \x01(\x0e2G.backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.FieldR\x05field\x12i\n" +
	"\tdirection\x18\x02 \x01(\x0e2K.backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"9\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02\"\xe0\x01\n" +
	"\x1fListCustomerRelatedDataResponse\x12b\n" +
	"\x15customer_related_data\x18\x01 \x03(\v2..backend.proto.customer.v2.CustomerRelatedDataR\x13customerRelatedData\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xc6\f\n" +
	" UpdateCustomerRelatedDataRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12_\n" +
	"\x03ref\x18\x02 \x01(\v2E.backend.proto.customer.v2.UpdateCustomerRelatedDataRequest.UpdateRefB\x06\xbaH\x03\xc8\x01\x01R\x03ref\x1a\xa7\v\n" +
	"\tUpdateRef\x12&\n" +
	"\fclient_color\x18\x01 \x01(\tH\x00R\vclientColor\x88\x01\x01\x12-\n" +
	"\x10is_block_message\x18\x02 \x01(\x05H\x01R\x0eisBlockMessage\x88\x01\x01\x12:\n" +
	"\x17is_block_online_booking\x18\x03 \x01(\x05H\x02R\x14isBlockOnlineBooking\x88\x01\x01\x12$\n" +
	"\vlogin_email\x18\x04 \x01(\tH\x03R\n" +
	"loginEmail\x88\x01\x01\x121\n" +
	"\x12referral_source_id\x18\x05 \x01(\x05H\x04R\x10referralSourceId\x88\x01\x01\x125\n" +
	"\x14referral_source_desc\x18\x06 \x01(\tH\x05R\x12referralSourceDesc\x88\x01\x01\x12+\n" +
	"\x0fsend_auto_email\x18\a \x01(\x05H\x06R\rsendAutoEmail\x88\x01\x01\x12/\n" +
	"\x11send_auto_message\x18\b \x01(\x05H\aR\x0fsendAutoMessage\x88\x01\x01\x126\n" +
	"\x15send_app_auto_message\x18\t \x01(\x05H\bR\x12sendAppAutoMessage\x88\x01\x01\x125\n" +
	"\x14preferred_groomer_id\x18\n" +
	" \x01(\x05H\tR\x12preferredGroomerId\x88\x01\x01\x12;\n" +
	"\x17preferred_frequency_day\x18\v \x01(\x05H\n" +
	"R\x15preferredFrequencyDay\x88\x01\x01\x12=\n" +
	"\x18preferred_frequency_type\x18\f \x01(\x05H\vR\x16preferredFrequencyType\x88\x01\x01\x12(\n" +
	"\rpreferred_day\x18\r \x01(\tH\fR\fpreferredDay\x88\x01\x01\x12*\n" +
	"\x0epreferred_time\x18\x0e \x01(\tH\rR\rpreferredTime\x88\x01\x01\x12,\n" +
	"\x0fis_unsubscribed\x18\x0f \x01(\bH\x0eR\x0eisUnsubscribed\x88\x01\x01\x12;\n" +
	"\bbirthday\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampH\x0fR\bbirthday\x88\x01\x01\x12:\n" +
	"\x17customize_life_cycle_id\x18\x11 \x01(\x03H\x10R\x14customizeLifeCycleId\x88\x01\x01\x12>\n" +
	"\x19customize_action_state_id\x18\x12 \x01(\x03H\x11R\x16customizeActionStateId\x88\x01\x01\x12O\n" +
	"\x05state\x18\x13 \x01(\x0e24.backend.proto.customer.v2.CustomerRelatedData.StateH\x12R\x05state\x88\x01\x01B\x0f\n" +
	"\r_client_colorB\x13\n" +
	"\x11_is_block_messageB\x1a\n" +
	"\x18_is_block_online_bookingB\x0e\n" +
	"\f_login_emailB\x15\n" +
	"\x13_referral_source_idB\x17\n" +
	"\x15_referral_source_descB\x12\n" +
	"\x10_send_auto_emailB\x14\n" +
	"\x12_send_auto_messageB\x18\n" +
	"\x16_send_app_auto_messageB\x17\n" +
	"\x15_preferred_groomer_idB\x1a\n" +
	"\x18_preferred_frequency_dayB\x1b\n" +
	"\x19_preferred_frequency_typeB\x10\n" +
	"\x0e_preferred_dayB\x11\n" +
	"\x0f_preferred_timeB\x12\n" +
	"\x10_is_unsubscribedB\v\n" +
	"\t_birthdayB\x1a\n" +
	"\x18_customize_life_cycle_idB\x1c\n" +
	"\x1a_customize_action_state_idB\b\n" +
	"\x06_state\";\n" +
	" DeleteCustomerRelatedDataRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"f\n" +
	"\x17CreateContactTagRequest\x12K\n" +
	"\vcontact_tag\x18\x01 \x01(\v2%.backend.proto.customer.v2.ContactTagB\x03\xe0A\x02R\n" +
	"contactTag\"/\n" +
	"\x14GetContactTagRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\xe4\x06\n" +
	"\x16ListContactTagsRequest\x12X\n" +
	"\x06filter\x18\x01 \x01(\v28.backend.proto.customer.v2.ListContactTagsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12S\n" +
	"\asorting\x18\x02 \x01(\v29.backend.proto.customer.v2.ListContactTagsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x12*\n" +
	"\x11return_total_size\x18\x05 \x01(\bR\x0freturnTotalSize\x1a\xd9\x01\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12V\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x06\xbaH\x03\xc8\x01\x01R\forganization\x12\x17\n" +
	"\x04name\x18\x03 \x01(\tH\x00R\x04name\x88\x01\x01\x12C\n" +
	"\x06states\x18\x04 \x03(\x0e2+.backend.proto.customer.v2.ContactTag.StateR\x06statesB\a\n" +
	"\x05_name\x1a\xca\x02\n" +
	"\aSorting\x12U\n" +
	"\x05field\x18\x01 \x01(\x0e2?.backend.proto.customer.v2.ListContactTagsRequest.Sorting.FieldR\x05field\x12a\n" +
	"\tdirection\x18\x02 \x01(\x0e2C.backend.proto.customer.v2.ListContactTagsRequest.Sorting.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"9\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02\"\xbe\x01\n" +
	"\x17ListContactTagsResponse\x12H\n" +
	"\fcontact_tags\x18\x01 \x03(\v2%.backend.proto.customer.v2.ContactTagR\vcontactTags\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xe4\x02\n" +
	"\x17UpdateContactTagRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12V\n" +
	"\x03ref\x18\x02 \x01(\v2<.backend.proto.customer.v2.UpdateContactTagRequest.UpdateRefB\x06\xbaH\x03\xc8\x01\x01R\x03ref\x1a\xd7\x01\n" +
	"\tUpdateRef\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tH\x00R\x04name\x88\x01\x01\x12\x19\n" +
	"\x05color\x18\x02 \x01(\tH\x01R\x05color\x88\x01\x01\x12\"\n" +
	"\n" +
	"sort_order\x18\x03 \x01(\x05H\x02R\tsortOrder\x88\x01\x01\x12F\n" +
	"\x05state\x18\x04 \x01(\x0e2+.backend.proto.customer.v2.ContactTag.StateH\x03R\x05state\x88\x01\x01B\a\n" +
	"\x05_nameB\b\n" +
	"\x06_colorB\r\n" +
	"\v_sort_orderB\b\n" +
	"\x06_state\"2\n" +
	"\x17DeleteContactTagRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"M\n" +
	"\x11CreateLeadRequest\x128\n" +
	"\x04lead\x18\x01 \x01(\v2\x1f.backend.proto.customer.v2.LeadB\x03\xe0A\x02R\x04lead\")\n" +
	"\x0eGetLeadRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\xe3\x06\n" +
	"\x10ListLeadsRequest\x12R\n" +
	"\x06filter\x18\x01 \x01(\v22.backend.proto.customer.v2.ListLeadsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12M\n" +
	"\asorting\x18\x02 \x01(\v23.backend.proto.customer.v2.ListLeadsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x12*\n" +
	"\x11return_total_size\x18\x05 \x01(\bR\x0freturnTotalSize\x1a\xf6\x01\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12N\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12=\n" +
	"\x06states\x18\x03 \x03(\x0e2%.backend.proto.customer.v2.Lead.StateR\x06states\x12#\n" +
	"\rlifecycle_ids\x18\x04 \x03(\x03R\flifecycleIds\x12&\n" +
	"\x0fowner_staff_ids\x18\x05 \x03(\x03R\rownerStaffIds\x1a\xbe\x02\n" +
	"\aSorting\x12O\n" +
	"\x05field\x18\x01 \x01(\x0e29.backend.proto.customer.v2.ListLeadsRequest.Sorting.FieldR\x05field\x12[\n" +
	"\tdirection\x18\x02 \x01(\x0e2=.backend.proto.customer.v2.ListLeadsRequest.Sorting.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"9\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02\"\xa5\x01\n" +
	"\x11ListLeadsResponse\x125\n" +
	"\x05leads\x18\x01 \x03(\v2\x1f.backend.proto.customer.v2.LeadR\x05leads\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\x8d\x04\n" +
	"\x11UpdateLeadRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12P\n" +
	"\x03ref\x18\x02 \x01(\v26.backend.proto.customer.v2.UpdateLeadRequest.UpdateRefB\x06\xbaH\x03\xc8\x01\x01R\x03ref\x1a\x8c\x03\n" +
	"\tUpdateRef\x12\"\n" +
	"\n" +
	"given_name\x18\x01 \x01(\tH\x00R\tgivenName\x88\x01\x01\x12$\n" +
	"\vfamily_name\x18\x02 \x01(\tH\x01R\n" +
	"familyName\x88\x01\x01\x12A\n" +
	"\rcustom_fields\x18\x03 \x01(\v2\x17.google.protobuf.StructH\x02R\fcustomFields\x88\x01\x01\x12&\n" +
	"\flifecycle_id\x18\x04 \x01(\x03H\x03R\vlifecycleId\x88\x01\x01\x12)\n" +
	"\x0eowner_staff_id\x18\x05 \x01(\x03H\x04R\fownerStaffId\x88\x01\x01\x12@\n" +
	"\x05state\x18\x06 \x01(\x0e2%.backend.proto.customer.v2.Lead.StateH\x05R\x05state\x88\x01\x01B\r\n" +
	"\v_given_nameB\x0e\n" +
	"\f_family_nameB\x10\n" +
	"\x0e_custom_fieldsB\x0f\n" +
	"\r_lifecycle_idB\x11\n" +
	"\x0f_owner_staff_idB\b\n" +
	"\x06_state\",\n" +
	"\x11DeleteLeadRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"Y\n" +
	"\x14CreateAddressRequest\x12A\n" +
	"\aaddress\x18\x01 \x01(\v2\".backend.proto.customer.v2.AddressB\x03\xe0A\x02R\aaddress\",\n" +
	"\x11GetAddressRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x87\x06\n" +
	"\x14ListAddressesRequest\x12V\n" +
	"\x06filter\x18\x01 \x01(\v26.backend.proto.customer.v2.ListAddressesRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12Q\n" +
	"\asorting\x18\x02 \x01(\v27.backend.proto.customer.v2.ListAddressesRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x12*\n" +
	"\x11return_total_size\x18\x05 \x01(\bR\x0freturnTotalSize\x1a\x86\x01\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12(\n" +
	"\vcustomer_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"customerId\x12@\n" +
	"\x06states\x18\x03 \x03(\x0e2(.backend.proto.customer.v2.Address.StateR\x06states\x1a\xc6\x02\n" +
	"\aSorting\x12S\n" +
	"\x05field\x18\x01 \x01(\x0e2=.backend.proto.customer.v2.ListAddressesRequest.Sorting.FieldR\x05field\x12_\n" +
	"\tdirection\x18\x02 \x01(\x0e2A.backend.proto.customer.v2.ListAddressesRequest.Sorting.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"9\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02\"\xb4\x01\n" +
	"\x15ListAddressesResponse\x12@\n" +
	"\taddresses\x18\x01 \x03(\v2\".backend.proto.customer.v2.AddressR\taddresses\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xe2\x02\n" +
	"\x14UpdateAddressRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12S\n" +
	"\x03ref\x18\x02 \x01(\v29.backend.proto.customer.v2.UpdateAddressRequest.UpdateRefB\x06\xbaH\x03\xc8\x01\x01R\x03ref\x1a\xdb\x01\n" +
	"\tUpdateRef\x124\n" +
	"\aaddress\x18\x01 \x01(\v2\x1a.google.type.PostalAddressR\aaddress\x12@\n" +
	"\x04type\x18\x02 \x01(\x0e2'.backend.proto.customer.v2.Address.TypeH\x00R\x04type\x88\x01\x01\x12C\n" +
	"\x05state\x18\x03 \x01(\x0e2(.backend.proto.customer.v2.Address.StateH\x01R\x05state\x88\x01\x01B\a\n" +
	"\x05_typeB\b\n" +
	"\x06_state\"/\n" +
	"\x14DeleteAddressRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"j\n" +
	"\x18CreateCustomFieldRequest\x12N\n" +
	"\fcustom_field\x18\x01 \x01(\v2&.backend.proto.customer.v2.CustomFieldB\x03\xe0A\x02R\vcustomField\"0\n" +
	"\x15GetCustomFieldRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\xa4\b\n" +
	"\x17ListCustomFieldsRequest\x12Y\n" +
	"\x06filter\x18\x01 \x01(\v29.backend.proto.customer.v2.ListCustomFieldsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12T\n" +
	"\asorting\x18\x02 \x01(\v2:.backend.proto.customer.v2.ListCustomFieldsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x12*\n" +
	"\x11return_total_size\x18\x05 \x01(\bR\x0freturnTotalSize\x1a\x94\x03\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12V\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x06\xbaH\x03\xc8\x01\x01R\forganization\x12a\n" +
	"\x10association_type\x18\x03 \x01(\x0e26.backend.proto.customer.v2.CustomField.AssociationTypeR\x0fassociationType\x12A\n" +
	"\x05types\x18\x04 \x03(\x0e2+.backend.proto.customer.v2.CustomField.TypeR\x05types\x12$\n" +
	"\vis_required\x18\x05 \x01(\bH\x00R\n" +
	"isRequired\x88\x01\x01\x12D\n" +
	"\x06states\x18\x06 \x03(\x0e2,.backend.proto.customer.v2.CustomField.StateR\x06statesB\x0e\n" +
	"\f_is_required\x1a\xcc\x02\n" +
	"\aSorting\x12V\n" +
	"\x05field\x18\x01 \x01(\<EMAIL>\x05field\x12b\n" +
	"\tdirection\x18\x02 \x01(\x0e2D.backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"9\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02\"\xc2\x01\n" +
	"\x18ListCustomFieldsResponse\x12K\n" +
	"\rcustom_fields\x18\x01 \x03(\v2&.backend.proto.customer.v2.CustomFieldR\fcustomFields\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\x94\x03\n" +
	"\x18UpdateCustomFieldRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12W\n" +
	"\x03ref\x18\x02 \x01(\v2=.backend.proto.customer.v2.UpdateCustomFieldRequest.UpdateRefB\x06\xbaH\x03\xc8\x01\x01R\x03ref\x1a\x85\x02\n" +
	"\tUpdateRef\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tH\x00R\x04name\x88\x01\x01\x12D\n" +
	"\x04type\x18\x02 \x01(\x0e2+.backend.proto.customer.v2.CustomField.TypeH\x01R\x04type\x88\x01\x01\x12$\n" +
	"\vis_required\x18\x03 \x01(\bH\x02R\n" +
	"isRequired\x88\x01\x01\x12G\n" +
	"\x05state\x18\x04 \x01(\x0e2,.backend.proto.customer.v2.CustomField.StateH\x03R\x05state\x88\x01\x01B\a\n" +
	"\x05_nameB\a\n" +
	"\x05_typeB\x0e\n" +
	"\f_is_requiredB\b\n" +
	"\x06_state\"3\n" +
	"\x18DeleteCustomFieldRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id*\x9c\n" +
	"\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10̥\a\x12!\n" +
	"\x1bERR_CODE_CUSTOMER_NOT_FOUND\x10ͥ\a\x12&\n" +
	" ERR_CODE_CUSTOMER_ALREADY_EXISTS\x10Υ\a\x12\"\n" +
	"\x1cERR_CODE_INVALID_CUSTOMER_ID\x10ϥ\a\x12$\n" +
	"\x1eERR_CODE_INVALID_CUSTOMER_NAME\x10Х\a\x12\x1f\n" +
	"\x19ERR_CODE_CUSTOMER_DELETED\x10ѥ\a\x12%\n" +
	"\x1fERR_CODE_CREATE_CUSTOMER_FAILED\x10ҥ\a\x12 \n" +
	"\x1aERR_CODE_ADDRESS_NOT_FOUND\x10֥\a\x12\x1e\n" +
	"\x18ERR_CODE_INVALID_ADDRESS\x10ץ\a\x12%\n" +
	"\x1fERR_CODE_ADDRESS_LIMIT_EXCEEDED\x10إ\a\x12,\n" +
	"&ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS\x10٥\a\x12\x1d\n" +
	"\x17ERR_CODE_TASK_NOT_FOUND\x10\xe0\xa5\a\x12%\n" +
	"\x1fERR_CODE_TASK_ALREADY_COMPLETED\x10\xe1\xa5\a\x12\"\n" +
	"\x1cERR_CODE_INVALID_TASK_STATUS\x10\xe2\xa5\a\x12&\n" +
	" ERR_CODE_ACTION_STATE_NAME_EXIST\x10\xe5\xa5\a\x12$\n" +
	"\x1eERR_CODE_LIFE_CYCLE_NAME_EXIST\x10\xe6\xa5\a\x12\x1e\n" +
	"\x18ERR_CODE_VIEW_NAME_EXIST\x10\xe7\xa5\a\x12 \n" +
	"\x1aERR_CODE_SOURCE_NAME_EXIST\x10\xe8\xa5\a\x12\x1d\n" +
	"\x17ERR_CODE_TAG_NAME_EXIST\x10\xe9\xa5\a\x12 \n" +
	"\x1aERR_CODE_CONTACT_NOT_FOUND\x10\xef\xa5\a\x12%\n" +
	"\x1fERR_CODE_CONTACT_ALREADY_EXISTS\x10\xf0\xa5\a\x12\x1e\n" +
	"\x18ERR_CODE_INVALID_CONTACT\x10\xf1\xa5\a\x12/\n" +
	")ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH\x10\xf2\xa5\a\x12\x1d\n" +
	"\x17ERR_CODE_LEAD_NOT_FOUND\x10\xf4\xa5\a\x12!\n" +
	"\x1bERR_CODE_CREATE_LEAD_FAILED\x10\xf5\xa5\a\x12$\n" +
	"\x1eERR_CODE_CONTACT_TAG_NOT_FOUND\x10\xf9\xa5\a\x12(\n" +
	"\"ERR_CODE_CREATE_CONTACT_TAG_FAILED\x10\xfa\xa5\a\x12%\n" +
	"\x1fERR_CODE_CUSTOM_FIELD_NOT_FOUND\x10\xfe\xa5\a\x12)\n" +
	"#ERR_CODE_CREATE_CUSTOM_FIELD_FAILED\x10\xff\xa5\a\x12*\n" +
	"$ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS\x10\x80\xa6\a\x12#\n" +
	"\x1dERR_CODE_CUSTOM_FIELD_DELETED\x10\x81\xa6\a\x12,\n" +
	"&ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND\x10\x82\xa6\a\x12*\n" +
	"$ERR_CODE_CUSTOM_FIELD_OPTION_DELETED\x10\x83\xa6\a\x12.\n" +
	"(ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND\x10\x88\xa6\a2\xc4\x1d\n" +
	"\x0fMetadataService\x12g\n" +
	"\x0eCreateCustomer\x120.backend.proto.customer.v2.CreateCustomerRequest\x1a#.backend.proto.customer.v2.Customer\x12a\n" +
	"\vGetCustomer\x12-.backend.proto.customer.v2.GetCustomerRequest\x1a#.backend.proto.customer.v2.Customer\x12r\n" +
	"\rListCustomers\x12/.backend.proto.customer.v2.ListCustomersRequest\x1a0.backend.proto.customer.v2.ListCustomersResponse\x12g\n" +
	"\x0eUpdateCustomer\x120.backend.proto.customer.v2.UpdateCustomerRequest\x1a#.backend.proto.customer.v2.Customer\x12Z\n" +
	"\x0eDeleteCustomer\x120.backend.proto.customer.v2.DeleteCustomerRequest\x1a\x16.google.protobuf.Empty\x12d\n" +
	"\rCreateContact\x12/.backend.proto.customer.v2.CreateContactRequest\x1a\".backend.proto.customer.v2.Contact\x12^\n" +
	"\n" +
	"GetContact\x12,.backend.proto.customer.v2.GetContactRequest\x1a\".backend.proto.customer.v2.Contact\x12o\n" +
	"\fListContacts\x12..backend.proto.customer.v2.ListContactsRequest\x1a/.backend.proto.customer.v2.ListContactsResponse\x12d\n" +
	"\rUpdateContact\x12/.backend.proto.customer.v2.UpdateContactRequest\x1a\".backend.proto.customer.v2.Contact\x12X\n" +
	"\rDeleteContact\x12/.backend.proto.customer.v2.DeleteContactRequest\x1a\x16.google.protobuf.Empty\x12\x88\x01\n" +
	"\x19CreateCustomerRelatedData\x12;.backend.proto.customer.v2.CreateCustomerRelatedDataRequest\x1a..backend.proto.customer.v2.CustomerRelatedData\x12\x82\x01\n" +
	"\x16GetCustomerRelatedData\x128.backend.proto.customer.v2.GetCustomerRelatedDataRequest\x1a..backend.proto.customer.v2.CustomerRelatedData\x12\x90\x01\n" +
	"\x17ListCustomerRelatedData\x129.backend.proto.customer.v2.ListCustomerRelatedDataRequest\x1a:.backend.proto.customer.v2.ListCustomerRelatedDataResponse\x12\x88\x01\n" +
	"\x19UpdateCustomerRelatedData\x12;.backend.proto.customer.v2.UpdateCustomerRelatedDataRequest\x1a..backend.proto.customer.v2.CustomerRelatedData\x12p\n" +
	"\x19DeleteCustomerRelatedData\x12;.backend.proto.customer.v2.DeleteCustomerRelatedDataRequest\x1a\x16.google.protobuf.Empty\x12m\n" +
	"\x10CreateContactTag\x122.backend.proto.customer.v2.CreateContactTagRequest\x1a%.backend.proto.customer.v2.ContactTag\x12g\n" +
	"\rGetContactTag\x12/.backend.proto.customer.v2.GetContactTagRequest\x1a%.backend.proto.customer.v2.ContactTag\x12x\n" +
	"\x0fListContactTags\x121.backend.proto.customer.v2.ListContactTagsRequest\x1a2.backend.proto.customer.v2.ListContactTagsResponse\x12m\n" +
	"\x10UpdateContactTag\x122.backend.proto.customer.v2.UpdateContactTagRequest\x1a%.backend.proto.customer.v2.ContactTag\x12^\n" +
	"\x10DeleteContactTag\x122.backend.proto.customer.v2.DeleteContactTagRequest\x1a\x16.google.protobuf.Empty\x12[\n" +
	"\n" +
	"CreateLead\x12,.backend.proto.customer.v2.CreateLeadRequest\x1a\x1f.backend.proto.customer.v2.Lead\x12U\n" +
	"\aGetLead\x12).backend.proto.customer.v2.GetLeadRequest\x1a\x1f.backend.proto.customer.v2.Lead\x12f\n" +
	"\tListLeads\x12+.backend.proto.customer.v2.ListLeadsRequest\x1a,.backend.proto.customer.v2.ListLeadsResponse\x12[\n" +
	"\n" +
	"UpdateLead\x12,.backend.proto.customer.v2.UpdateLeadRequest\x1a\x1f.backend.proto.customer.v2.Lead\x12R\n" +
	"\n" +
	"DeleteLead\x12,.backend.proto.customer.v2.DeleteLeadRequest\x1a\x16.google.protobuf.Empty\x12d\n" +
	"\rCreateAddress\x12/.backend.proto.customer.v2.CreateAddressRequest\x1a\".backend.proto.customer.v2.Address\x12^\n" +
	"\n" +
	"GetAddress\x12,.backend.proto.customer.v2.GetAddressRequest\x1a\".backend.proto.customer.v2.Address\x12r\n" +
	"\rListAddresses\x12/.backend.proto.customer.v2.ListAddressesRequest\x1a0.backend.proto.customer.v2.ListAddressesResponse\x12d\n" +
	"\rUpdateAddress\x12/.backend.proto.customer.v2.UpdateAddressRequest\x1a\".backend.proto.customer.v2.Address\x12X\n" +
	"\rDeleteAddress\x12/.backend.proto.customer.v2.DeleteAddressRequest\x1a\x16.google.protobuf.Empty\x12p\n" +
	"\x11CreateCustomField\x123.backend.proto.customer.v2.CreateCustomFieldRequest\x1a&.backend.proto.customer.v2.CustomField\x12j\n" +
	"\x0eGetCustomField\x120.backend.proto.customer.v2.GetCustomFieldRequest\x1a&.backend.proto.customer.v2.CustomField\x12{\n" +
	"\x10ListCustomFields\x122.backend.proto.customer.v2.ListCustomFieldsRequest\x1a3.backend.proto.customer.v2.ListCustomFieldsResponse\x12p\n" +
	"\x11UpdateCustomField\x123.backend.proto.customer.v2.UpdateCustomFieldRequest\x1a&.backend.proto.customer.v2.CustomField\x12`\n" +
	"\x11DeleteCustomField\x123.backend.proto.customer.v2.DeleteCustomFieldRequest\x1a\x16.google.protobuf.EmptyBk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_metadata_service_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_metadata_service_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_metadata_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_metadata_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_metadata_service_proto_rawDesc), len(file_backend_proto_customer_v2_metadata_service_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescData
}

var file_backend_proto_customer_v2_metadata_service_proto_enumTypes = make([]protoimpl.EnumInfo, 15)
var file_backend_proto_customer_v2_metadata_service_proto_msgTypes = make([]protoimpl.MessageInfo, 63)
var file_backend_proto_customer_v2_metadata_service_proto_goTypes = []any{
	(ErrCode)(0),                                          // 0: backend.proto.customer.v2.ErrCode
	(ListCustomersRequest_Sorting_Field)(0),               // 1: backend.proto.customer.v2.ListCustomersRequest.Sorting.Field
	(ListCustomersRequest_Sorting_Direction)(0),           // 2: backend.proto.customer.v2.ListCustomersRequest.Sorting.Direction
	(ListContactsRequest_Sorting_Field)(0),                // 3: backend.proto.customer.v2.ListContactsRequest.Sorting.Field
	(ListContactsRequest_Sorting_Direction)(0),            // 4: backend.proto.customer.v2.ListContactsRequest.Sorting.Direction
	(ListCustomerRelatedDataRequest_Sorting_Field)(0),     // 5: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.Field
	(ListCustomerRelatedDataRequest_Sorting_Direction)(0), // 6: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.Direction
	(ListContactTagsRequest_Sorting_Field)(0),             // 7: backend.proto.customer.v2.ListContactTagsRequest.Sorting.Field
	(ListContactTagsRequest_Sorting_Direction)(0),         // 8: backend.proto.customer.v2.ListContactTagsRequest.Sorting.Direction
	(ListLeadsRequest_Sorting_Field)(0),                   // 9: backend.proto.customer.v2.ListLeadsRequest.Sorting.Field
	(ListLeadsRequest_Sorting_Direction)(0),               // 10: backend.proto.customer.v2.ListLeadsRequest.Sorting.Direction
	(ListAddressesRequest_Sorting_Field)(0),               // 11: backend.proto.customer.v2.ListAddressesRequest.Sorting.Field
	(ListAddressesRequest_Sorting_Direction)(0),           // 12: backend.proto.customer.v2.ListAddressesRequest.Sorting.Direction
	(ListCustomFieldsRequest_Sorting_Field)(0),            // 13: backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.Field
	(ListCustomFieldsRequest_Sorting_Direction)(0),        // 14: backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.Direction
	(*CreateCustomerRequest)(nil),                         // 15: backend.proto.customer.v2.CreateCustomerRequest
	(*GetCustomerRequest)(nil),                            // 16: backend.proto.customer.v2.GetCustomerRequest
	(*ListCustomersRequest)(nil),                          // 17: backend.proto.customer.v2.ListCustomersRequest
	(*ListCustomersResponse)(nil),                         // 18: backend.proto.customer.v2.ListCustomersResponse
	(*UpdateCustomerRequest)(nil),                         // 19: backend.proto.customer.v2.UpdateCustomerRequest
	(*DeleteCustomerRequest)(nil),                         // 20: backend.proto.customer.v2.DeleteCustomerRequest
	(*CreateContactRequest)(nil),                          // 21: backend.proto.customer.v2.CreateContactRequest
	(*GetContactRequest)(nil),                             // 22: backend.proto.customer.v2.GetContactRequest
	(*ListContactsRequest)(nil),                           // 23: backend.proto.customer.v2.ListContactsRequest
	(*ListContactsResponse)(nil),                          // 24: backend.proto.customer.v2.ListContactsResponse
	(*UpdateContactRequest)(nil),                          // 25: backend.proto.customer.v2.UpdateContactRequest
	(*DeleteContactRequest)(nil),                          // 26: backend.proto.customer.v2.DeleteContactRequest
	(*CreateCustomerRelatedDataRequest)(nil),              // 27: backend.proto.customer.v2.CreateCustomerRelatedDataRequest
	(*GetCustomerRelatedDataRequest)(nil),                 // 28: backend.proto.customer.v2.GetCustomerRelatedDataRequest
	(*ListCustomerRelatedDataRequest)(nil),                // 29: backend.proto.customer.v2.ListCustomerRelatedDataRequest
	(*ListCustomerRelatedDataResponse)(nil),               // 30: backend.proto.customer.v2.ListCustomerRelatedDataResponse
	(*UpdateCustomerRelatedDataRequest)(nil),              // 31: backend.proto.customer.v2.UpdateCustomerRelatedDataRequest
	(*DeleteCustomerRelatedDataRequest)(nil),              // 32: backend.proto.customer.v2.DeleteCustomerRelatedDataRequest
	(*CreateContactTagRequest)(nil),                       // 33: backend.proto.customer.v2.CreateContactTagRequest
	(*GetContactTagRequest)(nil),                          // 34: backend.proto.customer.v2.GetContactTagRequest
	(*ListContactTagsRequest)(nil),                        // 35: backend.proto.customer.v2.ListContactTagsRequest
	(*ListContactTagsResponse)(nil),                       // 36: backend.proto.customer.v2.ListContactTagsResponse
	(*UpdateContactTagRequest)(nil),                       // 37: backend.proto.customer.v2.UpdateContactTagRequest
	(*DeleteContactTagRequest)(nil),                       // 38: backend.proto.customer.v2.DeleteContactTagRequest
	(*CreateLeadRequest)(nil),                             // 39: backend.proto.customer.v2.CreateLeadRequest
	(*GetLeadRequest)(nil),                                // 40: backend.proto.customer.v2.GetLeadRequest
	(*ListLeadsRequest)(nil),                              // 41: backend.proto.customer.v2.ListLeadsRequest
	(*ListLeadsResponse)(nil),                             // 42: backend.proto.customer.v2.ListLeadsResponse
	(*UpdateLeadRequest)(nil),                             // 43: backend.proto.customer.v2.UpdateLeadRequest
	(*DeleteLeadRequest)(nil),                             // 44: backend.proto.customer.v2.DeleteLeadRequest
	(*CreateAddressRequest)(nil),                          // 45: backend.proto.customer.v2.CreateAddressRequest
	(*GetAddressRequest)(nil),                             // 46: backend.proto.customer.v2.GetAddressRequest
	(*ListAddressesRequest)(nil),                          // 47: backend.proto.customer.v2.ListAddressesRequest
	(*ListAddressesResponse)(nil),                         // 48: backend.proto.customer.v2.ListAddressesResponse
	(*UpdateAddressRequest)(nil),                          // 49: backend.proto.customer.v2.UpdateAddressRequest
	(*DeleteAddressRequest)(nil),                          // 50: backend.proto.customer.v2.DeleteAddressRequest
	(*CreateCustomFieldRequest)(nil),                      // 51: backend.proto.customer.v2.CreateCustomFieldRequest
	(*GetCustomFieldRequest)(nil),                         // 52: backend.proto.customer.v2.GetCustomFieldRequest
	(*ListCustomFieldsRequest)(nil),                       // 53: backend.proto.customer.v2.ListCustomFieldsRequest
	(*ListCustomFieldsResponse)(nil),                      // 54: backend.proto.customer.v2.ListCustomFieldsResponse
	(*UpdateCustomFieldRequest)(nil),                      // 55: backend.proto.customer.v2.UpdateCustomFieldRequest
	(*DeleteCustomFieldRequest)(nil),                      // 56: backend.proto.customer.v2.DeleteCustomFieldRequest
	(*ListCustomersRequest_Filter)(nil),                   // 57: backend.proto.customer.v2.ListCustomersRequest.Filter
	(*ListCustomersRequest_Sorting)(nil),                  // 58: backend.proto.customer.v2.ListCustomersRequest.Sorting
	(*UpdateCustomerRequest_UpdateRef)(nil),               // 59: backend.proto.customer.v2.UpdateCustomerRequest.UpdateRef
	(*ListContactsRequest_Filter)(nil),                    // 60: backend.proto.customer.v2.ListContactsRequest.Filter
	(*ListContactsRequest_Sorting)(nil),                   // 61: backend.proto.customer.v2.ListContactsRequest.Sorting
	(*UpdateContactRequest_UpdateRef)(nil),                // 62: backend.proto.customer.v2.UpdateContactRequest.UpdateRef
	(*ListCustomerRelatedDataRequest_Filter)(nil),         // 63: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Filter
	(*ListCustomerRelatedDataRequest_Sorting)(nil),        // 64: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting
	(*UpdateCustomerRelatedDataRequest_UpdateRef)(nil),    // 65: backend.proto.customer.v2.UpdateCustomerRelatedDataRequest.UpdateRef
	(*ListContactTagsRequest_Filter)(nil),                 // 66: backend.proto.customer.v2.ListContactTagsRequest.Filter
	(*ListContactTagsRequest_Sorting)(nil),                // 67: backend.proto.customer.v2.ListContactTagsRequest.Sorting
	(*UpdateContactTagRequest_UpdateRef)(nil),             // 68: backend.proto.customer.v2.UpdateContactTagRequest.UpdateRef
	(*ListLeadsRequest_Filter)(nil),                       // 69: backend.proto.customer.v2.ListLeadsRequest.Filter
	(*ListLeadsRequest_Sorting)(nil),                      // 70: backend.proto.customer.v2.ListLeadsRequest.Sorting
	(*UpdateLeadRequest_UpdateRef)(nil),                   // 71: backend.proto.customer.v2.UpdateLeadRequest.UpdateRef
	(*ListAddressesRequest_Filter)(nil),                   // 72: backend.proto.customer.v2.ListAddressesRequest.Filter
	(*ListAddressesRequest_Sorting)(nil),                  // 73: backend.proto.customer.v2.ListAddressesRequest.Sorting
	(*UpdateAddressRequest_UpdateRef)(nil),                // 74: backend.proto.customer.v2.UpdateAddressRequest.UpdateRef
	(*ListCustomFieldsRequest_Filter)(nil),                // 75: backend.proto.customer.v2.ListCustomFieldsRequest.Filter
	(*ListCustomFieldsRequest_Sorting)(nil),               // 76: backend.proto.customer.v2.ListCustomFieldsRequest.Sorting
	(*UpdateCustomFieldRequest_UpdateRef)(nil),            // 77: backend.proto.customer.v2.UpdateCustomFieldRequest.UpdateRef
	(*Customer)(nil),                                      // 78: backend.proto.customer.v2.Customer
	(*Contact)(nil),                                       // 79: backend.proto.customer.v2.Contact
	(*CustomerRelatedData)(nil),                           // 80: backend.proto.customer.v2.CustomerRelatedData
	(*ContactTag)(nil),                                    // 81: backend.proto.customer.v2.ContactTag
	(*Lead)(nil),                                          // 82: backend.proto.customer.v2.Lead
	(*Address)(nil),                                       // 83: backend.proto.customer.v2.Address
	(*CustomField)(nil),                                   // 84: backend.proto.customer.v2.CustomField
	(*OrganizationRef)(nil),                               // 85: backend.proto.customer.v2.OrganizationRef
	(Customer_State)(0),                                   // 86: backend.proto.customer.v2.Customer.State
	(*structpb.Struct)(nil),                               // 87: google.protobuf.Struct
	(Contact_State)(0),                                    // 88: backend.proto.customer.v2.Contact.State
	(*phone_number.PhoneNumber)(nil),                      // 89: google.type.PhoneNumber
	(CustomerRelatedData_State)(0),                        // 90: backend.proto.customer.v2.CustomerRelatedData.State
	(*timestamppb.Timestamp)(nil),                         // 91: google.protobuf.Timestamp
	(ContactTag_State)(0),                                 // 92: backend.proto.customer.v2.ContactTag.State
	(Lead_State)(0),                                       // 93: backend.proto.customer.v2.Lead.State
	(Address_State)(0),                                    // 94: backend.proto.customer.v2.Address.State
	(*postaladdress.PostalAddress)(nil),                   // 95: google.type.PostalAddress
	(Address_Type)(0),                                     // 96: backend.proto.customer.v2.Address.Type
	(CustomField_AssociationType)(0),                      // 97: backend.proto.customer.v2.CustomField.AssociationType
	(CustomField_Type)(0),                                 // 98: backend.proto.customer.v2.CustomField.Type
	(CustomField_State)(0),                                // 99: backend.proto.customer.v2.CustomField.State
	(*emptypb.Empty)(nil),                                 // 100: google.protobuf.Empty
}
var file_backend_proto_customer_v2_metadata_service_proto_depIdxs = []int32{
	78,  // 0: backend.proto.customer.v2.CreateCustomerRequest.customer:type_name -> backend.proto.customer.v2.Customer
	57,  // 1: backend.proto.customer.v2.ListCustomersRequest.filter:type_name -> backend.proto.customer.v2.ListCustomersRequest.Filter
	58,  // 2: backend.proto.customer.v2.ListCustomersRequest.sorting:type_name -> backend.proto.customer.v2.ListCustomersRequest.Sorting
	78,  // 3: backend.proto.customer.v2.ListCustomersResponse.customers:type_name -> backend.proto.customer.v2.Customer
	59,  // 4: backend.proto.customer.v2.UpdateCustomerRequest.ref:type_name -> backend.proto.customer.v2.UpdateCustomerRequest.UpdateRef
	79,  // 5: backend.proto.customer.v2.CreateContactRequest.contact:type_name -> backend.proto.customer.v2.Contact
	60,  // 6: backend.proto.customer.v2.ListContactsRequest.filter:type_name -> backend.proto.customer.v2.ListContactsRequest.Filter
	61,  // 7: backend.proto.customer.v2.ListContactsRequest.sorting:type_name -> backend.proto.customer.v2.ListContactsRequest.Sorting
	79,  // 8: backend.proto.customer.v2.ListContactsResponse.contacts:type_name -> backend.proto.customer.v2.Contact
	62,  // 9: backend.proto.customer.v2.UpdateContactRequest.ref:type_name -> backend.proto.customer.v2.UpdateContactRequest.UpdateRef
	80,  // 10: backend.proto.customer.v2.CreateCustomerRelatedDataRequest.customer_related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	63,  // 11: backend.proto.customer.v2.ListCustomerRelatedDataRequest.filter:type_name -> backend.proto.customer.v2.ListCustomerRelatedDataRequest.Filter
	64,  // 12: backend.proto.customer.v2.ListCustomerRelatedDataRequest.sorting:type_name -> backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting
	80,  // 13: backend.proto.customer.v2.ListCustomerRelatedDataResponse.customer_related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	65,  // 14: backend.proto.customer.v2.UpdateCustomerRelatedDataRequest.ref:type_name -> backend.proto.customer.v2.UpdateCustomerRelatedDataRequest.UpdateRef
	81,  // 15: backend.proto.customer.v2.CreateContactTagRequest.contact_tag:type_name -> backend.proto.customer.v2.ContactTag
	66,  // 16: backend.proto.customer.v2.ListContactTagsRequest.filter:type_name -> backend.proto.customer.v2.ListContactTagsRequest.Filter
	67,  // 17: backend.proto.customer.v2.ListContactTagsRequest.sorting:type_name -> backend.proto.customer.v2.ListContactTagsRequest.Sorting
	81,  // 18: backend.proto.customer.v2.ListContactTagsResponse.contact_tags:type_name -> backend.proto.customer.v2.ContactTag
	68,  // 19: backend.proto.customer.v2.UpdateContactTagRequest.ref:type_name -> backend.proto.customer.v2.UpdateContactTagRequest.UpdateRef
	82,  // 20: backend.proto.customer.v2.CreateLeadRequest.lead:type_name -> backend.proto.customer.v2.Lead
	69,  // 21: backend.proto.customer.v2.ListLeadsRequest.filter:type_name -> backend.proto.customer.v2.ListLeadsRequest.Filter
	70,  // 22: backend.proto.customer.v2.ListLeadsRequest.sorting:type_name -> backend.proto.customer.v2.ListLeadsRequest.Sorting
	82,  // 23: backend.proto.customer.v2.ListLeadsResponse.leads:type_name -> backend.proto.customer.v2.Lead
	71,  // 24: backend.proto.customer.v2.UpdateLeadRequest.ref:type_name -> backend.proto.customer.v2.UpdateLeadRequest.UpdateRef
	83,  // 25: backend.proto.customer.v2.CreateAddressRequest.address:type_name -> backend.proto.customer.v2.Address
	72,  // 26: backend.proto.customer.v2.ListAddressesRequest.filter:type_name -> backend.proto.customer.v2.ListAddressesRequest.Filter
	73,  // 27: backend.proto.customer.v2.ListAddressesRequest.sorting:type_name -> backend.proto.customer.v2.ListAddressesRequest.Sorting
	83,  // 28: backend.proto.customer.v2.ListAddressesResponse.addresses:type_name -> backend.proto.customer.v2.Address
	74,  // 29: backend.proto.customer.v2.UpdateAddressRequest.ref:type_name -> backend.proto.customer.v2.UpdateAddressRequest.UpdateRef
	84,  // 30: backend.proto.customer.v2.CreateCustomFieldRequest.custom_field:type_name -> backend.proto.customer.v2.CustomField
	75,  // 31: backend.proto.customer.v2.ListCustomFieldsRequest.filter:type_name -> backend.proto.customer.v2.ListCustomFieldsRequest.Filter
	76,  // 32: backend.proto.customer.v2.ListCustomFieldsRequest.sorting:type_name -> backend.proto.customer.v2.ListCustomFieldsRequest.Sorting
	84,  // 33: backend.proto.customer.v2.ListCustomFieldsResponse.custom_fields:type_name -> backend.proto.customer.v2.CustomField
	77,  // 34: backend.proto.customer.v2.UpdateCustomFieldRequest.ref:type_name -> backend.proto.customer.v2.UpdateCustomFieldRequest.UpdateRef
	85,  // 35: backend.proto.customer.v2.ListCustomersRequest.Filter.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	86,  // 36: backend.proto.customer.v2.ListCustomersRequest.Filter.states:type_name -> backend.proto.customer.v2.Customer.State
	1,   // 37: backend.proto.customer.v2.ListCustomersRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListCustomersRequest.Sorting.Field
	2,   // 38: backend.proto.customer.v2.ListCustomersRequest.Sorting.direction:type_name -> backend.proto.customer.v2.ListCustomersRequest.Sorting.Direction
	87,  // 39: backend.proto.customer.v2.UpdateCustomerRequest.UpdateRef.custom_fields:type_name -> google.protobuf.Struct
	86,  // 40: backend.proto.customer.v2.UpdateCustomerRequest.UpdateRef.state:type_name -> backend.proto.customer.v2.Customer.State
	88,  // 41: backend.proto.customer.v2.ListContactsRequest.Filter.states:type_name -> backend.proto.customer.v2.Contact.State
	3,   // 42: backend.proto.customer.v2.ListContactsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListContactsRequest.Sorting.Field
	4,   // 43: backend.proto.customer.v2.ListContactsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.ListContactsRequest.Sorting.Direction
	89,  // 44: backend.proto.customer.v2.UpdateContactRequest.UpdateRef.phone:type_name -> google.type.PhoneNumber
	88,  // 45: backend.proto.customer.v2.UpdateContactRequest.UpdateRef.state:type_name -> backend.proto.customer.v2.Contact.State
	85,  // 46: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Filter.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	90,  // 47: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Filter.states:type_name -> backend.proto.customer.v2.CustomerRelatedData.State
	5,   // 48: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.Field
	6,   // 49: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.direction:type_name -> backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.Direction
	91,  // 50: backend.proto.customer.v2.UpdateCustomerRelatedDataRequest.UpdateRef.birthday:type_name -> google.protobuf.Timestamp
	90,  // 51: backend.proto.customer.v2.UpdateCustomerRelatedDataRequest.UpdateRef.state:type_name -> backend.proto.customer.v2.CustomerRelatedData.State
	85,  // 52: backend.proto.customer.v2.ListContactTagsRequest.Filter.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	92,  // 53: backend.proto.customer.v2.ListContactTagsRequest.Filter.states:type_name -> backend.proto.customer.v2.ContactTag.State
	7,   // 54: backend.proto.customer.v2.ListContactTagsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListContactTagsRequest.Sorting.Field
	8,   // 55: backend.proto.customer.v2.ListContactTagsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.ListContactTagsRequest.Sorting.Direction
	92,  // 56: backend.proto.customer.v2.UpdateContactTagRequest.UpdateRef.state:type_name -> backend.proto.customer.v2.ContactTag.State
	85,  // 57: backend.proto.customer.v2.ListLeadsRequest.Filter.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	93,  // 58: backend.proto.customer.v2.ListLeadsRequest.Filter.states:type_name -> backend.proto.customer.v2.Lead.State
	9,   // 59: backend.proto.customer.v2.ListLeadsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListLeadsRequest.Sorting.Field
	10,  // 60: backend.proto.customer.v2.ListLeadsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.ListLeadsRequest.Sorting.Direction
	87,  // 61: backend.proto.customer.v2.UpdateLeadRequest.UpdateRef.custom_fields:type_name -> google.protobuf.Struct
	93,  // 62: backend.proto.customer.v2.UpdateLeadRequest.UpdateRef.state:type_name -> backend.proto.customer.v2.Lead.State
	94,  // 63: backend.proto.customer.v2.ListAddressesRequest.Filter.states:type_name -> backend.proto.customer.v2.Address.State
	11,  // 64: backend.proto.customer.v2.ListAddressesRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListAddressesRequest.Sorting.Field
	12,  // 65: backend.proto.customer.v2.ListAddressesRequest.Sorting.direction:type_name -> backend.proto.customer.v2.ListAddressesRequest.Sorting.Direction
	95,  // 66: backend.proto.customer.v2.UpdateAddressRequest.UpdateRef.address:type_name -> google.type.PostalAddress
	96,  // 67: backend.proto.customer.v2.UpdateAddressRequest.UpdateRef.type:type_name -> backend.proto.customer.v2.Address.Type
	94,  // 68: backend.proto.customer.v2.UpdateAddressRequest.UpdateRef.state:type_name -> backend.proto.customer.v2.Address.State
	85,  // 69: backend.proto.customer.v2.ListCustomFieldsRequest.Filter.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	97,  // 70: backend.proto.customer.v2.ListCustomFieldsRequest.Filter.association_type:type_name -> backend.proto.customer.v2.CustomField.AssociationType
	98,  // 71: backend.proto.customer.v2.ListCustomFieldsRequest.Filter.types:type_name -> backend.proto.customer.v2.CustomField.Type
	99,  // 72: backend.proto.customer.v2.ListCustomFieldsRequest.Filter.states:type_name -> backend.proto.customer.v2.CustomField.State
	13,  // 73: backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.Field
	14,  // 74: backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.Direction
	98,  // 75: backend.proto.customer.v2.UpdateCustomFieldRequest.UpdateRef.type:type_name -> backend.proto.customer.v2.CustomField.Type
	99,  // 76: backend.proto.customer.v2.UpdateCustomFieldRequest.UpdateRef.state:type_name -> backend.proto.customer.v2.CustomField.State
	15,  // 77: backend.proto.customer.v2.MetadataService.CreateCustomer:input_type -> backend.proto.customer.v2.CreateCustomerRequest
	16,  // 78: backend.proto.customer.v2.MetadataService.GetCustomer:input_type -> backend.proto.customer.v2.GetCustomerRequest
	17,  // 79: backend.proto.customer.v2.MetadataService.ListCustomers:input_type -> backend.proto.customer.v2.ListCustomersRequest
	19,  // 80: backend.proto.customer.v2.MetadataService.UpdateCustomer:input_type -> backend.proto.customer.v2.UpdateCustomerRequest
	20,  // 81: backend.proto.customer.v2.MetadataService.DeleteCustomer:input_type -> backend.proto.customer.v2.DeleteCustomerRequest
	21,  // 82: backend.proto.customer.v2.MetadataService.CreateContact:input_type -> backend.proto.customer.v2.CreateContactRequest
	22,  // 83: backend.proto.customer.v2.MetadataService.GetContact:input_type -> backend.proto.customer.v2.GetContactRequest
	23,  // 84: backend.proto.customer.v2.MetadataService.ListContacts:input_type -> backend.proto.customer.v2.ListContactsRequest
	25,  // 85: backend.proto.customer.v2.MetadataService.UpdateContact:input_type -> backend.proto.customer.v2.UpdateContactRequest
	26,  // 86: backend.proto.customer.v2.MetadataService.DeleteContact:input_type -> backend.proto.customer.v2.DeleteContactRequest
	27,  // 87: backend.proto.customer.v2.MetadataService.CreateCustomerRelatedData:input_type -> backend.proto.customer.v2.CreateCustomerRelatedDataRequest
	28,  // 88: backend.proto.customer.v2.MetadataService.GetCustomerRelatedData:input_type -> backend.proto.customer.v2.GetCustomerRelatedDataRequest
	29,  // 89: backend.proto.customer.v2.MetadataService.ListCustomerRelatedData:input_type -> backend.proto.customer.v2.ListCustomerRelatedDataRequest
	31,  // 90: backend.proto.customer.v2.MetadataService.UpdateCustomerRelatedData:input_type -> backend.proto.customer.v2.UpdateCustomerRelatedDataRequest
	32,  // 91: backend.proto.customer.v2.MetadataService.DeleteCustomerRelatedData:input_type -> backend.proto.customer.v2.DeleteCustomerRelatedDataRequest
	33,  // 92: backend.proto.customer.v2.MetadataService.CreateContactTag:input_type -> backend.proto.customer.v2.CreateContactTagRequest
	34,  // 93: backend.proto.customer.v2.MetadataService.GetContactTag:input_type -> backend.proto.customer.v2.GetContactTagRequest
	35,  // 94: backend.proto.customer.v2.MetadataService.ListContactTags:input_type -> backend.proto.customer.v2.ListContactTagsRequest
	37,  // 95: backend.proto.customer.v2.MetadataService.UpdateContactTag:input_type -> backend.proto.customer.v2.UpdateContactTagRequest
	38,  // 96: backend.proto.customer.v2.MetadataService.DeleteContactTag:input_type -> backend.proto.customer.v2.DeleteContactTagRequest
	39,  // 97: backend.proto.customer.v2.MetadataService.CreateLead:input_type -> backend.proto.customer.v2.CreateLeadRequest
	40,  // 98: backend.proto.customer.v2.MetadataService.GetLead:input_type -> backend.proto.customer.v2.GetLeadRequest
	41,  // 99: backend.proto.customer.v2.MetadataService.ListLeads:input_type -> backend.proto.customer.v2.ListLeadsRequest
	43,  // 100: backend.proto.customer.v2.MetadataService.UpdateLead:input_type -> backend.proto.customer.v2.UpdateLeadRequest
	44,  // 101: backend.proto.customer.v2.MetadataService.DeleteLead:input_type -> backend.proto.customer.v2.DeleteLeadRequest
	45,  // 102: backend.proto.customer.v2.MetadataService.CreateAddress:input_type -> backend.proto.customer.v2.CreateAddressRequest
	46,  // 103: backend.proto.customer.v2.MetadataService.GetAddress:input_type -> backend.proto.customer.v2.GetAddressRequest
	47,  // 104: backend.proto.customer.v2.MetadataService.ListAddresses:input_type -> backend.proto.customer.v2.ListAddressesRequest
	49,  // 105: backend.proto.customer.v2.MetadataService.UpdateAddress:input_type -> backend.proto.customer.v2.UpdateAddressRequest
	50,  // 106: backend.proto.customer.v2.MetadataService.DeleteAddress:input_type -> backend.proto.customer.v2.DeleteAddressRequest
	51,  // 107: backend.proto.customer.v2.MetadataService.CreateCustomField:input_type -> backend.proto.customer.v2.CreateCustomFieldRequest
	52,  // 108: backend.proto.customer.v2.MetadataService.GetCustomField:input_type -> backend.proto.customer.v2.GetCustomFieldRequest
	53,  // 109: backend.proto.customer.v2.MetadataService.ListCustomFields:input_type -> backend.proto.customer.v2.ListCustomFieldsRequest
	55,  // 110: backend.proto.customer.v2.MetadataService.UpdateCustomField:input_type -> backend.proto.customer.v2.UpdateCustomFieldRequest
	56,  // 111: backend.proto.customer.v2.MetadataService.DeleteCustomField:input_type -> backend.proto.customer.v2.DeleteCustomFieldRequest
	78,  // 112: backend.proto.customer.v2.MetadataService.CreateCustomer:output_type -> backend.proto.customer.v2.Customer
	78,  // 113: backend.proto.customer.v2.MetadataService.GetCustomer:output_type -> backend.proto.customer.v2.Customer
	18,  // 114: backend.proto.customer.v2.MetadataService.ListCustomers:output_type -> backend.proto.customer.v2.ListCustomersResponse
	78,  // 115: backend.proto.customer.v2.MetadataService.UpdateCustomer:output_type -> backend.proto.customer.v2.Customer
	100, // 116: backend.proto.customer.v2.MetadataService.DeleteCustomer:output_type -> google.protobuf.Empty
	79,  // 117: backend.proto.customer.v2.MetadataService.CreateContact:output_type -> backend.proto.customer.v2.Contact
	79,  // 118: backend.proto.customer.v2.MetadataService.GetContact:output_type -> backend.proto.customer.v2.Contact
	24,  // 119: backend.proto.customer.v2.MetadataService.ListContacts:output_type -> backend.proto.customer.v2.ListContactsResponse
	79,  // 120: backend.proto.customer.v2.MetadataService.UpdateContact:output_type -> backend.proto.customer.v2.Contact
	100, // 121: backend.proto.customer.v2.MetadataService.DeleteContact:output_type -> google.protobuf.Empty
	80,  // 122: backend.proto.customer.v2.MetadataService.CreateCustomerRelatedData:output_type -> backend.proto.customer.v2.CustomerRelatedData
	80,  // 123: backend.proto.customer.v2.MetadataService.GetCustomerRelatedData:output_type -> backend.proto.customer.v2.CustomerRelatedData
	30,  // 124: backend.proto.customer.v2.MetadataService.ListCustomerRelatedData:output_type -> backend.proto.customer.v2.ListCustomerRelatedDataResponse
	80,  // 125: backend.proto.customer.v2.MetadataService.UpdateCustomerRelatedData:output_type -> backend.proto.customer.v2.CustomerRelatedData
	100, // 126: backend.proto.customer.v2.MetadataService.DeleteCustomerRelatedData:output_type -> google.protobuf.Empty
	81,  // 127: backend.proto.customer.v2.MetadataService.CreateContactTag:output_type -> backend.proto.customer.v2.ContactTag
	81,  // 128: backend.proto.customer.v2.MetadataService.GetContactTag:output_type -> backend.proto.customer.v2.ContactTag
	36,  // 129: backend.proto.customer.v2.MetadataService.ListContactTags:output_type -> backend.proto.customer.v2.ListContactTagsResponse
	81,  // 130: backend.proto.customer.v2.MetadataService.UpdateContactTag:output_type -> backend.proto.customer.v2.ContactTag
	100, // 131: backend.proto.customer.v2.MetadataService.DeleteContactTag:output_type -> google.protobuf.Empty
	82,  // 132: backend.proto.customer.v2.MetadataService.CreateLead:output_type -> backend.proto.customer.v2.Lead
	82,  // 133: backend.proto.customer.v2.MetadataService.GetLead:output_type -> backend.proto.customer.v2.Lead
	42,  // 134: backend.proto.customer.v2.MetadataService.ListLeads:output_type -> backend.proto.customer.v2.ListLeadsResponse
	82,  // 135: backend.proto.customer.v2.MetadataService.UpdateLead:output_type -> backend.proto.customer.v2.Lead
	100, // 136: backend.proto.customer.v2.MetadataService.DeleteLead:output_type -> google.protobuf.Empty
	83,  // 137: backend.proto.customer.v2.MetadataService.CreateAddress:output_type -> backend.proto.customer.v2.Address
	83,  // 138: backend.proto.customer.v2.MetadataService.GetAddress:output_type -> backend.proto.customer.v2.Address
	48,  // 139: backend.proto.customer.v2.MetadataService.ListAddresses:output_type -> backend.proto.customer.v2.ListAddressesResponse
	83,  // 140: backend.proto.customer.v2.MetadataService.UpdateAddress:output_type -> backend.proto.customer.v2.Address
	100, // 141: backend.proto.customer.v2.MetadataService.DeleteAddress:output_type -> google.protobuf.Empty
	84,  // 142: backend.proto.customer.v2.MetadataService.CreateCustomField:output_type -> backend.proto.customer.v2.CustomField
	84,  // 143: backend.proto.customer.v2.MetadataService.GetCustomField:output_type -> backend.proto.customer.v2.CustomField
	54,  // 144: backend.proto.customer.v2.MetadataService.ListCustomFields:output_type -> backend.proto.customer.v2.ListCustomFieldsResponse
	84,  // 145: backend.proto.customer.v2.MetadataService.UpdateCustomField:output_type -> backend.proto.customer.v2.CustomField
	100, // 146: backend.proto.customer.v2.MetadataService.DeleteCustomField:output_type -> google.protobuf.Empty
	112, // [112:147] is the sub-list for method output_type
	77,  // [77:112] is the sub-list for method input_type
	77,  // [77:77] is the sub-list for extension type_name
	77,  // [77:77] is the sub-list for extension extendee
	0,   // [0:77] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_metadata_service_proto_init() }
func file_backend_proto_customer_v2_metadata_service_proto_init() {
	if File_backend_proto_customer_v2_metadata_service_proto != nil {
		return
	}
	file_backend_proto_customer_v2_metadata_proto_init()
	file_backend_proto_customer_v2_common_proto_init()
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[3].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[9].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[15].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[21].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[27].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[33].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[39].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[44].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[47].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[50].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[51].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[53].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[56].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[59].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[60].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[62].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_metadata_service_proto_rawDesc), len(file_backend_proto_customer_v2_metadata_service_proto_rawDesc)),
			NumEnums:      15,
			NumMessages:   63,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_customer_v2_metadata_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_metadata_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v2_metadata_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v2_metadata_service_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_metadata_service_proto = out.File
	file_backend_proto_customer_v2_metadata_service_proto_goTypes = nil
	file_backend_proto_customer_v2_metadata_service_proto_depIdxs = nil
}
