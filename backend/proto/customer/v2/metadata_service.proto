syntax = "proto3";

package backend.proto.customer.v2;

import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "backend/proto/customer/v2/metadata.proto";
import "backend/proto/customer/v2/common.proto";
import "buf/validate/validate.proto";
import "google/protobuf/struct.proto";
import "google/type/phone_number.proto";
import "google/type/postal_address.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v2";

// MetadataService 提供客户元数据管理功能
service MetadataService {
  // ==================== Customer Management ====================
  // CreateCustomer 
  rpc CreateCustomer(CreateCustomerRequest) returns (Customer);

  // GetCustomer 
  // get customer by unique identification
  // if customer not found, return error
  rpc GetCustomer(GetCustomerRequest) returns (Customer);
  
  // ListCustomers 
  // list customers by filter
  // if customer not found, return empty list, not return error
  rpc ListCustomers(ListCustomersRequest) returns (ListCustomersResponse);
  
  // UpdateCustomer 更新客户
  rpc UpdateCustomer(UpdateCustomerRequest) returns (Customer);
  
  // DeleteCustomer 删除客户
  rpc DeleteCustomer(DeleteCustomerRequest) returns (google.protobuf.Empty);
  
  // ==================== Contact Management ====================
  // CreateContact 创建联系人
  rpc CreateContact(CreateContactRequest) returns (Contact);
  
  // GetContact 获取联系人
  rpc GetContact(GetContactRequest) returns (Contact);
  
  // ListContacts 列出联系人
  rpc ListContacts(ListContactsRequest) returns (ListContactsResponse);
  
  // UpdateContact 更新联系人
  rpc UpdateContact(UpdateContactRequest) returns (Contact);
  
  // DeleteContact 删除联系人
  rpc DeleteContact(DeleteContactRequest) returns (google.protobuf.Empty);

  // ==================== CustomerRelatedData Management ====================
  // CreateCustomerRelatedData 创建客户相关数据
  rpc CreateCustomerRelatedData(CreateCustomerRelatedDataRequest) returns (CustomerRelatedData);
  
  // GetCustomerRelatedData 获取客户相关数据
  rpc GetCustomerRelatedData(GetCustomerRelatedDataRequest) returns (CustomerRelatedData);
  
  // ListCustomerRelatedData 列出客户相关数据
  rpc ListCustomerRelatedData(ListCustomerRelatedDataRequest) returns (ListCustomerRelatedDataResponse);
  
  // UpdateCustomerRelatedData 更新客户相关数据
  rpc UpdateCustomerRelatedData(UpdateCustomerRelatedDataRequest) returns (CustomerRelatedData);
  
  // DeleteCustomerRelatedData 删除客户相关数据
  rpc DeleteCustomerRelatedData(DeleteCustomerRelatedDataRequest) returns (google.protobuf.Empty);

  // ==================== Contact Tag Management ====================
  // CreateContactTag 创建标签
  rpc CreateContactTag(CreateContactTagRequest) returns (ContactTag);
  
  // GetContactTag 获取标签
  rpc GetContactTag(GetContactTagRequest) returns (ContactTag);

  // ListContactTags 列出标签
  rpc ListContactTags(ListContactTagsRequest) returns (ListContactTagsResponse);
  
  // UpdateContactTag 更新标签
  rpc UpdateContactTag(UpdateContactTagRequest) returns (ContactTag);
  
  // DeleteContactTag 删除标签
  rpc DeleteContactTag(DeleteContactTagRequest) returns (google.protobuf.Empty);
  
  // ==================== Lead Management ====================
  // CreateLead 创建线索
  rpc CreateLead(CreateLeadRequest) returns (Lead);

  // GetLead 获取线索
  rpc GetLead(GetLeadRequest) returns (Lead);

  // ListLeads 列出线索
  rpc ListLeads(ListLeadsRequest) returns (ListLeadsResponse);

  // UpdateLead 更新线索
  rpc UpdateLead(UpdateLeadRequest) returns (Lead);

  // DeleteLead 删除线索
  rpc DeleteLead(DeleteLeadRequest) returns (google.protobuf.Empty);

  // ==================== Address Management ====================
  // CreateAddress 创建地址
  rpc CreateAddress(CreateAddressRequest) returns (Address);

  // GetAddress 获取地址
  rpc GetAddress(GetAddressRequest) returns (Address);

  // ListAddresses 列出地址
  rpc ListAddresses(ListAddressesRequest) returns (ListAddressesResponse);

  // UpdateAddress 更新地址
  rpc UpdateAddress(UpdateAddressRequest) returns (Address);

  // DeleteAddress 删除地址
  rpc DeleteAddress(DeleteAddressRequest) returns (google.protobuf.Empty);

  // ==================== Custom Field Management ====================
  // CreateCustomField 创建自定义字段
  rpc CreateCustomField(CreateCustomFieldRequest) returns (CustomField);
  
  // GetCustomField 获取自定义字段
  rpc GetCustomField(GetCustomFieldRequest) returns (CustomField);
  
  // ListCustomFields 列出自定义字段
  rpc ListCustomFields(ListCustomFieldsRequest) returns (ListCustomFieldsResponse);
  
  // UpdateCustomField 更新自定义字段
  rpc UpdateCustomField(UpdateCustomFieldRequest) returns (CustomField);
  
  // DeleteCustomField 删除自定义字段
  rpc DeleteCustomField(DeleteCustomFieldRequest) returns (google.protobuf.Empty);
}

// ==================== Customer Request/Response Messages ====================
// CreateCustomerRequest 创建客户请求
message CreateCustomerRequest {
  // 客户信息
  Customer customer = 1 [(google.api.field_behavior) = REQUIRED];
}

// GetCustomerRequest 获取客户请求
message GetCustomerRequest {
  // 客户ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ListCustomersRequest 列出客户请求
message ListCustomersRequest {
  // filter
  message Filter {
    // customer id list
    repeated int64 ids = 1;
    // 组织引用
    OrganizationRef organization = 2 [(buf.validate.field).required = true];
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // 状态
    // 如果states为空, 则返回所有状态的客户, 没有默认值
    repeated Customer.State states = 3;
    // 生命周期ID
    repeated int64 lifecycle_ids = 4;
    // 负责人员工ID
    repeated int64 owner_staff_ids = 5;
    // 推荐来源ID
    repeated int64 referral_source_ids = 6;
  }
  // sorting
  message Sorting {
    // sorting field
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 客户ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // sorting direction
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
   // 是否返回总数量, 默认不返回, 会影响性能
   bool return_total_size = 5;
}

// ListCustomersResponse 列出客户响应
message ListCustomersResponse {
  // 客户列表
  repeated Customer customers = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateCustomerRequest 更新客户请求
message UpdateCustomerRequest {
  // update ref
  message UpdateRef {
    // 客户名称
    optional string given_name = 1;
    // 客户姓氏
    optional string family_name = 2;
    // 客户自定义字段
    optional google.protobuf.Struct custom_fields = 3;
    // 客户生命周期ID
    optional int64 lifecycle_id = 4;
    // 负责人员工ID
    optional int64 owner_staff_id = 5;
    // 行动状态ID
    optional int64 action_state_id = 6;
    // 头像路径
    optional string avatar_path = 7 [(buf.validate.field).string.max_len = 255];
    // 推荐来源ID
    optional int64 referral_source_id = 8 [(buf.validate.field).int64.gt = 0];
    // state
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    optional Customer.State state = 9;
  }
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // ref 
  UpdateRef ref = 2 [(buf.validate.field).required = true];
}

// DeleteCustomerRequest 删除客户请求
message DeleteCustomerRequest {
  // 客户ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ==================== Contact Request/Response Messages ====================
// CreateContactRequest 创建联系人请求
message CreateContactRequest {
  // 联系人信息
  Contact contact = 1 [(google.api.field_behavior) = REQUIRED];
}

// GetContactRequest 获取联系人请求
message GetContactRequest {
  // 联系人ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ListContactsRequest 列出联系人请求
message ListContactsRequest {
  // filter
  message Filter {
    // 联系人ID列表
    repeated int64 ids = 1;
    // 所属客户ID列表
    repeated int64 customer_ids = 2;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // 状态
    // 如果states为空, 则返回所有状态的联系人, 没有默认值
    repeated Contact.State states = 3;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 联系人ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListContactsResponse 列出联系人响应
message ListContactsResponse {
  // 联系人列表
  repeated Contact contacts = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateContactRequest 更新联系人请求
message UpdateContactRequest {
  // update ref
  message UpdateRef {
    // 名字
    optional string given_name = 1;
    // 姓氏
    optional string family_name = 2;
    // 邮箱
    optional string email = 3;
    // 手机号
    optional google.type.PhoneNumber phone = 4;
    // 标签列表
    repeated int64 tag_ids = 5;
    // 备注
    optional string note = 6;
    // state
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for updating, not for output --)
    optional Contact.State state = 7;
  }
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // ref
  UpdateRef ref = 2 [(buf.validate.field).required = true];
}

// DeleteContactRequest 删除联系人请求
message DeleteContactRequest {
  // 联系人ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ==================== CustomerRelatedData Request/Response Messages ====================
// CreateCustomerRelatedDataRequest 创建客户相关数据请求
message CreateCustomerRelatedDataRequest {
  // 客户相关数据信息
  CustomerRelatedData customer_related_data = 1 [(google.api.field_behavior) = REQUIRED];
}

// GetCustomerRelatedDataRequest 获取客户相关数据请求
message GetCustomerRelatedDataRequest {
  // 客户相关数据ID
  int64 customer_id = 1 [(buf.validate.field).int64.gt = 0];
}

// ListCustomerRelatedDataRequest 列出客户相关数据请求
message ListCustomerRelatedDataRequest {
  // filter
  message Filter {
    // 客户相关数据ID列表
    repeated int64 ids = 1;
    // 客户ID列表
    repeated int64 customer_ids = 2;
    // 组织引用
    OrganizationRef organization = 3;
    // 商家ID列表
    repeated int32 business_ids = 4;
    // 公司ID列表 
    repeated int64 company_ids = 5;
    // 状态列表
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    repeated CustomerRelatedData.State states = 6;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 客户相关数据ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListCustomerRelatedDataResponse 列出客户相关数据响应
message ListCustomerRelatedDataResponse {
  // 客户相关数据列表
  repeated CustomerRelatedData customer_related_data = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateCustomerRelatedDataRequest 更新客户相关数据请求
message UpdateCustomerRelatedDataRequest {
  // update ref
  message UpdateRef {
    // 客户颜色
    optional string client_color = 1;
    // 是否屏蔽消息
    optional int32 is_block_message = 2;
    // 是否屏蔽在线预约
    optional int32 is_block_online_booking = 3;
    // 登录邮箱
    optional string login_email = 4;
    // 推荐来源ID
    optional int32 referral_source_id = 5;
    // 推荐来源描述
    optional string referral_source_desc = 6;
    // 发送自动邮件
    optional int32 send_auto_email = 7;
    // 发送自动短信
    optional int32 send_auto_message = 8;
    // 发送app自动短信
    optional int32 send_app_auto_message = 9;
    // 首选美容师ID
    optional int32 preferred_groomer_id = 10;
    // 首选频率天数
    optional int32 preferred_frequency_day = 11;
    // 首选频率类型
    optional int32 preferred_frequency_type = 12;
    // 首选天
    optional string preferred_day = 13;
    // 首选时间
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: this is json string not a time --)
    optional string preferred_time = 14;
    // 是否退订
    optional bool is_unsubscribed = 15;
    // 生日
    // (-- api-linter: core::0142::time-field-names=disabled
    //     aip.dev/not-precedent: this is not a time field --)
    optional google.protobuf.Timestamp birthday = 16;
    // 自定义生命周期ID
    optional int64 customize_life_cycle_id = 17;
    // 自定义行动状态ID
    optional int64 customize_action_state_id = 18;
    // state
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for updating, not for output --)
    optional CustomerRelatedData.State state = 19;
  }
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // ref
  UpdateRef ref = 2 [(buf.validate.field).required = true];
}

// DeleteCustomerRelatedDataRequest 删除客户相关数据请求
message DeleteCustomerRelatedDataRequest {
  // 客户相关数据ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ==================== Contact Tag Request/Response Messages ====================
// CreateContactTagRequest 创建标签请求
message CreateContactTagRequest {
  // 标签信息
  ContactTag contact_tag = 1 [(google.api.field_behavior) = REQUIRED];
}

// GetContactTagRequest 获取标签请求
message GetContactTagRequest {
  // 标签ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ListContactTagsRequest 列出标签请求
message ListContactTagsRequest {
  // 过滤条件
  message Filter {
    // 标签ID列表
    repeated int64 ids = 1;
    // 组织引用 (必填, 如果为空会报错)
    OrganizationRef organization = 2 [(buf.validate.field).required = true];
    // name, is fuzzy search
    optional string name = 3;
    // state
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    repeated ContactTag.State states = 4;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 标签ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListContactTagsResponse 列出标签响应
message ListContactTagsResponse {
  // 标签列表
  repeated ContactTag contact_tags = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateContactTagRequest 更新标签请求
message UpdateContactTagRequest {
  // update ref
  message UpdateRef {
    // name
    optional string name = 1;
    // color
    optional string color = 2;
    // sort order
    optional int32 sort_order = 3;
    // state
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for updating, not for output --)
    optional ContactTag.State state = 4;
  }
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // ref
  UpdateRef ref = 2 [(buf.validate.field).required = true];
}

// DeleteContactTagRequest 删除标签请求
message DeleteContactTagRequest {
  // 标签ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ==================== Lead Request/Response Messages ====================
// CreateLeadRequest 创建线索请求
message CreateLeadRequest {
  // 线索信息
  Lead lead = 1 [(google.api.field_behavior) = REQUIRED];
}

// GetLeadRequest 获取线索请求
message GetLeadRequest {
  // 线索ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ListLeadsRequest 列出线索请求
message ListLeadsRequest {
  // filter
  message Filter {
    // 线索ID列表
    repeated int64 ids = 1;
    // 组织引用
    OrganizationRef organization = 2;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // 状态
    // 如果states为空, 则返回所有状态的lead, 没有默认值
    repeated Lead.State states = 3;
    // 生命周期ID
    repeated int64 lifecycle_ids = 4;
    // 负责人员工ID
    repeated int64 owner_staff_ids = 5;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 线索ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListLeadsResponse 列出线索响应
message ListLeadsResponse {
  // 线索列表
  repeated Lead leads = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateLeadRequest 更新线索请求
message UpdateLeadRequest {
  // update ref
  message UpdateRef {
    // 线索名称
    optional string given_name = 1;
    // 线索姓氏
    optional string family_name = 2;
    // 线索自定义字段
    optional google.protobuf.Struct custom_fields = 3;
    // 生命周期ID
    optional int64 lifecycle_id = 4;
    // 负责人员工ID
    optional int64 owner_staff_id = 5;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for updating, not for output --)
    // 状态
    optional Lead.State state = 6;
  }
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // ref
  UpdateRef ref = 2 [(buf.validate.field).required = true];
}

// DeleteLeadRequest 删除线索请求
message DeleteLeadRequest {
  // 线索ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ==================== Address Request/Response Messages ====================
// CreateAddressRequest 创建地址请求
message CreateAddressRequest {
  // 地址信息
  Address address = 1 [(google.api.field_behavior) = REQUIRED];
}

// GetAddressRequest 获取地址请求
message GetAddressRequest {
  // 地址ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ListAddressesRequest 列出地址请求
message ListAddressesRequest {
  // filter
  message Filter {
    // 地址ID列表
    repeated int64 ids = 1;
    // 所有者ID
    int64 customer_id = 2 [(buf.validate.field).int64.gt = 0];
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // state 
    // 如果states为空, 则返回所有状态的地址, 没有默认值
    repeated Address.State states = 3;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 地址ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListAddressesResponse 列出地址响应
message ListAddressesResponse {
  // 地址列表
  repeated Address addresses = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateAddressRequest 更新地址请求
message UpdateAddressRequest {
  // update ref
  message UpdateRef {
    // address 
    google.type.PostalAddress address = 1;
    // type
    optional Address.Type type = 2;
    // state
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for updating, not for output --)
    optional Address.State state = 3;
  }
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // ref
  UpdateRef ref = 2 [(buf.validate.field).required = true];
}

// DeleteAddressRequest 删除地址请求
message DeleteAddressRequest {
  // 地址ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ==================== Custom Field Request/Response Messages ====================
// CreateCustomFieldRequest 创建自定义字段请求
message CreateCustomFieldRequest {
  // 自定义字段信息
  CustomField custom_field = 1 [(google.api.field_behavior) = REQUIRED];
}

// GetCustomFieldRequest 获取自定义字段请求
message GetCustomFieldRequest {
  // 自定义字段ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ListCustomFieldsRequest 列出自定义字段请求
message ListCustomFieldsRequest {
  // filter
  message Filter {
    // 自定义字段ID列表
    repeated int64 ids = 1;
    // organization 
    OrganizationRef organization = 2 [(buf.validate.field).required = true];
    // 实体所有者
    backend.proto.customer.v2.CustomField.AssociationType association_type = 3;
    // 字段类型
    repeated CustomField.Type types = 4;
    // 是否必填
    optional bool is_required = 5;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // 状态
    repeated CustomField.State states = 6;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 自定义字段ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListCustomFieldsResponse 列出自定义字段响应
message ListCustomFieldsResponse {
  // 自定义字段列表
  repeated CustomField custom_fields = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateCustomFieldRequest 更新自定义字段请求
message UpdateCustomFieldRequest {
  // update ref
  message UpdateRef {
    // 字段名称
    optional string name = 1;
    // 字段类型
    optional CustomField.Type type = 2;
    // 是否必填
    optional bool is_required = 3;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for updating, not for output --)
    // 状态
    optional CustomField.State state = 4;
  }
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // ref
  UpdateRef ref = 2 [(buf.validate.field).required = true];
}

// DeleteCustomFieldRequest 删除自定义字段请求
message DeleteCustomFieldRequest {
  // 自定义字段ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
    // (-- api-linter: core::0126::unspecified=disabled
    //     aip.dev/not-precedent: We need to do this because
    //     the content of the error code is automatically generated by
    //     the script and is exclusive to each service.
    //     Please do not turn off this linter for the rest of the enum --)
    // customer 
    // 成功
    ERR_CODE_OK = 0;
    // 本服务自动分配的全局唯一的起始错误码
    ERR_CODE_UNSPECIFIED = 119500;
    // 客户不存在
    ERR_CODE_CUSTOMER_NOT_FOUND = 119501;
    // 客户已存在
    ERR_CODE_CUSTOMER_ALREADY_EXISTS = 119502;
    // 无效的客户ID
    ERR_CODE_INVALID_CUSTOMER_ID = 119503;
    // 无效的客户名称
    ERR_CODE_INVALID_CUSTOMER_NAME = 119504;
    // 客户已删除
    ERR_CODE_CUSTOMER_DELETED = 119505;
    // 创建客户失败
    ERR_CODE_CREATE_CUSTOMER_FAILED = 119506;
  
    // address 
    // 地址不存在
    ERR_CODE_ADDRESS_NOT_FOUND = 119510;
    // 无效的地址信息
    ERR_CODE_INVALID_ADDRESS = 119511;
    // 超出地址数量限制
    ERR_CODE_ADDRESS_LIMIT_EXCEEDED = 119512;
    // 不允许重复设置主地址
    ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS = 119513;
  
    // task
    // 任务不存在
    ERR_CODE_TASK_NOT_FOUND = 119520;
    // 任务已完成
    ERR_CODE_TASK_ALREADY_COMPLETED = 119521;
    // 无效的任务状态
    ERR_CODE_INVALID_TASK_STATUS = 119522;
  
    // source 
    // Action State Name 已经存在
    ERR_CODE_ACTION_STATE_NAME_EXIST = 119525;
    // Life Cycle Name 已经存在
    ERR_CODE_LIFE_CYCLE_NAME_EXIST = 119526;
    // View Name 已经存在
    ERR_CODE_VIEW_NAME_EXIST = 119527;
    // Source Name 已经存在
    ERR_CODE_SOURCE_NAME_EXIST = 119528;
    // Tag Name 已经存在
    ERR_CODE_TAG_NAME_EXIST = 119529;

    // contact
    // 联系人不存在
    ERR_CODE_CONTACT_NOT_FOUND = 119535;
    // 联系人已存在
    ERR_CODE_CONTACT_ALREADY_EXISTS = 119536;
    // 无效的联系人信息
    ERR_CODE_INVALID_CONTACT = 119537;
    // 更新的内容与该联系方式记录的内容不符 (即contact记录email就只能更新email)
    ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH = 119538;
  
    // lead
    // 线索不存在
    ERR_CODE_LEAD_NOT_FOUND = 119540;
    // 创建线索失败
    ERR_CODE_CREATE_LEAD_FAILED = 119541;

    // contact tag
    // 标签不存在
    ERR_CODE_CONTACT_TAG_NOT_FOUND = 119545;
    // 创建标签失败
    ERR_CODE_CREATE_CONTACT_TAG_FAILED = 119546;

    // custom field
    // 自定义字段不存在
    ERR_CODE_CUSTOM_FIELD_NOT_FOUND = 119550;
    // 创建自定义字段失败
    ERR_CODE_CREATE_CUSTOM_FIELD_FAILED = 119551;
    // 自定义字段已存在
    ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS = 119552;
    // 自定义字段已删除
    ERR_CODE_CUSTOM_FIELD_DELETED = 119553;
    // 自定义字段选项不存在
    ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND = 119554;
    // 自定义字段选项已删除
    ERR_CODE_CUSTOM_FIELD_OPTION_DELETED = 119555;

    // customer related data
    // 客户关联数据不存在
    ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND = 119560;
  }
  