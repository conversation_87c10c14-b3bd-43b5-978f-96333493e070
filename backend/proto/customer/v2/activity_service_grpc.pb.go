// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0148::human-names=disabled
//     aip.dev/not-precedent: 使用first_name和last_name作为字段名 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0132::request-field-types=disabled
//     aip.dev/not-precedent: 使用自定义过滤器类型 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用customer_id作为标识符 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用自定义响应字段 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 使用自定义请求字段结构 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)syntax = "proto3";
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 使用空resp易于拓展 --)syntax = "proto3";
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 不使用behavior --)syntax = "proto3";
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent:  使用自定义分页方式 --)syntax = "proto3";

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/customer/v2/activity_service.proto

package customerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ActivityService_CreateCustomerActivityLog_FullMethodName = "/backend.proto.customer.v2.ActivityService/CreateCustomerActivityLog"
	ActivityService_UpdateCustomerActivityLog_FullMethodName = "/backend.proto.customer.v2.ActivityService/UpdateCustomerActivityLog"
	ActivityService_ListCustomerActivityLogs_FullMethodName  = "/backend.proto.customer.v2.ActivityService/ListCustomerActivityLogs"
	ActivityService_ConvertCustomer_FullMethodName           = "/backend.proto.customer.v2.ActivityService/ConvertCustomer"
	ActivityService_ConvertCustomersAttribute_FullMethodName = "/backend.proto.customer.v2.ActivityService/ConvertCustomersAttribute"
	ActivityService_CreateCustomerTask_FullMethodName        = "/backend.proto.customer.v2.ActivityService/CreateCustomerTask"
	ActivityService_UpdateCustomerTask_FullMethodName        = "/backend.proto.customer.v2.ActivityService/UpdateCustomerTask"
	ActivityService_ListCustomerTasks_FullMethodName         = "/backend.proto.customer.v2.ActivityService/ListCustomerTasks"
	ActivityService_DeleteCustomerTask_FullMethodName        = "/backend.proto.customer.v2.ActivityService/DeleteCustomerTask"
	ActivityService_CreateLifeCycle_FullMethodName           = "/backend.proto.customer.v2.ActivityService/CreateLifeCycle"
	ActivityService_UpdateLifeCycles_FullMethodName          = "/backend.proto.customer.v2.ActivityService/UpdateLifeCycles"
	ActivityService_ListLifeCycles_FullMethodName            = "/backend.proto.customer.v2.ActivityService/ListLifeCycles"
	ActivityService_DeleteLifeCycle_FullMethodName           = "/backend.proto.customer.v2.ActivityService/DeleteLifeCycle"
	ActivityService_CreateActionState_FullMethodName         = "/backend.proto.customer.v2.ActivityService/CreateActionState"
	ActivityService_UpdateActionStates_FullMethodName        = "/backend.proto.customer.v2.ActivityService/UpdateActionStates"
	ActivityService_ListActionStates_FullMethodName          = "/backend.proto.customer.v2.ActivityService/ListActionStates"
	ActivityService_DeleteActionState_FullMethodName         = "/backend.proto.customer.v2.ActivityService/DeleteActionState"
	ActivityService_CreateTag_FullMethodName                 = "/backend.proto.customer.v2.ActivityService/CreateTag"
	ActivityService_UpdateTags_FullMethodName                = "/backend.proto.customer.v2.ActivityService/UpdateTags"
	ActivityService_ListTags_FullMethodName                  = "/backend.proto.customer.v2.ActivityService/ListTags"
	ActivityService_DeleteTag_FullMethodName                 = "/backend.proto.customer.v2.ActivityService/DeleteTag"
	ActivityService_CoverCustomerTag_FullMethodName          = "/backend.proto.customer.v2.ActivityService/CoverCustomerTag"
	ActivityService_BatchAddCustomersTags_FullMethodName     = "/backend.proto.customer.v2.ActivityService/BatchAddCustomersTags"
	ActivityService_CreateNote_FullMethodName                = "/backend.proto.customer.v2.ActivityService/CreateNote"
	ActivityService_UpdateNotes_FullMethodName               = "/backend.proto.customer.v2.ActivityService/UpdateNotes"
	ActivityService_ListNotes_FullMethodName                 = "/backend.proto.customer.v2.ActivityService/ListNotes"
	ActivityService_DeleteNote_FullMethodName                = "/backend.proto.customer.v2.ActivityService/DeleteNote"
	ActivityService_CreateSource_FullMethodName              = "/backend.proto.customer.v2.ActivityService/CreateSource"
	ActivityService_UpdateSources_FullMethodName             = "/backend.proto.customer.v2.ActivityService/UpdateSources"
	ActivityService_ListSources_FullMethodName               = "/backend.proto.customer.v2.ActivityService/ListSources"
	ActivityService_DeleteSource_FullMethodName              = "/backend.proto.customer.v2.ActivityService/DeleteSource"
)

// ActivityServiceClient is the client API for ActivityService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ActivityService 提供客户活动服务
type ActivityServiceClient interface {
	// ActivityLog
	// CreateCustomerActivityLog 创建客户活动日志
	CreateCustomerActivityLog(ctx context.Context, in *CreateCustomerActivityLogRequest, opts ...grpc.CallOption) (*CreateCustomerActivityLogResponse, error)
	// ListCustomerActivityLogs 获取客户历史记录
	UpdateCustomerActivityLog(ctx context.Context, in *UpdateCustomerActivityLogRequest, opts ...grpc.CallOption) (*UpdateCustomerActivityLogResponse, error)
	// ListCustomerActivityLogs 获取客户历史记录
	ListCustomerActivityLogs(ctx context.Context, in *ListCustomerActivityLogsRequest, opts ...grpc.CallOption) (*ListCustomerActivityLogsResponse, error)
	// Convert
	// ConvertCustomer 转换客户状态
	ConvertCustomer(ctx context.Context, in *ConvertCustomerRequest, opts ...grpc.CallOption) (*ConvertCustomerResponse, error)
	// ConvertCustomersAttribute 批量转换客户属性
	ConvertCustomersAttribute(ctx context.Context, in *ConvertCustomersAttributeRequest, opts ...grpc.CallOption) (*ConvertCustomersAttributeResponse, error)
	// Task
	// CreateCustomerTask 创建客户任务
	CreateCustomerTask(ctx context.Context, in *CreateCustomerTaskRequest, opts ...grpc.CallOption) (*CreateCustomerTaskResponse, error)
	// UpdateCustomerTask 更新客户任务
	UpdateCustomerTask(ctx context.Context, in *UpdateCustomerTaskRequest, opts ...grpc.CallOption) (*UpdateCustomerTaskResponse, error)
	// ListCustomerTasks 获取客户任务列表
	ListCustomerTasks(ctx context.Context, in *ListCustomerTasksRequest, opts ...grpc.CallOption) (*ListCustomerTasksResponse, error)
	// DeleteCustomerTask 删除客户任务列表
	DeleteCustomerTask(ctx context.Context, in *DeleteCustomerTaskRequest, opts ...grpc.CallOption) (*DeleteCustomerTaskResponse, error)
	// LifeCycle
	// CreateLifeCycle 创建生命周期
	CreateLifeCycle(ctx context.Context, in *CreateLifeCycleRequest, opts ...grpc.CallOption) (*CreateLifeCycleResponse, error)
	// UpdateLifeCycle 更新生命周期
	UpdateLifeCycles(ctx context.Context, in *UpdateLifeCyclesRequest, opts ...grpc.CallOption) (*UpdateLifeCyclesResponse, error)
	// ListLifeCycles 获取生命周期列表
	ListLifeCycles(ctx context.Context, in *ListLifeCyclesRequest, opts ...grpc.CallOption) (*ListLifeCyclesResponse, error)
	// DeleteLifeCycle 删除生命周期
	DeleteLifeCycle(ctx context.Context, in *DeleteLifeCycleRequest, opts ...grpc.CallOption) (*DeleteLifeCycleResponse, error)
	// ActionState
	// CreateActionState 创建行动状态
	CreateActionState(ctx context.Context, in *CreateActionStateRequest, opts ...grpc.CallOption) (*CreateActionStateResponse, error)
	// UpdateActionState 更新行动状态
	UpdateActionStates(ctx context.Context, in *UpdateActionStatesRequest, opts ...grpc.CallOption) (*UpdateActionsStatesResponse, error)
	// ListActionStates 获取行动状态列表
	ListActionStates(ctx context.Context, in *ListActionStatesRequest, opts ...grpc.CallOption) (*ListActionStatesResponse, error)
	// DeleteActionState 删除行动状态
	DeleteActionState(ctx context.Context, in *DeleteActionStateRequest, opts ...grpc.CallOption) (*DeleteActionStateResponse, error)
	// Tag
	// CreateTag 创建标签
	CreateTag(ctx context.Context, in *CreateTagRequest, opts ...grpc.CallOption) (*CreateTagResponse, error)
	// UpdateTags 批量更新标签
	UpdateTags(ctx context.Context, in *UpdateTagsRequest, opts ...grpc.CallOption) (*UpdateTagsResponse, error)
	// ListTags 获取标签列表
	ListTags(ctx context.Context, in *ListTagsRequest, opts ...grpc.CallOption) (*ListTagsResponse, error)
	// DeleteTag 删除标签
	DeleteTag(ctx context.Context, in *DeleteTagRequest, opts ...grpc.CallOption) (*DeleteTagResponse, error)
	// CoverCustomerTag 覆盖客户标签
	CoverCustomerTag(ctx context.Context, in *CoverCustomerTagRequest, opts ...grpc.CallOption) (*CoverCustomerTagResponse, error)
	// BatchAddCustomersTags 批量添加客户标签 upsert语义
	BatchAddCustomersTags(ctx context.Context, in *BatchAddCustomersTagsRequest, opts ...grpc.CallOption) (*BatchAddCustomersTagsResponse, error)
	// Note
	// CreateNote 创建备注
	CreateNote(ctx context.Context, in *CreateNoteRequest, opts ...grpc.CallOption) (*CreateNoteResponse, error)
	// UpdateNotes 批量更新备注
	UpdateNotes(ctx context.Context, in *UpdateNotesRequest, opts ...grpc.CallOption) (*UpdateNotesResponse, error)
	// ListNotes 获取备注列表
	ListNotes(ctx context.Context, in *ListNotesRequest, opts ...grpc.CallOption) (*ListNotesResponse, error)
	// DeleteNote 删除备注
	DeleteNote(ctx context.Context, in *DeleteNoteRequest, opts ...grpc.CallOption) (*DeleteNoteResponse, error)
	// Source
	// CreateSource 创建来源
	CreateSource(ctx context.Context, in *CreateSourceRequest, opts ...grpc.CallOption) (*CreateSourceResponse, error)
	// UpdateSources 批量更新来源
	UpdateSources(ctx context.Context, in *UpdateSourcesRequest, opts ...grpc.CallOption) (*UpdateSourcesResponse, error)
	// ListSources 获取来源列表
	ListSources(ctx context.Context, in *ListSourcesRequest, opts ...grpc.CallOption) (*ListSourcesResponse, error)
	// DeleteSource 删除来源
	DeleteSource(ctx context.Context, in *DeleteSourceRequest, opts ...grpc.CallOption) (*DeleteSourceResponse, error)
}

type activityServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewActivityServiceClient(cc grpc.ClientConnInterface) ActivityServiceClient {
	return &activityServiceClient{cc}
}

func (c *activityServiceClient) CreateCustomerActivityLog(ctx context.Context, in *CreateCustomerActivityLogRequest, opts ...grpc.CallOption) (*CreateCustomerActivityLogResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomerActivityLogResponse)
	err := c.cc.Invoke(ctx, ActivityService_CreateCustomerActivityLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) UpdateCustomerActivityLog(ctx context.Context, in *UpdateCustomerActivityLogRequest, opts ...grpc.CallOption) (*UpdateCustomerActivityLogResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCustomerActivityLogResponse)
	err := c.cc.Invoke(ctx, ActivityService_UpdateCustomerActivityLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ListCustomerActivityLogs(ctx context.Context, in *ListCustomerActivityLogsRequest, opts ...grpc.CallOption) (*ListCustomerActivityLogsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomerActivityLogsResponse)
	err := c.cc.Invoke(ctx, ActivityService_ListCustomerActivityLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ConvertCustomer(ctx context.Context, in *ConvertCustomerRequest, opts ...grpc.CallOption) (*ConvertCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConvertCustomerResponse)
	err := c.cc.Invoke(ctx, ActivityService_ConvertCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ConvertCustomersAttribute(ctx context.Context, in *ConvertCustomersAttributeRequest, opts ...grpc.CallOption) (*ConvertCustomersAttributeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConvertCustomersAttributeResponse)
	err := c.cc.Invoke(ctx, ActivityService_ConvertCustomersAttribute_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CreateCustomerTask(ctx context.Context, in *CreateCustomerTaskRequest, opts ...grpc.CallOption) (*CreateCustomerTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomerTaskResponse)
	err := c.cc.Invoke(ctx, ActivityService_CreateCustomerTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) UpdateCustomerTask(ctx context.Context, in *UpdateCustomerTaskRequest, opts ...grpc.CallOption) (*UpdateCustomerTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCustomerTaskResponse)
	err := c.cc.Invoke(ctx, ActivityService_UpdateCustomerTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ListCustomerTasks(ctx context.Context, in *ListCustomerTasksRequest, opts ...grpc.CallOption) (*ListCustomerTasksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomerTasksResponse)
	err := c.cc.Invoke(ctx, ActivityService_ListCustomerTasks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) DeleteCustomerTask(ctx context.Context, in *DeleteCustomerTaskRequest, opts ...grpc.CallOption) (*DeleteCustomerTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteCustomerTaskResponse)
	err := c.cc.Invoke(ctx, ActivityService_DeleteCustomerTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CreateLifeCycle(ctx context.Context, in *CreateLifeCycleRequest, opts ...grpc.CallOption) (*CreateLifeCycleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateLifeCycleResponse)
	err := c.cc.Invoke(ctx, ActivityService_CreateLifeCycle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) UpdateLifeCycles(ctx context.Context, in *UpdateLifeCyclesRequest, opts ...grpc.CallOption) (*UpdateLifeCyclesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateLifeCyclesResponse)
	err := c.cc.Invoke(ctx, ActivityService_UpdateLifeCycles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ListLifeCycles(ctx context.Context, in *ListLifeCyclesRequest, opts ...grpc.CallOption) (*ListLifeCyclesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListLifeCyclesResponse)
	err := c.cc.Invoke(ctx, ActivityService_ListLifeCycles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) DeleteLifeCycle(ctx context.Context, in *DeleteLifeCycleRequest, opts ...grpc.CallOption) (*DeleteLifeCycleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteLifeCycleResponse)
	err := c.cc.Invoke(ctx, ActivityService_DeleteLifeCycle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CreateActionState(ctx context.Context, in *CreateActionStateRequest, opts ...grpc.CallOption) (*CreateActionStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateActionStateResponse)
	err := c.cc.Invoke(ctx, ActivityService_CreateActionState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) UpdateActionStates(ctx context.Context, in *UpdateActionStatesRequest, opts ...grpc.CallOption) (*UpdateActionsStatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateActionsStatesResponse)
	err := c.cc.Invoke(ctx, ActivityService_UpdateActionStates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ListActionStates(ctx context.Context, in *ListActionStatesRequest, opts ...grpc.CallOption) (*ListActionStatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListActionStatesResponse)
	err := c.cc.Invoke(ctx, ActivityService_ListActionStates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) DeleteActionState(ctx context.Context, in *DeleteActionStateRequest, opts ...grpc.CallOption) (*DeleteActionStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteActionStateResponse)
	err := c.cc.Invoke(ctx, ActivityService_DeleteActionState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CreateTag(ctx context.Context, in *CreateTagRequest, opts ...grpc.CallOption) (*CreateTagResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTagResponse)
	err := c.cc.Invoke(ctx, ActivityService_CreateTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) UpdateTags(ctx context.Context, in *UpdateTagsRequest, opts ...grpc.CallOption) (*UpdateTagsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateTagsResponse)
	err := c.cc.Invoke(ctx, ActivityService_UpdateTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ListTags(ctx context.Context, in *ListTagsRequest, opts ...grpc.CallOption) (*ListTagsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTagsResponse)
	err := c.cc.Invoke(ctx, ActivityService_ListTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) DeleteTag(ctx context.Context, in *DeleteTagRequest, opts ...grpc.CallOption) (*DeleteTagResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteTagResponse)
	err := c.cc.Invoke(ctx, ActivityService_DeleteTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CoverCustomerTag(ctx context.Context, in *CoverCustomerTagRequest, opts ...grpc.CallOption) (*CoverCustomerTagResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CoverCustomerTagResponse)
	err := c.cc.Invoke(ctx, ActivityService_CoverCustomerTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) BatchAddCustomersTags(ctx context.Context, in *BatchAddCustomersTagsRequest, opts ...grpc.CallOption) (*BatchAddCustomersTagsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchAddCustomersTagsResponse)
	err := c.cc.Invoke(ctx, ActivityService_BatchAddCustomersTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CreateNote(ctx context.Context, in *CreateNoteRequest, opts ...grpc.CallOption) (*CreateNoteResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateNoteResponse)
	err := c.cc.Invoke(ctx, ActivityService_CreateNote_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) UpdateNotes(ctx context.Context, in *UpdateNotesRequest, opts ...grpc.CallOption) (*UpdateNotesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateNotesResponse)
	err := c.cc.Invoke(ctx, ActivityService_UpdateNotes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ListNotes(ctx context.Context, in *ListNotesRequest, opts ...grpc.CallOption) (*ListNotesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNotesResponse)
	err := c.cc.Invoke(ctx, ActivityService_ListNotes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) DeleteNote(ctx context.Context, in *DeleteNoteRequest, opts ...grpc.CallOption) (*DeleteNoteResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteNoteResponse)
	err := c.cc.Invoke(ctx, ActivityService_DeleteNote_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) CreateSource(ctx context.Context, in *CreateSourceRequest, opts ...grpc.CallOption) (*CreateSourceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateSourceResponse)
	err := c.cc.Invoke(ctx, ActivityService_CreateSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) UpdateSources(ctx context.Context, in *UpdateSourcesRequest, opts ...grpc.CallOption) (*UpdateSourcesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateSourcesResponse)
	err := c.cc.Invoke(ctx, ActivityService_UpdateSources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) ListSources(ctx context.Context, in *ListSourcesRequest, opts ...grpc.CallOption) (*ListSourcesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSourcesResponse)
	err := c.cc.Invoke(ctx, ActivityService_ListSources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityServiceClient) DeleteSource(ctx context.Context, in *DeleteSourceRequest, opts ...grpc.CallOption) (*DeleteSourceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSourceResponse)
	err := c.cc.Invoke(ctx, ActivityService_DeleteSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ActivityServiceServer is the server API for ActivityService service.
// All implementations must embed UnimplementedActivityServiceServer
// for forward compatibility.
//
// ActivityService 提供客户活动服务
type ActivityServiceServer interface {
	// ActivityLog
	// CreateCustomerActivityLog 创建客户活动日志
	CreateCustomerActivityLog(context.Context, *CreateCustomerActivityLogRequest) (*CreateCustomerActivityLogResponse, error)
	// ListCustomerActivityLogs 获取客户历史记录
	UpdateCustomerActivityLog(context.Context, *UpdateCustomerActivityLogRequest) (*UpdateCustomerActivityLogResponse, error)
	// ListCustomerActivityLogs 获取客户历史记录
	ListCustomerActivityLogs(context.Context, *ListCustomerActivityLogsRequest) (*ListCustomerActivityLogsResponse, error)
	// Convert
	// ConvertCustomer 转换客户状态
	ConvertCustomer(context.Context, *ConvertCustomerRequest) (*ConvertCustomerResponse, error)
	// ConvertCustomersAttribute 批量转换客户属性
	ConvertCustomersAttribute(context.Context, *ConvertCustomersAttributeRequest) (*ConvertCustomersAttributeResponse, error)
	// Task
	// CreateCustomerTask 创建客户任务
	CreateCustomerTask(context.Context, *CreateCustomerTaskRequest) (*CreateCustomerTaskResponse, error)
	// UpdateCustomerTask 更新客户任务
	UpdateCustomerTask(context.Context, *UpdateCustomerTaskRequest) (*UpdateCustomerTaskResponse, error)
	// ListCustomerTasks 获取客户任务列表
	ListCustomerTasks(context.Context, *ListCustomerTasksRequest) (*ListCustomerTasksResponse, error)
	// DeleteCustomerTask 删除客户任务列表
	DeleteCustomerTask(context.Context, *DeleteCustomerTaskRequest) (*DeleteCustomerTaskResponse, error)
	// LifeCycle
	// CreateLifeCycle 创建生命周期
	CreateLifeCycle(context.Context, *CreateLifeCycleRequest) (*CreateLifeCycleResponse, error)
	// UpdateLifeCycle 更新生命周期
	UpdateLifeCycles(context.Context, *UpdateLifeCyclesRequest) (*UpdateLifeCyclesResponse, error)
	// ListLifeCycles 获取生命周期列表
	ListLifeCycles(context.Context, *ListLifeCyclesRequest) (*ListLifeCyclesResponse, error)
	// DeleteLifeCycle 删除生命周期
	DeleteLifeCycle(context.Context, *DeleteLifeCycleRequest) (*DeleteLifeCycleResponse, error)
	// ActionState
	// CreateActionState 创建行动状态
	CreateActionState(context.Context, *CreateActionStateRequest) (*CreateActionStateResponse, error)
	// UpdateActionState 更新行动状态
	UpdateActionStates(context.Context, *UpdateActionStatesRequest) (*UpdateActionsStatesResponse, error)
	// ListActionStates 获取行动状态列表
	ListActionStates(context.Context, *ListActionStatesRequest) (*ListActionStatesResponse, error)
	// DeleteActionState 删除行动状态
	DeleteActionState(context.Context, *DeleteActionStateRequest) (*DeleteActionStateResponse, error)
	// Tag
	// CreateTag 创建标签
	CreateTag(context.Context, *CreateTagRequest) (*CreateTagResponse, error)
	// UpdateTags 批量更新标签
	UpdateTags(context.Context, *UpdateTagsRequest) (*UpdateTagsResponse, error)
	// ListTags 获取标签列表
	ListTags(context.Context, *ListTagsRequest) (*ListTagsResponse, error)
	// DeleteTag 删除标签
	DeleteTag(context.Context, *DeleteTagRequest) (*DeleteTagResponse, error)
	// CoverCustomerTag 覆盖客户标签
	CoverCustomerTag(context.Context, *CoverCustomerTagRequest) (*CoverCustomerTagResponse, error)
	// BatchAddCustomersTags 批量添加客户标签 upsert语义
	BatchAddCustomersTags(context.Context, *BatchAddCustomersTagsRequest) (*BatchAddCustomersTagsResponse, error)
	// Note
	// CreateNote 创建备注
	CreateNote(context.Context, *CreateNoteRequest) (*CreateNoteResponse, error)
	// UpdateNotes 批量更新备注
	UpdateNotes(context.Context, *UpdateNotesRequest) (*UpdateNotesResponse, error)
	// ListNotes 获取备注列表
	ListNotes(context.Context, *ListNotesRequest) (*ListNotesResponse, error)
	// DeleteNote 删除备注
	DeleteNote(context.Context, *DeleteNoteRequest) (*DeleteNoteResponse, error)
	// Source
	// CreateSource 创建来源
	CreateSource(context.Context, *CreateSourceRequest) (*CreateSourceResponse, error)
	// UpdateSources 批量更新来源
	UpdateSources(context.Context, *UpdateSourcesRequest) (*UpdateSourcesResponse, error)
	// ListSources 获取来源列表
	ListSources(context.Context, *ListSourcesRequest) (*ListSourcesResponse, error)
	// DeleteSource 删除来源
	DeleteSource(context.Context, *DeleteSourceRequest) (*DeleteSourceResponse, error)
	mustEmbedUnimplementedActivityServiceServer()
}

// UnimplementedActivityServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedActivityServiceServer struct{}

func (UnimplementedActivityServiceServer) CreateCustomerActivityLog(context.Context, *CreateCustomerActivityLogRequest) (*CreateCustomerActivityLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerActivityLog not implemented")
}
func (UnimplementedActivityServiceServer) UpdateCustomerActivityLog(context.Context, *UpdateCustomerActivityLogRequest) (*UpdateCustomerActivityLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerActivityLog not implemented")
}
func (UnimplementedActivityServiceServer) ListCustomerActivityLogs(context.Context, *ListCustomerActivityLogsRequest) (*ListCustomerActivityLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerActivityLogs not implemented")
}
func (UnimplementedActivityServiceServer) ConvertCustomer(context.Context, *ConvertCustomerRequest) (*ConvertCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertCustomer not implemented")
}
func (UnimplementedActivityServiceServer) ConvertCustomersAttribute(context.Context, *ConvertCustomersAttributeRequest) (*ConvertCustomersAttributeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertCustomersAttribute not implemented")
}
func (UnimplementedActivityServiceServer) CreateCustomerTask(context.Context, *CreateCustomerTaskRequest) (*CreateCustomerTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerTask not implemented")
}
func (UnimplementedActivityServiceServer) UpdateCustomerTask(context.Context, *UpdateCustomerTaskRequest) (*UpdateCustomerTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerTask not implemented")
}
func (UnimplementedActivityServiceServer) ListCustomerTasks(context.Context, *ListCustomerTasksRequest) (*ListCustomerTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerTasks not implemented")
}
func (UnimplementedActivityServiceServer) DeleteCustomerTask(context.Context, *DeleteCustomerTaskRequest) (*DeleteCustomerTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomerTask not implemented")
}
func (UnimplementedActivityServiceServer) CreateLifeCycle(context.Context, *CreateLifeCycleRequest) (*CreateLifeCycleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLifeCycle not implemented")
}
func (UnimplementedActivityServiceServer) UpdateLifeCycles(context.Context, *UpdateLifeCyclesRequest) (*UpdateLifeCyclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLifeCycles not implemented")
}
func (UnimplementedActivityServiceServer) ListLifeCycles(context.Context, *ListLifeCyclesRequest) (*ListLifeCyclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLifeCycles not implemented")
}
func (UnimplementedActivityServiceServer) DeleteLifeCycle(context.Context, *DeleteLifeCycleRequest) (*DeleteLifeCycleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLifeCycle not implemented")
}
func (UnimplementedActivityServiceServer) CreateActionState(context.Context, *CreateActionStateRequest) (*CreateActionStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateActionState not implemented")
}
func (UnimplementedActivityServiceServer) UpdateActionStates(context.Context, *UpdateActionStatesRequest) (*UpdateActionsStatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateActionStates not implemented")
}
func (UnimplementedActivityServiceServer) ListActionStates(context.Context, *ListActionStatesRequest) (*ListActionStatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListActionStates not implemented")
}
func (UnimplementedActivityServiceServer) DeleteActionState(context.Context, *DeleteActionStateRequest) (*DeleteActionStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteActionState not implemented")
}
func (UnimplementedActivityServiceServer) CreateTag(context.Context, *CreateTagRequest) (*CreateTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTag not implemented")
}
func (UnimplementedActivityServiceServer) UpdateTags(context.Context, *UpdateTagsRequest) (*UpdateTagsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTags not implemented")
}
func (UnimplementedActivityServiceServer) ListTags(context.Context, *ListTagsRequest) (*ListTagsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTags not implemented")
}
func (UnimplementedActivityServiceServer) DeleteTag(context.Context, *DeleteTagRequest) (*DeleteTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTag not implemented")
}
func (UnimplementedActivityServiceServer) CoverCustomerTag(context.Context, *CoverCustomerTagRequest) (*CoverCustomerTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CoverCustomerTag not implemented")
}
func (UnimplementedActivityServiceServer) BatchAddCustomersTags(context.Context, *BatchAddCustomersTagsRequest) (*BatchAddCustomersTagsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchAddCustomersTags not implemented")
}
func (UnimplementedActivityServiceServer) CreateNote(context.Context, *CreateNoteRequest) (*CreateNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNote not implemented")
}
func (UnimplementedActivityServiceServer) UpdateNotes(context.Context, *UpdateNotesRequest) (*UpdateNotesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNotes not implemented")
}
func (UnimplementedActivityServiceServer) ListNotes(context.Context, *ListNotesRequest) (*ListNotesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNotes not implemented")
}
func (UnimplementedActivityServiceServer) DeleteNote(context.Context, *DeleteNoteRequest) (*DeleteNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNote not implemented")
}
func (UnimplementedActivityServiceServer) CreateSource(context.Context, *CreateSourceRequest) (*CreateSourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSource not implemented")
}
func (UnimplementedActivityServiceServer) UpdateSources(context.Context, *UpdateSourcesRequest) (*UpdateSourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSources not implemented")
}
func (UnimplementedActivityServiceServer) ListSources(context.Context, *ListSourcesRequest) (*ListSourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSources not implemented")
}
func (UnimplementedActivityServiceServer) DeleteSource(context.Context, *DeleteSourceRequest) (*DeleteSourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSource not implemented")
}
func (UnimplementedActivityServiceServer) mustEmbedUnimplementedActivityServiceServer() {}
func (UnimplementedActivityServiceServer) testEmbeddedByValue()                         {}

// UnsafeActivityServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ActivityServiceServer will
// result in compilation errors.
type UnsafeActivityServiceServer interface {
	mustEmbedUnimplementedActivityServiceServer()
}

func RegisterActivityServiceServer(s grpc.ServiceRegistrar, srv ActivityServiceServer) {
	// If the following call pancis, it indicates UnimplementedActivityServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ActivityService_ServiceDesc, srv)
}

func _ActivityService_CreateCustomerActivityLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerActivityLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CreateCustomerActivityLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_CreateCustomerActivityLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CreateCustomerActivityLog(ctx, req.(*CreateCustomerActivityLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_UpdateCustomerActivityLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerActivityLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).UpdateCustomerActivityLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_UpdateCustomerActivityLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).UpdateCustomerActivityLog(ctx, req.(*UpdateCustomerActivityLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ListCustomerActivityLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerActivityLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ListCustomerActivityLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_ListCustomerActivityLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ListCustomerActivityLogs(ctx, req.(*ListCustomerActivityLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ConvertCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ConvertCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_ConvertCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ConvertCustomer(ctx, req.(*ConvertCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ConvertCustomersAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertCustomersAttributeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ConvertCustomersAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_ConvertCustomersAttribute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ConvertCustomersAttribute(ctx, req.(*ConvertCustomersAttributeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CreateCustomerTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CreateCustomerTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_CreateCustomerTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CreateCustomerTask(ctx, req.(*CreateCustomerTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_UpdateCustomerTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).UpdateCustomerTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_UpdateCustomerTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).UpdateCustomerTask(ctx, req.(*UpdateCustomerTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ListCustomerTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ListCustomerTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_ListCustomerTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ListCustomerTasks(ctx, req.(*ListCustomerTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_DeleteCustomerTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomerTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).DeleteCustomerTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_DeleteCustomerTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).DeleteCustomerTask(ctx, req.(*DeleteCustomerTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CreateLifeCycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLifeCycleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CreateLifeCycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_CreateLifeCycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CreateLifeCycle(ctx, req.(*CreateLifeCycleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_UpdateLifeCycles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLifeCyclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).UpdateLifeCycles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_UpdateLifeCycles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).UpdateLifeCycles(ctx, req.(*UpdateLifeCyclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ListLifeCycles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLifeCyclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ListLifeCycles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_ListLifeCycles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ListLifeCycles(ctx, req.(*ListLifeCyclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_DeleteLifeCycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLifeCycleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).DeleteLifeCycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_DeleteLifeCycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).DeleteLifeCycle(ctx, req.(*DeleteLifeCycleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CreateActionState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateActionStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CreateActionState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_CreateActionState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CreateActionState(ctx, req.(*CreateActionStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_UpdateActionStates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActionStatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).UpdateActionStates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_UpdateActionStates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).UpdateActionStates(ctx, req.(*UpdateActionStatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ListActionStates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListActionStatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ListActionStates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_ListActionStates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ListActionStates(ctx, req.(*ListActionStatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_DeleteActionState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteActionStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).DeleteActionState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_DeleteActionState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).DeleteActionState(ctx, req.(*DeleteActionStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CreateTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CreateTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_CreateTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CreateTag(ctx, req.(*CreateTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_UpdateTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTagsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).UpdateTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_UpdateTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).UpdateTags(ctx, req.(*UpdateTagsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ListTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTagsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ListTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_ListTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ListTags(ctx, req.(*ListTagsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_DeleteTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).DeleteTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_DeleteTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).DeleteTag(ctx, req.(*DeleteTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CoverCustomerTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoverCustomerTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CoverCustomerTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_CoverCustomerTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CoverCustomerTag(ctx, req.(*CoverCustomerTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_BatchAddCustomersTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddCustomersTagsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).BatchAddCustomersTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_BatchAddCustomersTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).BatchAddCustomersTags(ctx, req.(*BatchAddCustomersTagsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CreateNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CreateNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_CreateNote_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CreateNote(ctx, req.(*CreateNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_UpdateNotes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNotesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).UpdateNotes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_UpdateNotes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).UpdateNotes(ctx, req.(*UpdateNotesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ListNotes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNotesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ListNotes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_ListNotes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ListNotes(ctx, req.(*ListNotesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_DeleteNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).DeleteNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_DeleteNote_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).DeleteNote(ctx, req.(*DeleteNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_CreateSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).CreateSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_CreateSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).CreateSource(ctx, req.(*CreateSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_UpdateSources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).UpdateSources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_UpdateSources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).UpdateSources(ctx, req.(*UpdateSourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_ListSources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).ListSources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_ListSources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).ListSources(ctx, req.(*ListSourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityService_DeleteSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServiceServer).DeleteSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActivityService_DeleteSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServiceServer).DeleteSource(ctx, req.(*DeleteSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ActivityService_ServiceDesc is the grpc.ServiceDesc for ActivityService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ActivityService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.customer.v2.ActivityService",
	HandlerType: (*ActivityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCustomerActivityLog",
			Handler:    _ActivityService_CreateCustomerActivityLog_Handler,
		},
		{
			MethodName: "UpdateCustomerActivityLog",
			Handler:    _ActivityService_UpdateCustomerActivityLog_Handler,
		},
		{
			MethodName: "ListCustomerActivityLogs",
			Handler:    _ActivityService_ListCustomerActivityLogs_Handler,
		},
		{
			MethodName: "ConvertCustomer",
			Handler:    _ActivityService_ConvertCustomer_Handler,
		},
		{
			MethodName: "ConvertCustomersAttribute",
			Handler:    _ActivityService_ConvertCustomersAttribute_Handler,
		},
		{
			MethodName: "CreateCustomerTask",
			Handler:    _ActivityService_CreateCustomerTask_Handler,
		},
		{
			MethodName: "UpdateCustomerTask",
			Handler:    _ActivityService_UpdateCustomerTask_Handler,
		},
		{
			MethodName: "ListCustomerTasks",
			Handler:    _ActivityService_ListCustomerTasks_Handler,
		},
		{
			MethodName: "DeleteCustomerTask",
			Handler:    _ActivityService_DeleteCustomerTask_Handler,
		},
		{
			MethodName: "CreateLifeCycle",
			Handler:    _ActivityService_CreateLifeCycle_Handler,
		},
		{
			MethodName: "UpdateLifeCycles",
			Handler:    _ActivityService_UpdateLifeCycles_Handler,
		},
		{
			MethodName: "ListLifeCycles",
			Handler:    _ActivityService_ListLifeCycles_Handler,
		},
		{
			MethodName: "DeleteLifeCycle",
			Handler:    _ActivityService_DeleteLifeCycle_Handler,
		},
		{
			MethodName: "CreateActionState",
			Handler:    _ActivityService_CreateActionState_Handler,
		},
		{
			MethodName: "UpdateActionStates",
			Handler:    _ActivityService_UpdateActionStates_Handler,
		},
		{
			MethodName: "ListActionStates",
			Handler:    _ActivityService_ListActionStates_Handler,
		},
		{
			MethodName: "DeleteActionState",
			Handler:    _ActivityService_DeleteActionState_Handler,
		},
		{
			MethodName: "CreateTag",
			Handler:    _ActivityService_CreateTag_Handler,
		},
		{
			MethodName: "UpdateTags",
			Handler:    _ActivityService_UpdateTags_Handler,
		},
		{
			MethodName: "ListTags",
			Handler:    _ActivityService_ListTags_Handler,
		},
		{
			MethodName: "DeleteTag",
			Handler:    _ActivityService_DeleteTag_Handler,
		},
		{
			MethodName: "CoverCustomerTag",
			Handler:    _ActivityService_CoverCustomerTag_Handler,
		},
		{
			MethodName: "BatchAddCustomersTags",
			Handler:    _ActivityService_BatchAddCustomersTags_Handler,
		},
		{
			MethodName: "CreateNote",
			Handler:    _ActivityService_CreateNote_Handler,
		},
		{
			MethodName: "UpdateNotes",
			Handler:    _ActivityService_UpdateNotes_Handler,
		},
		{
			MethodName: "ListNotes",
			Handler:    _ActivityService_ListNotes_Handler,
		},
		{
			MethodName: "DeleteNote",
			Handler:    _ActivityService_DeleteNote_Handler,
		},
		{
			MethodName: "CreateSource",
			Handler:    _ActivityService_CreateSource_Handler,
		},
		{
			MethodName: "UpdateSources",
			Handler:    _ActivityService_UpdateSources_Handler,
		},
		{
			MethodName: "ListSources",
			Handler:    _ActivityService_ListSources_Handler,
		},
		{
			MethodName: "DeleteSource",
			Handler:    _ActivityService_DeleteSource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/customer/v2/activity_service.proto",
}
