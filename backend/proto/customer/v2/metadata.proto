syntax = "proto3";

package backend.proto.customer.v2;


import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "google/type/phone_number.proto";
import "google/type/postal_address.proto";
import "google/type/latlng.proto";
import "google/protobuf/struct.proto";
import "buf/validate/validate.proto";
import "backend/proto/customer/v2/common.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v2";


// customer
message Customer {
  // state enumeration
  enum State {
    // 0 is reserved for unspecified
    STATE_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // inactive
    INACTIVE = 2;
    // deleted
    DELETED = 3;
  }

  // 联系人ID
  int64 id = 1;
  
  // organization reference
  OrganizationRef organization = 2;
  
  // core identifier information
  // 名 (最大长度255字符)
  string given_name = 3 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 姓 (最大长度255字符)
  string family_name = 4 [(buf.validate.field) = { string: { max_len: 255 } }];
  // basic classification
  // 实体状态（活跃/非活跃/删除）
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
  // lifecycle
  int64 lifecycle_id = 12;
  
  // relationship
  // 负责人 id (staff id)
  int64 owner_staff_id = 13;
  
  // extended fields
  // 自定义字段
  google.protobuf.Struct custom_fields = 14;
  
  // audit information
  // 创建时间
  google.protobuf.Timestamp create_time = 15;
  // 更新时间
  google.protobuf.Timestamp update_time = 16;
  // 删除时间
  google.protobuf.Timestamp delete_time = 17;

  // additional fields
  // action state id
  int64 action_state_id = 18;
  // avatar path
  string avatar_path = 19 [(buf.validate.field) = { string: { max_len: 255 } }];
  // customer code
  string customer_code = 20 [(buf.validate.field) = { string: { max_len: 255 } }];
  // referral source id
  int64 referral_source_id = 21 [(buf.validate.field) = { int64: { gte: 0 } }];
}

// customer related data
message CustomerRelatedData {
  // state enumeration
  enum State {
    // 0 is reserved for unspecified
    STATE_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // inactive
    INACTIVE = 2;
    // deleted
    DELETED = 3;
  }

  // 客户相关数据ID
  int64 id = 1;
  // 关联的客户ID
  int64 customer_id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
  // 商家ID
  int32 business_id = 3;
  // 公司ID
  int64 company_id = 4;
  // 客户颜色
  string client_color = 5 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 是否屏蔽消息 (0-正常 1-屏蔽)
  int32 is_block_message = 6;
  // 是否屏蔽在线预约 (0-正常 1-屏蔽)
  int32 is_block_online_booking = 7;
  // 登录邮箱
  string login_email = 8 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 推荐来源ID
  int32 referral_source_id = 9;
  // 推荐来源描述
  string referral_source_desc = 10 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 发送自动邮件 (0-不接受 1-接受)
  int32 send_auto_email = 11;
  // 发送自动短信 (0-不接受 1-接受)
  int32 send_auto_message = 12;
  // 发送app自动短信 (0-不接受 1-接受)
  int32 send_app_auto_message = 13;
  // 未确认提醒方式 (1-text 2-email 3-phone call)
    // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: this is not a preposition --)
  int32 unconfirmed_reminder_by = 14;
  // 首选美容师ID
  int32 preferred_groomer_id = 15;
  // 首选频率天数
  int32 preferred_frequency_day = 16;
  // 首选频率类型 (0-by days 1-by weeks)
  int32 preferred_frequency_type = 17;
  // 最后服务时间
  // (-- api-linter: core::0142::time-field-type=disabled
  //     aip.dev/not-precedent: this is json string not a time --)
  string last_service_time = 18 [(buf.validate.field) = { string: { max_len: 20 } }];
  // 来源
  string source = 19 [(buf.validate.field) = { string: { max_len: 20 } }];
  // 外部ID
  string external_id = 20 [(buf.validate.field) = { string: { max_len: 20 } }];
  // 创建人
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: this is not a preposition --)
  int32 create_by = 21;
  // 更新人
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: this is not a preposition --)
  int32 update_by = 22;
  // 是否重复 (1-true 2-false)
  optional int32 is_recurring = 23;
  // 分享预约状态 (0-all 1-unconfirm 2-confirm 4-finished)
  int32 share_appt_status = 24;
  // 分享范围类型 (0-all 1-in x days 2-next x appointment 3-manually apptids)
  int32 share_range_type = 25;
  // 分享范围值
  int32 share_range_value = 26;
  // 分享预约JSON
  optional string share_appt_json = 27;
  // 首选天 (JSON数组格式)
  string preferred_day = 28 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 首选时间 (JSON数组格式)
  // (-- api-linter: core::0142::time-field-type=disabled
  //     aip.dev/not-precedent: this is json string not a time --)
  string preferred_time = 29 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 账户ID
  int64 account_id = 30;
  // 客户编码
  string customer_code = 31 [(buf.validate.field) = { string: { max_len: 8 } }];
  // 是否退订
  bool is_unsubscribed = 32;
  // 生日
  // (-- api-linter: core::0142::time-field-names=disabled
  //     aip.dev/not-precedent: this is not a time field --)
  optional google.protobuf.Timestamp birthday = 33;
  // 行动状态
  string action_state = 34 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 自定义生命周期ID
  int64 customize_life_cycle_id = 35;
  // 自定义行动状态ID
  int64 customize_action_state_id = 36;
  
  // state information
  State state = 40 [(google.api.field_behavior) = OUTPUT_ONLY];
  
  // audit information
  // 删除时间
  optional google.protobuf.Timestamp delete_time = 41;
  // 创建时间
  google.protobuf.Timestamp create_time = 42 [(google.api.field_behavior) = OUTPUT_ONLY];
  // 更新时间
  google.protobuf.Timestamp update_time = 43 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// lead
message Lead {
  // state enumeration
  enum State {
    // 0 is reserved for unspecified
    STATE_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // inactive
    INACTIVE = 2;
    // deleted
    DELETED = 3;
  }

  // lead ID
  int64 id = 1;

  // organization reference
  OrganizationRef organization = 2;

  // core identifier information
  // 名 (最大长度255字符)
  string given_name = 3 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 姓 (最大长度255字符)
  string family_name = 4 [(buf.validate.field) = { string: { max_len: 255 } }];

  // 实体状态（活跃/非活跃/删除）
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
  // lifecycle
  int64 lifecycle_id = 12;

  // relationship
  // 负责人 id (staff id)
  int64 owner_staff_id = 13;

  // extended fields
  // 自定义字段
  google.protobuf.Struct custom_fields = 14;

  // audit information
  // 创建时间
  google.protobuf.Timestamp create_time = 15;
  // 更新时间
  google.protobuf.Timestamp update_time = 16;
  // 删除时间
  google.protobuf.Timestamp delete_time = 17;
}


// contact 
message Contact {
  // state enumeration
  enum State {
    // 0 is reserved for unspecified
    STATE_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // inactive
    INACTIVE = 2;
    // deleted 
    DELETED = 3;
  }

  // 联系人ID
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // basic information
  // 名字
  string given_name = 3 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 姓
  string family_name = 4 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 邮箱地址 (必须是有效的邮箱格式)
  string email = 5 [(buf.validate.field) = { string: { max_len: 255, email: true } }];
  // 电话号码
  google.type.PhoneNumber phone = 6;
  // 是否是自己创建的
  bool is_self = 7;
  
  // state information
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // 关联的标签列表
  // 1.如果创建时没有指定, 就是默认空列表
  // 2. 如果创建时仅指定id, 会根据id查询标签列表, 并做关联
  // 3. 如果创建时传完整Tag, 会以传入的Tag创建新Tag, 并做关联
  // 注: 如果创建时传id和完整Tag, 会以id查询内容为准
  // -- 
  // 所以, 如果创建Contact时绑定已有标签, 这里应该只传id 
  // 如果创建Contact时还需要同时创建新标签, 这里应该传完整Tag
  repeated ContactTag tags = 11;

  // 备注
  string note = 12;
  // audit information
  // 创建时间
  google.protobuf.Timestamp create_time = 15;
  // 更新时间
  google.protobuf.Timestamp update_time = 16;
  // 删除时间
  google.protobuf.Timestamp delete_time = 17;
}

// contact tag
message ContactTag {
  // state enumeration
  enum State {
    // 0 is reserved for unspecified
    STATE_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // inactive
    INACTIVE = 2;
    // deleted
    DELETED = 3;
  }

    // type
    enum Type {
      // 0 is reserved for unspecified
      TYPE_UNSPECIFIED = 0;
      // custom
      CUSTOM = 1;
  
      // 以下都是内置标签
      // EMERGENCY
      EMERGENCY = 2;
      // PICKUP
      PICKUP = 3;
      // COMMUNITY
      COMMUNITY = 4;
      // PRIMARY
      PRIMARY = 5;
    }

  // 标签ID
  int64 id = 1;
  
  // organization reference
  OrganizationRef organization = 2;
  
  // 标签名称 (最大长度100字符)
  string name = 3 [(buf.validate.field) = { string: { max_len: 100 } }];
  
  // 标签描述 (最大长度500字符)
  string description = 4 [(buf.validate.field) = { string: { max_len: 500 } }];
  
  // 标签颜色 (十六进制颜色代码，如 #FF5733)
  string color = 5 [(buf.validate.field) = { string: { max_len: 7 } }];
  
  // 排序顺序
  int32 sort_order = 6;
  
  // 实体状态（活跃/非活跃/删除）
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
  // 标签类型
  Type type = 8; 
  
  // audit information
  // 创建时间
  google.protobuf.Timestamp create_time = 10;
  // 更新时间
  google.protobuf.Timestamp update_time = 11;
  // 删除时间
  google.protobuf.Timestamp delete_time = 12;
}

// address
message Address {
  // state enumeration
  enum State {
    // 0 is reserved for unspecified
    STATE_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // inactive
    INACTIVE = 2;
    // deleted
    DELETED = 3;
  }

  // address type
  enum Type {
    // 0 is reserved for unspecified
    TYPE_UNSPECIFIED = 0;
    // primary
    PRIMARY = 1;
    // additional
    ADDITIONAL = 2;
  }
  // 地址ID
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  
  // address information
  // 完整地址信息
  // 解释字段:
  // "postal_address": {
  // "revision": 1, // 版本号, 一般用不上
  // "regionCode": "US", // 国家代码, 必填
  // "languageCode": "en-US", // 语言代码
  // "postalCode": "94043", // 邮编
  // "administrativeArea": "CA", // 省
  // "locality": "Mountain View", // 城市
  // "addressLines": [ // 地址行
  //   "1600 Amphitheatre Parkway",
  //   "Building 43, Floor 2"
  // ],
  // "recipients": ["John Smith"], // 收件人
  // "organization": "Google LLC" // 组织
  // }
  google.type.PostalAddress address = 4;
  
  // geographic coordinates
  // 纬度
  // lat 纬度
  // lng 经度
  google.type.LatLng latlng = 5;

  // 地址类型
  Type type = 6;

  // state information
  State state = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // audit information
  // 创建时间
  google.protobuf.Timestamp create_time = 9;
  // 更新时间
  google.protobuf.Timestamp update_time = 10;
  // 删除时间
  google.protobuf.Timestamp delete_time = 11;
}

// custom field definition
message CustomField {
  // field state enumeration
  enum State {
    // 0 is reserved for unspecified
    STATE_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // deleted
    DELETED = 2;
  }

  // field type enumeration
  enum Type {
    // 0 is reserved for unspecified
    TYPE_UNSPECIFIED = 0;
    // text
    TEXT = 1;
    // number
    NUMBER = 2;
    // date
    DATE = 3;
    // boolean
    BOOLEAN = 4;
    // currency
    CURRENCY = 5;
    // select
    SELECT = 6;
    // multi select
    MULTI_SELECT = 7;
    // relation
    RELATION = 8;
    // money
    MONEY = 9;
  }

  // custom field association type
  enum AssociationType {
    // 0 is reserved for unspecified
    ASSOCIATION_TYPE_UNSPECIFIED = 0;
    // customer
    CUSTOMER = 1;
    // lead
    LEAD = 2;
  }

  // Custom field value, can be of any supported type.
  message Value {
    // Relation to another entity (customer/lead)
    message Relation {
      // 实体类型
      enum Entity {
        // 0 is reserved for unspecified
        ENTITY_UNSPECIFIED = 0;
        // customer
        CUSTOMER =  1;
        // lead
        LEAD = 2;
      }
      // 关联实体类型
      Entity entity = 1;
      // 关联实体ID
      int64 id = 2;
    }

    // 实际的值，oneof 只会有一个生效
    oneof value {
      // 字符串类型
      string string = 2;
      // 浮点数类型
      double double_value = 3;
      // 整型类型
      int64 int64 = 4;
      // 布尔类型
      bool bool = 6;
      // 金额类型
      google.type.Money money = 7;
      // 时间戳类型
      google.protobuf.Timestamp timestamp_time = 8;
      // 关联关系类型
      Relation relation = 9;
    }
  }

  // 字段选项
  message Option {
    // 字段值
    Value value = 1;
    // 选项标签
    string label = 2;
    // 排序顺序
    int32 sort_order = 3;
    // 实体状态（活跃/删除）
    State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // 自定义字段定义ID
  int64 id = 1;
  
  // field basic information
  // 主体
  OrganizationRef organization = 2;
  // 实体类型
  AssociationType association_type = 3  ;
  // 字段标识, 唯一标识的机器码, 由系统生成
  string code = 4;
  // 字段标签
  string label = 5;
  // 字段类型
  Type type = 6;
  
  // field configuration
  // 是否必填
  bool is_required = 10;
  // 实体状态（活跃/删除）
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
  // 默认值
  Value default_value = 12;
  
  // option configuration
  // 字段选项列表
  repeated Option options = 15;
  
  // validation rules
  // 验证规则
  google.protobuf.Struct validation_rules = 18;
  
  // display configuration
  // 显示顺序
  int32 display_order = 21;
  // 帮助文本
  string help_text = 22;
  
  // audit information
  // 创建时间
  google.protobuf.Timestamp create_time = 25;
  // 更新时间
  google.protobuf.Timestamp update_time = 26;
  // 删除时间
  google.protobuf.Timestamp delete_time = 27;
}

// customer type
enum CustomerType {
  // 0 is reserved for unspecified
  CUSTOMER_TYPE_UNSPECIFIED = 0;
  // lead
  LEAD = 1;
  // customer
  CUSTOMER = 2;
}
