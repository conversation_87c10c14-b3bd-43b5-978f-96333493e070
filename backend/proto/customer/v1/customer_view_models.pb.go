// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/customer/v1/customer_view_models.proto

package customerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 类型
type CustomerView_Type int32

const (
	// 未指定字段
	CustomerView_TYPE_UNSPECIFIED CustomerView_Type = 0
	// 客户
	CustomerView_CUSTOMER CustomerView_Type = 1
	// 潜客
	CustomerView_LEAD CustomerView_Type = 2
)

// Enum value maps for CustomerView_Type.
var (
	CustomerView_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CUSTOMER",
		2: "LEAD",
	}
	CustomerView_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"CUSTOMER":         1,
		"LEAD":             2,
	}
)

func (x CustomerView_Type) Enum() *CustomerView_Type {
	p := new(CustomerView_Type)
	*p = x
	return p
}

func (x CustomerView_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerView_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_view_models_proto_enumTypes[0].Descriptor()
}

func (CustomerView_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_view_models_proto_enumTypes[0]
}

func (x CustomerView_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerView_Type.Descriptor instead.
func (CustomerView_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_view_models_proto_rawDescGZIP(), []int{0, 0}
}

// 客户视图
type CustomerView struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 视图 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 公司 ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 员工 ID
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 是否为默认视图 0-否 1-是
	IsDefault int32 `protobuf:"varint,4,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	// 视图标题
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// 显示字段列表
	Fields []string `protobuf:"bytes,6,rep,name=fields,proto3" json:"fields,omitempty"`
	// 排序规则
	OrderBy *CustomerView_OrderBy `protobuf:"bytes,7,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// 筛选条件
	Filter *CustomerView_Filter `protobuf:"bytes,8,opt,name=filter,proto3" json:"filter,omitempty"`
	// 类型
	Type          CustomerView_Type `protobuf:"varint,9,opt,name=type,proto3,enum=backend.proto.customer.v1.CustomerView_Type" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerView) Reset() {
	*x = CustomerView{}
	mi := &file_backend_proto_customer_v1_customer_view_models_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerView) ProtoMessage() {}

func (x *CustomerView) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_view_models_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerView.ProtoReflect.Descriptor instead.
func (*CustomerView) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_view_models_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomerView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CustomerView) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CustomerView) GetIsDefault() int32 {
	if x != nil {
		return x.IsDefault
	}
	return 0
}

func (x *CustomerView) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CustomerView) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *CustomerView) GetOrderBy() *CustomerView_OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *CustomerView) GetFilter() *CustomerView_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *CustomerView) GetType() CustomerView_Type {
	if x != nil {
		return x.Type
	}
	return CustomerView_TYPE_UNSPECIFIED
}

// 排序规则
type CustomerView_OrderBy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Property string `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	// 排序方向
	Order         string `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerView_OrderBy) Reset() {
	*x = CustomerView_OrderBy{}
	mi := &file_backend_proto_customer_v1_customer_view_models_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerView_OrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerView_OrderBy) ProtoMessage() {}

func (x *CustomerView_OrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_view_models_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerView_OrderBy.ProtoReflect.Descriptor instead.
func (*CustomerView_OrderBy) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_view_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CustomerView_OrderBy) GetProperty() string {
	if x != nil {
		return x.Property
	}
	return ""
}

func (x *CustomerView_OrderBy) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

// 筛选条件
type CustomerView_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 筛选类型
	Type *string `protobuf:"bytes,1,opt,name=type,proto3,oneof" json:"type,omitempty"`
	// 子筛选条件
	Filters []*CustomerView_Filter `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
	// 操作符
	Operator *string `protobuf:"bytes,3,opt,name=operator,proto3,oneof" json:"operator,omitempty"`
	// 筛选字段
	Property *string `protobuf:"bytes,4,opt,name=property,proto3,oneof" json:"property,omitempty"`
	// 筛选值
	Value *string `protobuf:"bytes,5,opt,name=value,proto3,oneof" json:"value,omitempty"`
	// 多个值
	Values        []string `protobuf:"bytes,6,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerView_Filter) Reset() {
	*x = CustomerView_Filter{}
	mi := &file_backend_proto_customer_v1_customer_view_models_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerView_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerView_Filter) ProtoMessage() {}

func (x *CustomerView_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_view_models_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerView_Filter.ProtoReflect.Descriptor instead.
func (*CustomerView_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_view_models_proto_rawDescGZIP(), []int{0, 1}
}

func (x *CustomerView_Filter) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *CustomerView_Filter) GetFilters() []*CustomerView_Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *CustomerView_Filter) GetOperator() string {
	if x != nil && x.Operator != nil {
		return *x.Operator
	}
	return ""
}

func (x *CustomerView_Filter) GetProperty() string {
	if x != nil && x.Property != nil {
		return *x.Property
	}
	return ""
}

func (x *CustomerView_Filter) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *CustomerView_Filter) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_backend_proto_customer_v1_customer_view_models_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v1_customer_view_models_proto_rawDesc = "" +
	"\n" +
	"4backend/proto/customer/v1/customer_view_models.proto\x12\x19backend.proto.customer.v1\"\xfe\x05\n" +
	"\fCustomerView\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"is_default\x18\x04 \x01(\x05R\tisDefault\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12\x16\n" +
	"\x06fields\x18\x06 \x03(\tR\x06fields\x12J\n" +
	"\border_by\x18\a \x01(\v2/.backend.proto.customer.v1.CustomerView.OrderByR\aorderBy\x12F\n" +
	"\x06filter\x18\b \x01(\v2..backend.proto.customer.v1.CustomerView.FilterR\x06filter\x12@\n" +
	"\x04type\x18\t \x01(\x0e2,.backend.proto.customer.v1.CustomerView.TypeR\x04type\x1a;\n" +
	"\aOrderBy\x12\x1a\n" +
	"\bproperty\x18\x01 \x01(\tR\bproperty\x12\x14\n" +
	"\x05order\x18\x02 \x01(\tR\x05order\x1a\x8d\x02\n" +
	"\x06Filter\x12\x17\n" +
	"\x04type\x18\x01 \x01(\tH\x00R\x04type\x88\x01\x01\x12H\n" +
	"\afilters\x18\x02 \x03(\v2..backend.proto.customer.v1.CustomerView.FilterR\afilters\x12\x1f\n" +
	"\boperator\x18\x03 \x01(\tH\x01R\boperator\x88\x01\x01\x12\x1f\n" +
	"\bproperty\x18\x04 \x01(\tH\x02R\bproperty\x88\x01\x01\x12\x19\n" +
	"\x05value\x18\x05 \x01(\tH\x03R\x05value\x88\x01\x01\x12\x16\n" +
	"\x06values\x18\x06 \x03(\tR\x06valuesB\a\n" +
	"\x05_typeB\v\n" +
	"\t_operatorB\v\n" +
	"\t_propertyB\b\n" +
	"\x06_value\"4\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bCUSTOMER\x10\x01\x12\b\n" +
	"\x04LEAD\x10\x02Bk\n" +
	"#com.moego.backend.proto.customer.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v1;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v1_customer_view_models_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v1_customer_view_models_proto_rawDescData []byte
)

func file_backend_proto_customer_v1_customer_view_models_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v1_customer_view_models_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v1_customer_view_models_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v1_customer_view_models_proto_rawDesc), len(file_backend_proto_customer_v1_customer_view_models_proto_rawDesc)))
	})
	return file_backend_proto_customer_v1_customer_view_models_proto_rawDescData
}

var file_backend_proto_customer_v1_customer_view_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_customer_v1_customer_view_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_backend_proto_customer_v1_customer_view_models_proto_goTypes = []any{
	(CustomerView_Type)(0),       // 0: backend.proto.customer.v1.CustomerView.Type
	(*CustomerView)(nil),         // 1: backend.proto.customer.v1.CustomerView
	(*CustomerView_OrderBy)(nil), // 2: backend.proto.customer.v1.CustomerView.OrderBy
	(*CustomerView_Filter)(nil),  // 3: backend.proto.customer.v1.CustomerView.Filter
}
var file_backend_proto_customer_v1_customer_view_models_proto_depIdxs = []int32{
	2, // 0: backend.proto.customer.v1.CustomerView.order_by:type_name -> backend.proto.customer.v1.CustomerView.OrderBy
	3, // 1: backend.proto.customer.v1.CustomerView.filter:type_name -> backend.proto.customer.v1.CustomerView.Filter
	0, // 2: backend.proto.customer.v1.CustomerView.type:type_name -> backend.proto.customer.v1.CustomerView.Type
	3, // 3: backend.proto.customer.v1.CustomerView.Filter.filters:type_name -> backend.proto.customer.v1.CustomerView.Filter
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v1_customer_view_models_proto_init() }
func file_backend_proto_customer_v1_customer_view_models_proto_init() {
	if File_backend_proto_customer_v1_customer_view_models_proto != nil {
		return
	}
	file_backend_proto_customer_v1_customer_view_models_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v1_customer_view_models_proto_rawDesc), len(file_backend_proto_customer_v1_customer_view_models_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_customer_v1_customer_view_models_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v1_customer_view_models_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v1_customer_view_models_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v1_customer_view_models_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v1_customer_view_models_proto = out.File
	file_backend_proto_customer_v1_customer_view_models_proto_goTypes = nil
	file_backend_proto_customer_v1_customer_view_models_proto_depIdxs = nil
}
