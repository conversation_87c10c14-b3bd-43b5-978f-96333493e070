// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/customer/v1/customer.proto

package customerpb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LifeCycle 表示客户生命周期状态
type Customer_LifeCycle int32

const (
	// 未指定的生命周期状态
	Customer_LIFE_CYCLE_UNSPECIFIED Customer_LifeCycle = 0
	// 潜在客户
	Customer_LIFE_CYCLE_LEAD Customer_LifeCycle = 1
	// 商机
	Customer_LIFE_CYCLE_OPPORTUNITY Customer_LifeCycle = 2
	// 垃圾线索
	Customer_LIFE_CYCLE_TRASH Customer_LifeCycle = 3
	// 休眠客户
	Customer_LIFE_CYCLE_DORMANT_LEAD Customer_LifeCycle = 4
)

// Enum value maps for Customer_LifeCycle.
var (
	Customer_LifeCycle_name = map[int32]string{
		0: "LIFE_CYCLE_UNSPECIFIED",
		1: "LIFE_CYCLE_LEAD",
		2: "LIFE_CYCLE_OPPORTUNITY",
		3: "LIFE_CYCLE_TRASH",
		4: "LIFE_CYCLE_DORMANT_LEAD",
	}
	Customer_LifeCycle_value = map[string]int32{
		"LIFE_CYCLE_UNSPECIFIED":  0,
		"LIFE_CYCLE_LEAD":         1,
		"LIFE_CYCLE_OPPORTUNITY":  2,
		"LIFE_CYCLE_TRASH":        3,
		"LIFE_CYCLE_DORMANT_LEAD": 4,
	}
)

func (x Customer_LifeCycle) Enum() *Customer_LifeCycle {
	p := new(Customer_LifeCycle)
	*p = x
	return p
}

func (x Customer_LifeCycle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Customer_LifeCycle) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[0].Descriptor()
}

func (Customer_LifeCycle) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[0]
}

func (x Customer_LifeCycle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Customer_LifeCycle.Descriptor instead.
func (Customer_LifeCycle) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{0, 0}
}

// ActionState 表示客户行动状态
type Customer_ActionState int32

const (
	// 未指定的行动状态
	Customer_ACTION_STATE_UNSPECIFIED Customer_ActionState = 0
	// 已符合资格
	Customer_LEAD_STATE Customer_ActionState = 1
	// 已完成TOUR
	Customer_TOUR_COMPLETED Customer_ActionState = 2
	// 客户服务代表跟进
	Customer_CSR_FOLLOWUP Customer_ActionState = 3
	// 首次会议
	Customer_FIRST_RESERVATION Customer_ActionState = 4
	// 会面问候
	Customer_MEET_AND_GREET Customer_ActionState = 5
	// 客户未出现
	Customer_CLIENT_NO_SHOW Customer_ActionState = 6
	// 新的符合资格
	Customer_QUALIFIED_NEW Customer_ActionState = 7
	// 不符合资格
	Customer_UNQUALIFIED Customer_ActionState = 8
	// 尝试联系
	Customer_ATTEMPTED_TO_CONTACT Customer_ActionState = 9
	// 已建立联系
	Customer_CONNECTED Customer_ActionState = 10
	// 时机不合适
	Customer_BAD_TIMING Customer_ActionState = 11
	// 进行中
	Customer_IN_PROGRESS Customer_ActionState = 12
	// for k9
	// Lead Send email #1
	Customer_NEW_LEADS_TARGET_EMAIL_1 Customer_ActionState = 50
	// Lead Send email #2
	Customer_NEW_LEADS_TARGET_EMAIL_2 Customer_ActionState = 51
	// Lead Send email #3
	Customer_NEW_LEADS_TARGET_EMAIL_3 Customer_ActionState = 52
	// Lead Send email #4
	Customer_NEW_LEADS_TARGET_EMAIL_4 Customer_ActionState = 53
	// Opportunity Send email #1
	Customer_OPPORTUNITY_TARGET_EMAIL_1 Customer_ActionState = 54
	// Opportunity Send email #2
	Customer_OPPORTUNITY_TARGET_EMAIL_2 Customer_ActionState = 55
	// Opportunity Send email #3
	Customer_OPPORTUNITY_TARGET_EMAIL_3 Customer_ActionState = 56
)

// Enum value maps for Customer_ActionState.
var (
	Customer_ActionState_name = map[int32]string{
		0:  "ACTION_STATE_UNSPECIFIED",
		1:  "LEAD_STATE",
		2:  "TOUR_COMPLETED",
		3:  "CSR_FOLLOWUP",
		4:  "FIRST_RESERVATION",
		5:  "MEET_AND_GREET",
		6:  "CLIENT_NO_SHOW",
		7:  "QUALIFIED_NEW",
		8:  "UNQUALIFIED",
		9:  "ATTEMPTED_TO_CONTACT",
		10: "CONNECTED",
		11: "BAD_TIMING",
		12: "IN_PROGRESS",
		50: "NEW_LEADS_TARGET_EMAIL_1",
		51: "NEW_LEADS_TARGET_EMAIL_2",
		52: "NEW_LEADS_TARGET_EMAIL_3",
		53: "NEW_LEADS_TARGET_EMAIL_4",
		54: "OPPORTUNITY_TARGET_EMAIL_1",
		55: "OPPORTUNITY_TARGET_EMAIL_2",
		56: "OPPORTUNITY_TARGET_EMAIL_3",
	}
	Customer_ActionState_value = map[string]int32{
		"ACTION_STATE_UNSPECIFIED":   0,
		"LEAD_STATE":                 1,
		"TOUR_COMPLETED":             2,
		"CSR_FOLLOWUP":               3,
		"FIRST_RESERVATION":          4,
		"MEET_AND_GREET":             5,
		"CLIENT_NO_SHOW":             6,
		"QUALIFIED_NEW":              7,
		"UNQUALIFIED":                8,
		"ATTEMPTED_TO_CONTACT":       9,
		"CONNECTED":                  10,
		"BAD_TIMING":                 11,
		"IN_PROGRESS":                12,
		"NEW_LEADS_TARGET_EMAIL_1":   50,
		"NEW_LEADS_TARGET_EMAIL_2":   51,
		"NEW_LEADS_TARGET_EMAIL_3":   52,
		"NEW_LEADS_TARGET_EMAIL_4":   53,
		"OPPORTUNITY_TARGET_EMAIL_1": 54,
		"OPPORTUNITY_TARGET_EMAIL_2": 55,
		"OPPORTUNITY_TARGET_EMAIL_3": 56,
	}
)

func (x Customer_ActionState) Enum() *Customer_ActionState {
	p := new(Customer_ActionState)
	*p = x
	return p
}

func (x Customer_ActionState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Customer_ActionState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[1].Descriptor()
}

func (Customer_ActionState) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[1]
}

func (x Customer_ActionState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Customer_ActionState.Descriptor instead.
func (Customer_ActionState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{0, 1}
}

// state 表示客户状态
type Customer_State int32

const (
	// 未指定的状态
	Customer_STATE_UNSPECIFIED Customer_State = 0
	// 正常状态
	Customer_NORMAL Customer_State = 1
	// 已删除
	Customer_DELETED Customer_State = 2
)

// Enum value maps for Customer_State.
var (
	Customer_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "NORMAL",
		2: "DELETED",
	}
	Customer_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"NORMAL":            1,
		"DELETED":           2,
	}
)

func (x Customer_State) Enum() *Customer_State {
	p := new(Customer_State)
	*p = x
	return p
}

func (x Customer_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Customer_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[2].Descriptor()
}

func (Customer_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[2]
}

func (x Customer_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Customer_State.Descriptor instead.
func (Customer_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{0, 2}
}

// Type 表示客户类型
type Customer_Type int32

const (
	// 未指定的状态
	Customer_TYPE_UNSPECIFIED Customer_Type = 0
	// 默认类型
	Customer_CUSTOMER Customer_Type = 1
	// 潜客
	Customer_LEAD Customer_Type = 2
)

// Enum value maps for Customer_Type.
var (
	Customer_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CUSTOMER",
		2: "LEAD",
	}
	Customer_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"CUSTOMER":         1,
		"LEAD":             2,
	}
)

func (x Customer_Type) Enum() *Customer_Type {
	p := new(Customer_Type)
	*p = x
	return p
}

func (x Customer_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Customer_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[3].Descriptor()
}

func (Customer_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[3]
}

func (x Customer_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Customer_Type.Descriptor instead.
func (Customer_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{0, 3}
}

// State 表示地址状态
type Address_State int32

const (
	// 未指定的状态
	Address_STATE_UNSPECIFIED Address_State = 0
	// 启用
	Address_NORMAL Address_State = 1
	// 删除
	Address_DELETED Address_State = 2
)

// Enum value maps for Address_State.
var (
	Address_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "NORMAL",
		2: "DELETED",
	}
	Address_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"NORMAL":            1,
		"DELETED":           2,
	}
)

func (x Address_State) Enum() *Address_State {
	p := new(Address_State)
	*p = x
	return p
}

func (x Address_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Address_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[4].Descriptor()
}

func (Address_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[4]
}

func (x Address_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Address_State.Descriptor instead.
func (Address_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{1, 0}
}

// IsPrimary 表示是否为主地址
type Address_IsPrimary int32

const (
	// 未指定的类型
	Address_IS_PRIMARY_UNSPECIFIED Address_IsPrimary = 0
	// 主要地址
	Address_PRIMARY Address_IsPrimary = 1
	// 附加地址
	Address_NO_PRIMARY Address_IsPrimary = 2
)

// Enum value maps for Address_IsPrimary.
var (
	Address_IsPrimary_name = map[int32]string{
		0: "IS_PRIMARY_UNSPECIFIED",
		1: "PRIMARY",
		2: "NO_PRIMARY",
	}
	Address_IsPrimary_value = map[string]int32{
		"IS_PRIMARY_UNSPECIFIED": 0,
		"PRIMARY":                1,
		"NO_PRIMARY":             2,
	}
)

func (x Address_IsPrimary) Enum() *Address_IsPrimary {
	p := new(Address_IsPrimary)
	*p = x
	return p
}

func (x Address_IsPrimary) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Address_IsPrimary) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[5].Descriptor()
}

func (Address_IsPrimary) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[5]
}

func (x Address_IsPrimary) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Address_IsPrimary.Descriptor instead.
func (Address_IsPrimary) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{1, 1}
}

// Status 表示联系人状态
type CustomerContact_State int32

const (
	// 未指定的状态
	CustomerContact_STATE_UNSPECIFIED CustomerContact_State = 0
	// 启用
	CustomerContact_NORMAL CustomerContact_State = 1
	// 删除
	CustomerContact_DELETED CustomerContact_State = 2
)

// Enum value maps for CustomerContact_State.
var (
	CustomerContact_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "NORMAL",
		2: "DELETED",
	}
	CustomerContact_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"NORMAL":            1,
		"DELETED":           2,
	}
)

func (x CustomerContact_State) Enum() *CustomerContact_State {
	p := new(CustomerContact_State)
	*p = x
	return p
}

func (x CustomerContact_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerContact_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[6].Descriptor()
}

func (CustomerContact_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[6]
}

func (x CustomerContact_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerContact_State.Descriptor instead.
func (CustomerContact_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{2, 0}
}

// 联系人类型
type CustomerContact_Type int32

const (
	// 未指定的类型
	CustomerContact_TYPE_UNSPECIFIED CustomerContact_Type = 0
	// 主要联系人
	CustomerContact_MAIN CustomerContact_Type = 1
	// 附加联系人
	CustomerContact_ADDITIONAL CustomerContact_Type = 2
)

// Enum value maps for CustomerContact_Type.
var (
	CustomerContact_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "MAIN",
		2: "ADDITIONAL",
	}
	CustomerContact_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"MAIN":             1,
		"ADDITIONAL":       2,
	}
)

func (x CustomerContact_Type) Enum() *CustomerContact_Type {
	p := new(CustomerContact_Type)
	*p = x
	return p
}

func (x CustomerContact_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerContact_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[7].Descriptor()
}

func (CustomerContact_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[7]
}

func (x CustomerContact_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerContact_Type.Descriptor instead.
func (CustomerContact_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{2, 1}
}

// IsPrimary 表示是否为主联系人
type CustomerContact_IsPrimary int32

const (
	// 未指定的类型
	CustomerContact_IS_PRIMARY_UNSPECIFIED CustomerContact_IsPrimary = 0
	// 主要联系人
	CustomerContact_PRIMARY CustomerContact_IsPrimary = 1
	// 附加联系人
	CustomerContact_NO_PRIMARY CustomerContact_IsPrimary = 2
)

// Enum value maps for CustomerContact_IsPrimary.
var (
	CustomerContact_IsPrimary_name = map[int32]string{
		0: "IS_PRIMARY_UNSPECIFIED",
		1: "PRIMARY",
		2: "NO_PRIMARY",
	}
	CustomerContact_IsPrimary_value = map[string]int32{
		"IS_PRIMARY_UNSPECIFIED": 0,
		"PRIMARY":                1,
		"NO_PRIMARY":             2,
	}
)

func (x CustomerContact_IsPrimary) Enum() *CustomerContact_IsPrimary {
	p := new(CustomerContact_IsPrimary)
	*p = x
	return p
}

func (x CustomerContact_IsPrimary) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerContact_IsPrimary) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[8].Descriptor()
}

func (CustomerContact_IsPrimary) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[8]
}

func (x CustomerContact_IsPrimary) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerContact_IsPrimary.Descriptor instead.
func (CustomerContact_IsPrimary) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{2, 2}
}

// State 表示任务状态
type Task_State int32

const (
	// 未指定的状态
	Task_STATE_UNSPECIFIED Task_State = 0
	// 新任务
	Task_NEW Task_State = 1
	// 已完成
	Task_FINISH Task_State = 2
)

// Enum value maps for Task_State.
var (
	Task_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "NEW",
		2: "FINISH",
	}
	Task_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"NEW":               1,
		"FINISH":            2,
	}
)

func (x Task_State) Enum() *Task_State {
	p := new(Task_State)
	*p = x
	return p
}

func (x Task_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Task_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[9].Descriptor()
}

func (Task_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[9]
}

func (x Task_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Task_State.Descriptor instead.
func (Task_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{3, 0}
}

// Type 表示历史记录类型
type HistoryLog_Type int32

const (
	// 未指定的记录类型
	HistoryLog_TYPE_UNSPECIFIED HistoryLog_Type = 0
	// 短信记录
	HistoryLog_MESSAGE HistoryLog_Type = 1
	// 通话记录
	HistoryLog_CALL HistoryLog_Type = 2
	// 备注记录
	HistoryLog_NOTE HistoryLog_Type = 3
	// 任务记录
	HistoryLog_TASK HistoryLog_Type = 4
	// 类型转化的记录
	HistoryLog_CONVERT HistoryLog_Type = 5
	// 用户创建记录
	HistoryLog_CREATE HistoryLog_Type = 6
)

// Enum value maps for HistoryLog_Type.
var (
	HistoryLog_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "MESSAGE",
		2: "CALL",
		3: "NOTE",
		4: "TASK",
		5: "CONVERT",
		6: "CREATE",
	}
	HistoryLog_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"MESSAGE":          1,
		"CALL":             2,
		"NOTE":             3,
		"TASK":             4,
		"CONVERT":          5,
		"CREATE":           6,
	}
)

func (x HistoryLog_Type) Enum() *HistoryLog_Type {
	p := new(HistoryLog_Type)
	*p = x
	return p
}

func (x HistoryLog_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HistoryLog_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[10].Descriptor()
}

func (HistoryLog_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[10]
}

func (x HistoryLog_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HistoryLog_Type.Descriptor instead.
func (HistoryLog_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 0}
}

// Source 表示历史记录来源
type HistoryLog_Source int32

const (
	// 未指定的记录类型
	HistoryLog_SOURCE_UNSPECIFIED HistoryLog_Source = 0
	// 员工操作
	HistoryLog_STAFF HistoryLog_Source = 1
	// 预约
	HistoryLog_APPOINTMENT HistoryLog_Source = 2
	// OB
	HistoryLog_ONLINE_BOOKING HistoryLog_Source = 3
	// product(retail)
	HistoryLog_PRODUCT HistoryLog_Source = 4
	// package
	HistoryLog_PACKAGE HistoryLog_Source = 5
	// fulfillment(new appointment)
	HistoryLog_FULFILLMENT HistoryLog_Source = 6
	// membership
	HistoryLog_MEMBERSHIP HistoryLog_Source = 7
)

// Enum value maps for HistoryLog_Source.
var (
	HistoryLog_Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "STAFF",
		2: "APPOINTMENT",
		3: "ONLINE_BOOKING",
		4: "PRODUCT",
		5: "PACKAGE",
		6: "FULFILLMENT",
		7: "MEMBERSHIP",
	}
	HistoryLog_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"STAFF":              1,
		"APPOINTMENT":        2,
		"ONLINE_BOOKING":     3,
		"PRODUCT":            4,
		"PACKAGE":            5,
		"FULFILLMENT":        6,
		"MEMBERSHIP":         7,
	}
)

func (x HistoryLog_Source) Enum() *HistoryLog_Source {
	p := new(HistoryLog_Source)
	*p = x
	return p
}

func (x HistoryLog_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HistoryLog_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[11].Descriptor()
}

func (HistoryLog_Source) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[11]
}

func (x HistoryLog_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HistoryLog_Source.Descriptor instead.
func (HistoryLog_Source) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 1}
}

// State 表示短信状态
type HistoryLog_Message_State int32

const (
	// 未指定的状态
	HistoryLog_Message_STATE_UNSPECIFIED HistoryLog_Message_State = 0
	// 发送成功
	HistoryLog_Message_SUCCEEDED HistoryLog_Message_State = 1
	// 发送失败
	HistoryLog_Message_FAILED HistoryLog_Message_State = 2
)

// Enum value maps for HistoryLog_Message_State.
var (
	HistoryLog_Message_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "SUCCEEDED",
		2: "FAILED",
	}
	HistoryLog_Message_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"SUCCEEDED":         1,
		"FAILED":            2,
	}
)

func (x HistoryLog_Message_State) Enum() *HistoryLog_Message_State {
	p := new(HistoryLog_Message_State)
	*p = x
	return p
}

func (x HistoryLog_Message_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HistoryLog_Message_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[12].Descriptor()
}

func (HistoryLog_Message_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[12]
}

func (x HistoryLog_Message_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HistoryLog_Message_State.Descriptor instead.
func (HistoryLog_Message_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 0, 0}
}

// Direction 表示收发方向
type HistoryLog_Message_Direction int32

const (
	// 未指定的状态
	HistoryLog_Message_DIRECTION_UNSPECIFIED HistoryLog_Message_Direction = 0
	// 发送
	HistoryLog_Message_SEND HistoryLog_Message_Direction = 1
	// 接收
	HistoryLog_Message_RECEIVE HistoryLog_Message_Direction = 2
)

// Enum value maps for HistoryLog_Message_Direction.
var (
	HistoryLog_Message_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "SEND",
		2: "RECEIVE",
	}
	HistoryLog_Message_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"SEND":                  1,
		"RECEIVE":               2,
	}
)

func (x HistoryLog_Message_Direction) Enum() *HistoryLog_Message_Direction {
	p := new(HistoryLog_Message_Direction)
	*p = x
	return p
}

func (x HistoryLog_Message_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HistoryLog_Message_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[13].Descriptor()
}

func (HistoryLog_Message_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[13]
}

func (x HistoryLog_Message_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HistoryLog_Message_Direction.Descriptor instead.
func (HistoryLog_Message_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 0, 1}
}

// State 表示通话状态
type HistoryLog_Call_State int32

const (
	// 未指定的状态
	HistoryLog_Call_STATE_UNSPECIFIED HistoryLog_Call_State = 0
	// 接通
	HistoryLog_Call_ANSWERED HistoryLog_Call_State = 1
	// 未接
	HistoryLog_Call_NO_ANSWER HistoryLog_Call_State = 2
)

// Enum value maps for HistoryLog_Call_State.
var (
	HistoryLog_Call_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ANSWERED",
		2: "NO_ANSWER",
	}
	HistoryLog_Call_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ANSWERED":          1,
		"NO_ANSWER":         2,
	}
)

func (x HistoryLog_Call_State) Enum() *HistoryLog_Call_State {
	p := new(HistoryLog_Call_State)
	*p = x
	return p
}

func (x HistoryLog_Call_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HistoryLog_Call_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[14].Descriptor()
}

func (HistoryLog_Call_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[14]
}

func (x HistoryLog_Call_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HistoryLog_Call_State.Descriptor instead.
func (HistoryLog_Call_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 1, 0}
}

// Direction 表示收发方向
type HistoryLog_Call_Direction int32

const (
	// 未指定的状态
	HistoryLog_Call_DIRECTION_UNSPECIFIED HistoryLog_Call_Direction = 0
	// 用户联系商家
	HistoryLog_Call_INCOMING HistoryLog_Call_Direction = 1
	// 商家联系用户
	HistoryLog_Call_OUTGOING HistoryLog_Call_Direction = 2
)

// Enum value maps for HistoryLog_Call_Direction.
var (
	HistoryLog_Call_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "INCOMING",
		2: "OUTGOING",
	}
	HistoryLog_Call_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"INCOMING":              1,
		"OUTGOING":              2,
	}
)

func (x HistoryLog_Call_Direction) Enum() *HistoryLog_Call_Direction {
	p := new(HistoryLog_Call_Direction)
	*p = x
	return p
}

func (x HistoryLog_Call_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HistoryLog_Call_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[15].Descriptor()
}

func (HistoryLog_Call_Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[15]
}

func (x HistoryLog_Call_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HistoryLog_Call_Direction.Descriptor instead.
func (HistoryLog_Call_Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 1, 1}
}

// Type 表示任务历史记录类型
type HistoryLog_Task_Type int32

const (
	// 未指定的记录类型
	HistoryLog_Task_TYPE_UNSPECIFIED HistoryLog_Task_Type = 0
	// 创建任务
	HistoryLog_Task_CREATE HistoryLog_Task_Type = 1
	// 更新任务
	HistoryLog_Task_UPDATE HistoryLog_Task_Type = 2
	// 完成任务
	HistoryLog_Task_FINISH HistoryLog_Task_Type = 3
	// 删除任务
	HistoryLog_Task_DELETE HistoryLog_Task_Type = 4
)

// Enum value maps for HistoryLog_Task_Type.
var (
	HistoryLog_Task_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CREATE",
		2: "UPDATE",
		3: "FINISH",
		4: "DELETE",
	}
	HistoryLog_Task_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"CREATE":           1,
		"UPDATE":           2,
		"FINISH":           3,
		"DELETE":           4,
	}
)

func (x HistoryLog_Task_Type) Enum() *HistoryLog_Task_Type {
	p := new(HistoryLog_Task_Type)
	*p = x
	return p
}

func (x HistoryLog_Task_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HistoryLog_Task_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_proto_enumTypes[16].Descriptor()
}

func (HistoryLog_Task_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_proto_enumTypes[16]
}

func (x HistoryLog_Task_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HistoryLog_Task_Type.Descriptor instead.
func (HistoryLog_Task_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 3, 0}
}

// customer info
type Customer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 首选 business ID (客户属于哪一个business)
	PreferredBusinessId int64 `protobuf:"varint,3,opt,name=preferred_business_id,json=preferredBusinessId,proto3" json:"preferred_business_id,omitempty"`
	// 账户ID
	AccountId int64 `protobuf:"varint,4,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// 电话号码
	PhoneNumber string `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// 客户编码
	CustomerCode string `protobuf:"bytes,6,opt,name=customer_code,json=customerCode,proto3" json:"customer_code,omitempty"`
	// 生命周期状态
	LifeCycle Customer_LifeCycle `protobuf:"varint,7,opt,name=life_cycle,json=lifeCycle,proto3,enum=backend.proto.customer.v1.Customer_LifeCycle" json:"life_cycle,omitempty"`
	// 行动状态
	ActionState Customer_ActionState `protobuf:"varint,8,opt,name=action_state,json=actionState,proto3,enum=backend.proto.customer.v1.Customer_ActionState" json:"action_state,omitempty"`
	// 客户来源
	Source string `protobuf:"bytes,9,opt,name=source,proto3" json:"source,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 头像路径
	AvatarPath string `protobuf:"bytes,12,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// 名
	GivenName string `protobuf:"bytes,13,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓
	FamilyName string `protobuf:"bytes,14,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 邮箱
	Email string `protobuf:"bytes,15,opt,name=email,proto3" json:"email,omitempty"`
	// 生日
	BirthTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=birth_time,json=birthTime,proto3" json:"birth_time,omitempty"`
	// 用户类型
	Type Customer_Type `protobuf:"varint,17,opt,name=type,proto3,enum=backend.proto.customer.v1.Customer_Type" json:"type,omitempty"`
	// 地址
	Address *Address `protobuf:"bytes,18,opt,name=address,proto3" json:"address,omitempty"`
	// 联系人
	Contact *CustomerContact `protobuf:"bytes,19,opt,name=contact,proto3" json:"contact,omitempty"`
	// 关联的员工ID
	AllocateStaffId int64 `protobuf:"varint,20,opt,name=allocate_staff_id,json=allocateStaffId,proto3" json:"allocate_staff_id,omitempty"`
	// OB additional info
	AdditionalInfo *Customer_AdditionalInfo `protobuf:"bytes,21,opt,name=additional_info,json=additionalInfo,proto3" json:"additional_info,omitempty"`
	// state
	State Customer_State `protobuf:"varint,22,opt,name=state,proto3,enum=backend.proto.customer.v1.Customer_State" json:"state,omitempty"`
	// 关联自定义生命周期ID
	CustomizeLifeCycleId int64 `protobuf:"varint,23,opt,name=customize_life_cycle_id,json=customizeLifeCycleId,proto3" json:"customize_life_cycle_id,omitempty"`
	// 关联自定义行动状态ID
	CustomizeActionStateId int64 `protobuf:"varint,24,opt,name=customize_action_state_id,json=customizeActionStateId,proto3" json:"customize_action_state_id,omitempty"`
	// 用户头像颜色
	ClientColor   string `protobuf:"bytes,25,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Customer) Reset() {
	*x = Customer{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer) ProtoMessage() {}

func (x *Customer) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer.ProtoReflect.Descriptor instead.
func (*Customer) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *Customer) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Customer) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *Customer) GetPreferredBusinessId() int64 {
	if x != nil {
		return x.PreferredBusinessId
	}
	return 0
}

func (x *Customer) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *Customer) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *Customer) GetCustomerCode() string {
	if x != nil {
		return x.CustomerCode
	}
	return ""
}

func (x *Customer) GetLifeCycle() Customer_LifeCycle {
	if x != nil {
		return x.LifeCycle
	}
	return Customer_LIFE_CYCLE_UNSPECIFIED
}

func (x *Customer) GetActionState() Customer_ActionState {
	if x != nil {
		return x.ActionState
	}
	return Customer_ACTION_STATE_UNSPECIFIED
}

func (x *Customer) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Customer) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Customer) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Customer) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *Customer) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *Customer) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Customer) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Customer) GetBirthTime() *timestamppb.Timestamp {
	if x != nil {
		return x.BirthTime
	}
	return nil
}

func (x *Customer) GetType() Customer_Type {
	if x != nil {
		return x.Type
	}
	return Customer_TYPE_UNSPECIFIED
}

func (x *Customer) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *Customer) GetContact() *CustomerContact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *Customer) GetAllocateStaffId() int64 {
	if x != nil {
		return x.AllocateStaffId
	}
	return 0
}

func (x *Customer) GetAdditionalInfo() *Customer_AdditionalInfo {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

func (x *Customer) GetState() Customer_State {
	if x != nil {
		return x.State
	}
	return Customer_STATE_UNSPECIFIED
}

func (x *Customer) GetCustomizeLifeCycleId() int64 {
	if x != nil {
		return x.CustomizeLifeCycleId
	}
	return 0
}

func (x *Customer) GetCustomizeActionStateId() int64 {
	if x != nil {
		return x.CustomizeActionStateId
	}
	return 0
}

func (x *Customer) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

// Address 表示地址信息
type Address struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 地址1
	Address1 string `protobuf:"bytes,4,opt,name=address1,proto3" json:"address1,omitempty"`
	// 地址2
	Address2 string `protobuf:"bytes,5,opt,name=address2,proto3" json:"address2,omitempty"`
	// 城市
	City string `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`
	// 州/省
	State string `protobuf:"bytes,7,opt,name=state,proto3" json:"state,omitempty"`
	// 区域代码 (替代 country)
	RegionCode string `protobuf:"bytes,8,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
	// 邮编
	Zipcode string `protobuf:"bytes,9,opt,name=zipcode,proto3" json:"zipcode,omitempty"`
	// 纬度
	Lat string `protobuf:"bytes,10,opt,name=lat,proto3" json:"lat,omitempty"`
	// 经度
	Lng string `protobuf:"bytes,11,opt,name=lng,proto3" json:"lng,omitempty"`
	// 状态
	Status Address_State `protobuf:"varint,12,opt,name=status,proto3,enum=backend.proto.customer.v1.Address_State" json:"status,omitempty"`
	// 是否为主地址
	// 注意, 这里的枚举和数据库不一致
	// 数据库中未重构前是0和1, 目前用枚举表示, service 需要转换枚举和db中的数据
	IsPrimary Address_IsPrimary `protobuf:"varint,13,opt,name=is_primary,json=isPrimary,proto3,enum=backend.proto.customer.v1.Address_IsPrimary" json:"is_primary,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Address) Reset() {
	*x = Address{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *Address) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Address) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *Address) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Address) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *Address) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *Address) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *Address) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Address) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

func (x *Address) GetZipcode() string {
	if x != nil {
		return x.Zipcode
	}
	return ""
}

func (x *Address) GetLat() string {
	if x != nil {
		return x.Lat
	}
	return ""
}

func (x *Address) GetLng() string {
	if x != nil {
		return x.Lng
	}
	return ""
}

func (x *Address) GetStatus() Address_State {
	if x != nil {
		return x.Status
	}
	return Address_STATE_UNSPECIFIED
}

func (x *Address) GetIsPrimary() Address_IsPrimary {
	if x != nil {
		return x.IsPrimary
	}
	return Address_IS_PRIMARY_UNSPECIFIED
}

func (x *Address) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Address) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// contact 表示联系信息
type CustomerContact struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 商户ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 名
	GivenName string `protobuf:"bytes,4,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓
	FamilyName string `protobuf:"bytes,5,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 电话号码
	PhoneNumber string `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// 邮箱
	Email string `protobuf:"bytes,7,opt,name=email,proto3" json:"email,omitempty"`
	// 职位
	Title string `protobuf:"bytes,8,opt,name=title,proto3" json:"title,omitempty"`
	// 类型 1 主要联系人 2 附加联系人
	Type CustomerContact_Type `protobuf:"varint,9,opt,name=type,proto3,enum=backend.proto.customer.v1.CustomerContact_Type" json:"type,omitempty"`
	// is_primary 表示是否为主联系人
	// 注意, 这里的枚举和数据库不一致
	// 数据库中未重构前是0和1, 目前用枚举表示, service 需要转换枚举和db中的数据
	IsPrimary CustomerContact_IsPrimary `protobuf:"varint,10,opt,name=is_primary,json=isPrimary,proto3,enum=backend.proto.customer.v1.CustomerContact_IsPrimary" json:"is_primary,omitempty"`
	// 状态 1 启用 2 删除
	State CustomerContact_State `protobuf:"varint,11,opt,name=state,proto3,enum=backend.proto.customer.v1.CustomerContact_State" json:"state,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,14,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// E164格式电话号码
	E164PhoneNumber string `protobuf:"bytes,15,opt,name=e164_phone_number,json=e164PhoneNumber,proto3" json:"e164_phone_number,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CustomerContact) Reset() {
	*x = CustomerContact{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerContact) ProtoMessage() {}

func (x *CustomerContact) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerContact.ProtoReflect.Descriptor instead.
func (*CustomerContact) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerContact) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomerContact) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CustomerContact) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CustomerContact) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *CustomerContact) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *CustomerContact) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CustomerContact) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerContact) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CustomerContact) GetType() CustomerContact_Type {
	if x != nil {
		return x.Type
	}
	return CustomerContact_TYPE_UNSPECIFIED
}

func (x *CustomerContact) GetIsPrimary() CustomerContact_IsPrimary {
	if x != nil {
		return x.IsPrimary
	}
	return CustomerContact_IS_PRIMARY_UNSPECIFIED
}

func (x *CustomerContact) GetState() CustomerContact_State {
	if x != nil {
		return x.State
	}
	return CustomerContact_STATE_UNSPECIFIED
}

func (x *CustomerContact) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CustomerContact) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *CustomerContact) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CustomerContact) GetE164PhoneNumber() string {
	if x != nil {
		return x.E164PhoneNumber
	}
	return ""
}

// Task 表示任务信息
type Task struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 任务ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 任务名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 分配员工
	AllocateStaffId *int64 `protobuf:"varint,3,opt,name=allocate_staff_id,json=allocateStaffId,proto3,oneof" json:"allocate_staff_id,omitempty"`
	// 预期完成的时间
	CompleteTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=complete_time,json=completeTime,proto3,oneof" json:"complete_time,omitempty"`
	// 任务状态
	State         Task_State `protobuf:"varint,5,opt,name=state,proto3,enum=backend.proto.customer.v1.Task_State" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Task) Reset() {
	*x = Task{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *Task) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Task) GetAllocateStaffId() int64 {
	if x != nil && x.AllocateStaffId != nil {
		return *x.AllocateStaffId
	}
	return 0
}

func (x *Task) GetCompleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompleteTime
	}
	return nil
}

func (x *Task) GetState() Task_State {
	if x != nil {
		return x.State
	}
	return Task_STATE_UNSPECIFIED
}

// HistoryLog 表示历史记录
type HistoryLog struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 客户名称
	CustomerName string `protobuf:"bytes,10,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// 客户电话
	CustomerPhoneNumber string `protobuf:"bytes,11,opt,name=customer_phone_number,json=customerPhoneNumber,proto3" json:"customer_phone_number,omitempty"`
	// 记录类型
	Type HistoryLog_Type `protobuf:"varint,3,opt,name=type,proto3,enum=backend.proto.customer.v1.HistoryLog_Type" json:"type,omitempty"`
	// 记录数据
	Action *HistoryLog_Action `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
	// 记录操作人
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 记录来源
	Source HistoryLog_Source `protobuf:"varint,7,opt,name=source,proto3,enum=backend.proto.customer.v1.HistoryLog_Source" json:"source,omitempty"`
	// 记录来源ID
	SourceId int64 `protobuf:"varint,8,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// 记录来源名称
	SourceName    string `protobuf:"bytes,9,opt,name=source_name,json=sourceName,proto3" json:"source_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryLog) Reset() {
	*x = HistoryLog{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryLog) ProtoMessage() {}

func (x *HistoryLog) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryLog.ProtoReflect.Descriptor instead.
func (*HistoryLog) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *HistoryLog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HistoryLog) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *HistoryLog) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *HistoryLog) GetCustomerPhoneNumber() string {
	if x != nil {
		return x.CustomerPhoneNumber
	}
	return ""
}

func (x *HistoryLog) GetType() HistoryLog_Type {
	if x != nil {
		return x.Type
	}
	return HistoryLog_TYPE_UNSPECIFIED
}

func (x *HistoryLog) GetAction() *HistoryLog_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *HistoryLog) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *HistoryLog) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *HistoryLog) GetSource() HistoryLog_Source {
	if x != nil {
		return x.Source
	}
	return HistoryLog_SOURCE_UNSPECIFIED
}

func (x *HistoryLog) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *HistoryLog) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

// 客户自定义生命周期状态
type CustomizeLifeCycle struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	// 是否默认 0-否 1-是
	IsDefault     int32 `protobuf:"varint,4,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomizeLifeCycle) Reset() {
	*x = CustomizeLifeCycle{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomizeLifeCycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizeLifeCycle) ProtoMessage() {}

func (x *CustomizeLifeCycle) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizeLifeCycle.ProtoReflect.Descriptor instead.
func (*CustomizeLifeCycle) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomizeLifeCycle) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomizeLifeCycle) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomizeLifeCycle) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *CustomizeLifeCycle) GetIsDefault() int32 {
	if x != nil {
		return x.IsDefault
	}
	return 0
}

// 客户自定义行动状态
type CustomizeActionState struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	// 颜色
	Color         string `protobuf:"bytes,4,opt,name=color,proto3" json:"color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomizeActionState) Reset() {
	*x = CustomizeActionState{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomizeActionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizeActionState) ProtoMessage() {}

func (x *CustomizeActionState) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizeActionState.ProtoReflect.Descriptor instead.
func (*CustomizeActionState) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{6}
}

func (x *CustomizeActionState) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomizeActionState) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomizeActionState) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *CustomizeActionState) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// OB additional info
type Customer_AdditionalInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Referral source
	ReferralSourceId int64 `protobuf:"varint,1,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	// Referral source desc
	ReferralSourceDesc string `protobuf:"bytes,2,opt,name=referral_source_desc,json=referralSourceDesc,proto3" json:"referral_source_desc,omitempty"`
	// Preferred groomer
	PreferredGroomerId int64 `protobuf:"varint,3,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3" json:"preferred_groomer_id,omitempty"`
	// Preferred frequency
	PreferredFrequencyDay int64 `protobuf:"varint,4,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3" json:"preferred_frequency_day,omitempty"`
	// Preferred frequency type (0-by days, 1-by weeks)
	PreferredFrequencyType int64 `protobuf:"varint,5,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3" json:"preferred_frequency_type,omitempty"`
	// Preferred days of the week
	PreferredDay []int64 `protobuf:"varint,6,rep,packed,name=preferred_day,json=preferredDay,proto3" json:"preferred_day,omitempty"`
	// Preferred times of the day
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: @jett ob 的垃圾设计使我不得不兼容这里的int64 --)
	PreferredTime []int64 `protobuf:"varint,7,rep,packed,name=preferred_time,json=preferredTime,proto3" json:"preferred_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Customer_AdditionalInfo) Reset() {
	*x = Customer_AdditionalInfo{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Customer_AdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer_AdditionalInfo) ProtoMessage() {}

func (x *Customer_AdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer_AdditionalInfo.ProtoReflect.Descriptor instead.
func (*Customer_AdditionalInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Customer_AdditionalInfo) GetReferralSourceId() int64 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

func (x *Customer_AdditionalInfo) GetReferralSourceDesc() string {
	if x != nil {
		return x.ReferralSourceDesc
	}
	return ""
}

func (x *Customer_AdditionalInfo) GetPreferredGroomerId() int64 {
	if x != nil {
		return x.PreferredGroomerId
	}
	return 0
}

func (x *Customer_AdditionalInfo) GetPreferredFrequencyDay() int64 {
	if x != nil {
		return x.PreferredFrequencyDay
	}
	return 0
}

func (x *Customer_AdditionalInfo) GetPreferredFrequencyType() int64 {
	if x != nil {
		return x.PreferredFrequencyType
	}
	return 0
}

func (x *Customer_AdditionalInfo) GetPreferredDay() []int64 {
	if x != nil {
		return x.PreferredDay
	}
	return nil
}

func (x *Customer_AdditionalInfo) GetPreferredTime() []int64 {
	if x != nil {
		return x.PreferredTime
	}
	return nil
}

// SMS 表示短信历史记录
type HistoryLog_Message struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 短信ID
	MessageId int64 `protobuf:"varint,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// 短信内容
	Text string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	// 发送状态
	State HistoryLog_Message_State `protobuf:"varint,3,opt,name=state,proto3,enum=backend.proto.customer.v1.HistoryLog_Message_State" json:"state,omitempty"`
	// 失败原因
	FailReason string `protobuf:"bytes,4,opt,name=fail_reason,json=failReason,proto3" json:"fail_reason,omitempty"`
	// 收发方向
	Direction     HistoryLog_Message_Direction `protobuf:"varint,5,opt,name=direction,proto3,enum=backend.proto.customer.v1.HistoryLog_Message_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryLog_Message) Reset() {
	*x = HistoryLog_Message{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryLog_Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryLog_Message) ProtoMessage() {}

func (x *HistoryLog_Message) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryLog_Message.ProtoReflect.Descriptor instead.
func (*HistoryLog_Message) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 0}
}

func (x *HistoryLog_Message) GetMessageId() int64 {
	if x != nil {
		return x.MessageId
	}
	return 0
}

func (x *HistoryLog_Message) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *HistoryLog_Message) GetState() HistoryLog_Message_State {
	if x != nil {
		return x.State
	}
	return HistoryLog_Message_STATE_UNSPECIFIED
}

func (x *HistoryLog_Message) GetFailReason() string {
	if x != nil {
		return x.FailReason
	}
	return ""
}

func (x *HistoryLog_Message) GetDirection() HistoryLog_Message_Direction {
	if x != nil {
		return x.Direction
	}
	return HistoryLog_Message_DIRECTION_UNSPECIFIED
}

// Call 表示通话历史记录
type HistoryLog_Call struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 通话ID
	CallId int64 `protobuf:"varint,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	// 通话记录
	Text string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	// 通话状态
	State HistoryLog_Call_State `protobuf:"varint,3,opt,name=state,proto3,enum=backend.proto.customer.v1.HistoryLog_Call_State" json:"state,omitempty"`
	// 失败原因
	FailReason string `protobuf:"bytes,4,opt,name=fail_reason,json=failReason,proto3" json:"fail_reason,omitempty"`
	// 收发方向
	Direction     HistoryLog_Call_Direction `protobuf:"varint,5,opt,name=direction,proto3,enum=backend.proto.customer.v1.HistoryLog_Call_Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryLog_Call) Reset() {
	*x = HistoryLog_Call{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryLog_Call) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryLog_Call) ProtoMessage() {}

func (x *HistoryLog_Call) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryLog_Call.ProtoReflect.Descriptor instead.
func (*HistoryLog_Call) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 1}
}

func (x *HistoryLog_Call) GetCallId() int64 {
	if x != nil {
		return x.CallId
	}
	return 0
}

func (x *HistoryLog_Call) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *HistoryLog_Call) GetState() HistoryLog_Call_State {
	if x != nil {
		return x.State
	}
	return HistoryLog_Call_STATE_UNSPECIFIED
}

func (x *HistoryLog_Call) GetFailReason() string {
	if x != nil {
		return x.FailReason
	}
	return ""
}

func (x *HistoryLog_Call) GetDirection() HistoryLog_Call_Direction {
	if x != nil {
		return x.Direction
	}
	return HistoryLog_Call_DIRECTION_UNSPECIFIED
}

// Note 表示备注历史记录
type HistoryLog_Note struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 备注内容
	Text          string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryLog_Note) Reset() {
	*x = HistoryLog_Note{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryLog_Note) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryLog_Note) ProtoMessage() {}

func (x *HistoryLog_Note) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryLog_Note.ProtoReflect.Descriptor instead.
func (*HistoryLog_Note) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 2}
}

func (x *HistoryLog_Note) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

// Task 表示任务历史记录
type HistoryLog_Task struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录类型
	Type HistoryLog_Task_Type `protobuf:"varint,1,opt,name=type,proto3,enum=backend.proto.customer.v1.HistoryLog_Task_Type" json:"type,omitempty"`
	// 任务信息
	Task          *Task `protobuf:"bytes,2,opt,name=task,proto3" json:"task,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryLog_Task) Reset() {
	*x = HistoryLog_Task{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryLog_Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryLog_Task) ProtoMessage() {}

func (x *HistoryLog_Task) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryLog_Task.ProtoReflect.Descriptor instead.
func (*HistoryLog_Task) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 3}
}

func (x *HistoryLog_Task) GetType() HistoryLog_Task_Type {
	if x != nil {
		return x.Type
	}
	return HistoryLog_Task_TYPE_UNSPECIFIED
}

func (x *HistoryLog_Task) GetTask() *Task {
	if x != nil {
		return x.Task
	}
	return nil
}

// Convert 表示类型转化记录
type HistoryLog_Convert struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 转化前类型
	OriginType Customer_Type `protobuf:"varint,1,opt,name=origin_type,json=originType,proto3,enum=backend.proto.customer.v1.Customer_Type" json:"origin_type,omitempty"`
	// 转化后的类型
	TargetType    Customer_Type `protobuf:"varint,2,opt,name=target_type,json=targetType,proto3,enum=backend.proto.customer.v1.Customer_Type" json:"target_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryLog_Convert) Reset() {
	*x = HistoryLog_Convert{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryLog_Convert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryLog_Convert) ProtoMessage() {}

func (x *HistoryLog_Convert) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryLog_Convert.ProtoReflect.Descriptor instead.
func (*HistoryLog_Convert) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 4}
}

func (x *HistoryLog_Convert) GetOriginType() Customer_Type {
	if x != nil {
		return x.OriginType
	}
	return Customer_TYPE_UNSPECIFIED
}

func (x *HistoryLog_Convert) GetTargetType() Customer_Type {
	if x != nil {
		return x.TargetType
	}
	return Customer_TYPE_UNSPECIFIED
}

// Create 表示用户创建记录
type HistoryLog_Create struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryLog_Create) Reset() {
	*x = HistoryLog_Create{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryLog_Create) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryLog_Create) ProtoMessage() {}

func (x *HistoryLog_Create) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryLog_Create.ProtoReflect.Descriptor instead.
func (*HistoryLog_Create) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 5}
}

// actions
type HistoryLog_Action struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Action:
	//
	//	*HistoryLog_Action_Message
	//	*HistoryLog_Action_Call
	//	*HistoryLog_Action_Note
	//	*HistoryLog_Action_Task
	//	*HistoryLog_Action_Convert
	//	*HistoryLog_Action_Create
	Action        isHistoryLog_Action_Action `protobuf_oneof:"action"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryLog_Action) Reset() {
	*x = HistoryLog_Action{}
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryLog_Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryLog_Action) ProtoMessage() {}

func (x *HistoryLog_Action) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryLog_Action.ProtoReflect.Descriptor instead.
func (*HistoryLog_Action) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_proto_rawDescGZIP(), []int{4, 6}
}

func (x *HistoryLog_Action) GetAction() isHistoryLog_Action_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *HistoryLog_Action) GetMessage() *HistoryLog_Message {
	if x != nil {
		if x, ok := x.Action.(*HistoryLog_Action_Message); ok {
			return x.Message
		}
	}
	return nil
}

func (x *HistoryLog_Action) GetCall() *HistoryLog_Call {
	if x != nil {
		if x, ok := x.Action.(*HistoryLog_Action_Call); ok {
			return x.Call
		}
	}
	return nil
}

func (x *HistoryLog_Action) GetNote() *HistoryLog_Note {
	if x != nil {
		if x, ok := x.Action.(*HistoryLog_Action_Note); ok {
			return x.Note
		}
	}
	return nil
}

func (x *HistoryLog_Action) GetTask() *HistoryLog_Task {
	if x != nil {
		if x, ok := x.Action.(*HistoryLog_Action_Task); ok {
			return x.Task
		}
	}
	return nil
}

func (x *HistoryLog_Action) GetConvert() *HistoryLog_Convert {
	if x != nil {
		if x, ok := x.Action.(*HistoryLog_Action_Convert); ok {
			return x.Convert
		}
	}
	return nil
}

func (x *HistoryLog_Action) GetCreate() *HistoryLog_Create {
	if x != nil {
		if x, ok := x.Action.(*HistoryLog_Action_Create); ok {
			return x.Create
		}
	}
	return nil
}

type isHistoryLog_Action_Action interface {
	isHistoryLog_Action_Action()
}

type HistoryLog_Action_Message struct {
	// 短信消息
	Message *HistoryLog_Message `protobuf:"bytes,1,opt,name=message,proto3,oneof"`
}

type HistoryLog_Action_Call struct {
	// 通话记录
	Call *HistoryLog_Call `protobuf:"bytes,2,opt,name=call,proto3,oneof"`
}

type HistoryLog_Action_Note struct {
	// 备注记录
	Note *HistoryLog_Note `protobuf:"bytes,3,opt,name=note,proto3,oneof"`
}

type HistoryLog_Action_Task struct {
	// 任务记录
	Task *HistoryLog_Task `protobuf:"bytes,4,opt,name=task,proto3,oneof"`
}

type HistoryLog_Action_Convert struct {
	// 转化记录
	Convert *HistoryLog_Convert `protobuf:"bytes,5,opt,name=convert,proto3,oneof"`
}

type HistoryLog_Action_Create struct {
	// 创建记录
	Create *HistoryLog_Create `protobuf:"bytes,6,opt,name=create,proto3,oneof"`
}

func (*HistoryLog_Action_Message) isHistoryLog_Action_Action() {}

func (*HistoryLog_Action_Call) isHistoryLog_Action_Action() {}

func (*HistoryLog_Action_Note) isHistoryLog_Action_Action() {}

func (*HistoryLog_Action_Task) isHistoryLog_Action_Action() {}

func (*HistoryLog_Action_Convert) isHistoryLog_Action_Action() {}

func (*HistoryLog_Action_Create) isHistoryLog_Action_Action() {}

var File_backend_proto_customer_v1_customer_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v1_customer_proto_rawDesc = "" +
	"\n" +
	"(backend/proto/customer/v1/customer.proto\x12\x19backend.proto.customer.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb3\x12\n" +
	"\bCustomer\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x122\n" +
	"\x15preferred_business_id\x18\x03 \x01(\x03R\x13preferredBusinessId\x12\x1d\n" +
	"\n" +
	"account_id\x18\x04 \x01(\x03R\taccountId\x12!\n" +
	"\fphone_number\x18\x05 \x01(\tR\vphoneNumber\x12#\n" +
	"\rcustomer_code\x18\x06 \x01(\tR\fcustomerCode\x12Q\n" +
	"\n" +
	"life_cycle\x18\a \x01(\x0e2-.backend.proto.customer.v1.Customer.LifeCycleB\x03\xe0A\x03R\tlifeCycle\x12W\n" +
	"\faction_state\x18\b \x01(\x0e2/.backend.proto.customer.v1.Customer.ActionStateB\x03\xe0A\x03R\vactionState\x12\x16\n" +
	"\x06source\x18\t \x01(\tR\x06source\x12;\n" +
	"\vcreate_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1f\n" +
	"\vavatar_path\x18\f \x01(\tR\n" +
	"avatarPath\x12\x1d\n" +
	"\n" +
	"given_name\x18\r \x01(\tR\tgivenName\x12\x1f\n" +
	"\vfamily_name\x18\x0e \x01(\tR\n" +
	"familyName\x12\x14\n" +
	"\x05email\x18\x0f \x01(\tR\x05email\x129\n" +
	"\n" +
	"birth_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\tbirthTime\x12<\n" +
	"\x04type\x18\x11 \x01(\x0e2(.backend.proto.customer.v1.Customer.TypeR\x04type\x12<\n" +
	"\aaddress\x18\x12 \x01(\v2\".backend.proto.customer.v1.AddressR\aaddress\x12D\n" +
	"\acontact\x18\x13 \x01(\v2*.backend.proto.customer.v1.CustomerContactR\acontact\x12*\n" +
	"\x11allocate_staff_id\x18\x14 \x01(\x03R\x0fallocateStaffId\x12[\n" +
	"\x0fadditional_info\x18\x15 \x01(\v22.backend.proto.customer.v1.Customer.AdditionalInfoR\x0eadditionalInfo\x12D\n" +
	"\x05state\x18\x16 \x01(\x0e2).backend.proto.customer.v1.Customer.StateB\x03\xe0A\x03R\x05state\x125\n" +
	"\x17customize_life_cycle_id\x18\x17 \x01(\x03R\x14customizeLifeCycleId\x129\n" +
	"\x19customize_action_state_id\x18\x18 \x01(\x03R\x16customizeActionStateId\x12!\n" +
	"\fclient_color\x18\x19 \x01(\tR\vclientColor\x1a\xe0\x02\n" +
	"\x0eAdditionalInfo\x12,\n" +
	"\x12referral_source_id\x18\x01 \x01(\x03R\x10referralSourceId\x120\n" +
	"\x14referral_source_desc\x18\x02 \x01(\tR\x12referralSourceDesc\x120\n" +
	"\x14preferred_groomer_id\x18\x03 \x01(\x03R\x12preferredGroomerId\x126\n" +
	"\x17preferred_frequency_day\x18\x04 \x01(\x03R\x15preferredFrequencyDay\x128\n" +
	"\x18preferred_frequency_type\x18\x05 \x01(\x03R\x16preferredFrequencyType\x12#\n" +
	"\rpreferred_day\x18\x06 \x03(\x03R\fpreferredDay\x12%\n" +
	"\x0epreferred_time\x18\a \x03(\x03R\rpreferredTime\"\x8b\x01\n" +
	"\tLifeCycle\x12\x1a\n" +
	"\x16LIFE_CYCLE_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fLIFE_CYCLE_LEAD\x10\x01\x12\x1a\n" +
	"\x16LIFE_CYCLE_OPPORTUNITY\x10\x02\x12\x14\n" +
	"\x10LIFE_CYCLE_TRASH\x10\x03\x12\x1b\n" +
	"\x17LIFE_CYCLE_DORMANT_LEAD\x10\x04\"\xe6\x03\n" +
	"\vActionState\x12\x1c\n" +
	"\x18ACTION_STATE_UNSPECIFIED\x10\x00\x12\x0e\n" +
	"\n" +
	"LEAD_STATE\x10\x01\x12\x12\n" +
	"\x0eTOUR_COMPLETED\x10\x02\x12\x10\n" +
	"\fCSR_FOLLOWUP\x10\x03\x12\x15\n" +
	"\x11FIRST_RESERVATION\x10\x04\x12\x12\n" +
	"\x0eMEET_AND_GREET\x10\x05\x12\x12\n" +
	"\x0eCLIENT_NO_SHOW\x10\x06\x12\x11\n" +
	"\rQUALIFIED_NEW\x10\a\x12\x0f\n" +
	"\vUNQUALIFIED\x10\b\x12\x18\n" +
	"\x14ATTEMPTED_TO_CONTACT\x10\t\x12\r\n" +
	"\tCONNECTED\x10\n" +
	"\x12\x0e\n" +
	"\n" +
	"BAD_TIMING\x10\v\x12\x0f\n" +
	"\vIN_PROGRESS\x10\f\x12\x1c\n" +
	"\x18NEW_LEADS_TARGET_EMAIL_1\x102\x12\x1c\n" +
	"\x18NEW_LEADS_TARGET_EMAIL_2\x103\x12\x1c\n" +
	"\x18NEW_LEADS_TARGET_EMAIL_3\x104\x12\x1c\n" +
	"\x18NEW_LEADS_TARGET_EMAIL_4\x105\x12\x1e\n" +
	"\x1aOPPORTUNITY_TARGET_EMAIL_1\x106\x12\x1e\n" +
	"\x1aOPPORTUNITY_TARGET_EMAIL_2\x107\x12\x1e\n" +
	"\x1aOPPORTUNITY_TARGET_EMAIL_3\x108\"7\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06NORMAL\x10\x01\x12\v\n" +
	"\aDELETED\x10\x02\"4\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bCUSTOMER\x10\x01\x12\b\n" +
	"\x04LEAD\x10\x02\"\xa7\x05\n" +
	"\aAddress\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vcustomer_id\x18\x03 \x01(\x03R\n" +
	"customerId\x12\x1a\n" +
	"\baddress1\x18\x04 \x01(\tR\baddress1\x12\x1a\n" +
	"\baddress2\x18\x05 \x01(\tR\baddress2\x12\x12\n" +
	"\x04city\x18\x06 \x01(\tR\x04city\x12\x14\n" +
	"\x05state\x18\a \x01(\tR\x05state\x12\x1f\n" +
	"\vregion_code\x18\b \x01(\tR\n" +
	"regionCode\x12\x18\n" +
	"\azipcode\x18\t \x01(\tR\azipcode\x12\x10\n" +
	"\x03lat\x18\n" +
	" \x01(\tR\x03lat\x12\x10\n" +
	"\x03lng\x18\v \x01(\tR\x03lng\x12E\n" +
	"\x06status\x18\f \x01(\x0e2(.backend.proto.customer.v1.Address.StateB\x03\xe0A\x03R\x06status\x12K\n" +
	"\n" +
	"is_primary\x18\r \x01(\x0e2,.backend.proto.customer.v1.Address.IsPrimaryR\tisPrimary\x12;\n" +
	"\vcreate_time\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\"7\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06NORMAL\x10\x01\x12\v\n" +
	"\aDELETED\x10\x02\"D\n" +
	"\tIsPrimary\x12\x1a\n" +
	"\x16IS_PRIMARY_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aPRIMARY\x10\x01\x12\x0e\n" +
	"\n" +
	"NO_PRIMARY\x10\x02\"\xda\x06\n" +
	"\x0fCustomerContact\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x1f\n" +
	"\vcustomer_id\x18\x03 \x01(\x03R\n" +
	"customerId\x12\x1d\n" +
	"\n" +
	"given_name\x18\x04 \x01(\tR\tgivenName\x12\x1f\n" +
	"\vfamily_name\x18\x05 \x01(\tR\n" +
	"familyName\x12!\n" +
	"\fphone_number\x18\x06 \x01(\tR\vphoneNumber\x12\x14\n" +
	"\x05email\x18\a \x01(\tR\x05email\x12\x14\n" +
	"\x05title\x18\b \x01(\tR\x05title\x12H\n" +
	"\x04type\x18\t \x01(\x0e2/.backend.proto.customer.v1.CustomerContact.TypeB\x03\xe0A\x03R\x04type\x12S\n" +
	"\n" +
	"is_primary\x18\n" +
	" \x01(\x0e24.backend.proto.customer.v1.CustomerContact.IsPrimaryR\tisPrimary\x12K\n" +
	"\x05state\x18\v \x01(\x0e20.backend.proto.customer.v1.CustomerContact.StateB\x03\xe0A\x03R\x05state\x12;\n" +
	"\vcreate_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1d\n" +
	"\n" +
	"company_id\x18\x0e \x01(\x03R\tcompanyId\x12*\n" +
	"\x11e164_phone_number\x18\x0f \x01(\tR\x0fe164PhoneNumber\"7\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06NORMAL\x10\x01\x12\v\n" +
	"\aDELETED\x10\x02\"6\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04MAIN\x10\x01\x12\x0e\n" +
	"\n" +
	"ADDITIONAL\x10\x02\"D\n" +
	"\tIsPrimary\x12\x1a\n" +
	"\x16IS_PRIMARY_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aPRIMARY\x10\x01\x12\x0e\n" +
	"\n" +
	"NO_PRIMARY\x10\x02\"\xc0\x02\n" +
	"\x04Task\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12/\n" +
	"\x11allocate_staff_id\x18\x03 \x01(\x03H\x00R\x0fallocateStaffId\x88\x01\x01\x12D\n" +
	"\rcomplete_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\fcompleteTime\x88\x01\x01\x12@\n" +
	"\x05state\x18\x05 \x01(\x0e2%.backend.proto.customer.v1.Task.StateB\x03\xe0A\x03R\x05state\"3\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03NEW\x10\x01\x12\n" +
	"\n" +
	"\x06FINISH\x10\x02B\x14\n" +
	"\x12_allocate_staff_idB\x10\n" +
	"\x0e_complete_time\"\xb4\x12\n" +
	"\n" +
	"HistoryLog\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12#\n" +
	"\rcustomer_name\x18\n" +
	" \x01(\tR\fcustomerName\x122\n" +
	"\x15customer_phone_number\x18\v \x01(\tR\x13customerPhoneNumber\x12>\n" +
	"\x04type\x18\x03 \x01(\x0e2*.backend.proto.customer.v1.HistoryLog.TypeR\x04type\x12D\n" +
	"\x06action\x18\x04 \x01(\v2,.backend.proto.customer.v1.HistoryLog.ActionR\x06action\x12\x19\n" +
	"\bstaff_id\x18\x05 \x01(\x03R\astaffId\x12;\n" +
	"\vcreate_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12D\n" +
	"\x06source\x18\a \x01(\x0e2,.backend.proto.customer.v1.HistoryLog.SourceR\x06source\x12\x1b\n" +
	"\tsource_id\x18\b \x01(\x03R\bsourceId\x12\x1f\n" +
	"\vsource_name\x18\t \x01(\tR\n" +
	"sourceName\x1a\xfe\x02\n" +
	"\aMessage\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\x03R\tmessageId\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12N\n" +
	"\x05state\x18\x03 \x01(\x0e23.backend.proto.customer.v1.HistoryLog.Message.StateB\x03\xe0A\x03R\x05state\x12\x1f\n" +
	"\vfail_reason\x18\x04 \x01(\tR\n" +
	"failReason\x12U\n" +
	"\tdirection\x18\x05 \x01(\x0e27.backend.proto.customer.v1.HistoryLog.Message.DirectionR\tdirection\"9\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tSUCCEEDED\x10\x01\x12\n" +
	"\n" +
	"\x06FAILED\x10\x02\"=\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04SEND\x10\x01\x12\v\n" +
	"\aRECEIVE\x10\x02\x1a\xf6\x02\n" +
	"\x04Call\x12\x17\n" +
	"\acall_id\x18\x01 \x01(\x03R\x06callId\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12K\n" +
	"\x05state\x18\x03 \x01(\x0e20.backend.proto.customer.v1.HistoryLog.Call.StateB\x03\xe0A\x03R\x05state\x12\x1f\n" +
	"\vfail_reason\x18\x04 \x01(\tR\n" +
	"failReason\x12R\n" +
	"\tdirection\x18\x05 \x01(\x0e24.backend.proto.customer.v1.HistoryLog.Call.DirectionR\tdirection\";\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bANSWERED\x10\x01\x12\r\n" +
	"\tNO_ANSWER\x10\x02\"B\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bINCOMING\x10\x01\x12\f\n" +
	"\bOUTGOING\x10\x02\x1a\x1a\n" +
	"\x04Note\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x1a\xce\x01\n" +
	"\x04Task\x12C\n" +
	"\x04type\x18\x01 \x01(\x0e2/.backend.proto.customer.v1.HistoryLog.Task.TypeR\x04type\x123\n" +
	"\x04task\x18\x02 \x01(\v2\x1f.backend.proto.customer.v1.TaskR\x04task\"L\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06CREATE\x10\x01\x12\n" +
	"\n" +
	"\x06UPDATE\x10\x02\x12\n" +
	"\n" +
	"\x06FINISH\x10\x03\x12\n" +
	"\n" +
	"\x06DELETE\x10\x04\x1a\x9f\x01\n" +
	"\aConvert\x12I\n" +
	"\vorigin_type\x18\x01 \x01(\x0e2(.backend.proto.customer.v1.Customer.TypeR\n" +
	"originType\x12I\n" +
	"\vtarget_type\x18\x02 \x01(\x0e2(.backend.proto.customer.v1.Customer.TypeR\n" +
	"targetType\x1a\b\n" +
	"\x06Create\x1a\xb6\x03\n" +
	"\x06Action\x12I\n" +
	"\amessage\x18\x01 \x01(\v2-.backend.proto.customer.v1.HistoryLog.MessageH\x00R\amessage\x12@\n" +
	"\x04call\x18\x02 \x01(\v2*.backend.proto.customer.v1.HistoryLog.CallH\x00R\x04call\x12@\n" +
	"\x04note\x18\x03 \x01(\v2*.backend.proto.customer.v1.HistoryLog.NoteH\x00R\x04note\x12@\n" +
	"\x04task\x18\x04 \x01(\v2*.backend.proto.customer.v1.HistoryLog.TaskH\x00R\x04task\x12I\n" +
	"\aconvert\x18\x05 \x01(\v2-.backend.proto.customer.v1.HistoryLog.ConvertH\x00R\aconvert\x12F\n" +
	"\x06create\x18\x06 \x01(\v2,.backend.proto.customer.v1.HistoryLog.CreateH\x00R\x06createB\b\n" +
	"\x06action\"`\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aMESSAGE\x10\x01\x12\b\n" +
	"\x04CALL\x10\x02\x12\b\n" +
	"\x04NOTE\x10\x03\x12\b\n" +
	"\x04TASK\x10\x04\x12\v\n" +
	"\aCONVERT\x10\x05\x12\n" +
	"\n" +
	"\x06CREATE\x10\x06\"\x8b\x01\n" +
	"\x06Source\x12\x16\n" +
	"\x12SOURCE_UNSPECIFIED\x10\x00\x12\t\n" +
	"\x05STAFF\x10\x01\x12\x0f\n" +
	"\vAPPOINTMENT\x10\x02\x12\x12\n" +
	"\x0eONLINE_BOOKING\x10\x03\x12\v\n" +
	"\aPRODUCT\x10\x04\x12\v\n" +
	"\aPACKAGE\x10\x05\x12\x0f\n" +
	"\vFULFILLMENT\x10\x06\x12\x0e\n" +
	"\n" +
	"MEMBERSHIP\x10\a\"k\n" +
	"\x12CustomizeLifeCycle\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x1d\n" +
	"\n" +
	"is_default\x18\x04 \x01(\x05R\tisDefault\"d\n" +
	"\x14CustomizeActionState\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x05R\x04sort\x12\x14\n" +
	"\x05color\x18\x04 \x01(\tR\x05colorBk\n" +
	"#com.moego.backend.proto.customer.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v1;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v1_customer_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v1_customer_proto_rawDescData []byte
)

func file_backend_proto_customer_v1_customer_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v1_customer_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v1_customer_proto_rawDesc), len(file_backend_proto_customer_v1_customer_proto_rawDesc)))
	})
	return file_backend_proto_customer_v1_customer_proto_rawDescData
}

var file_backend_proto_customer_v1_customer_proto_enumTypes = make([]protoimpl.EnumInfo, 17)
var file_backend_proto_customer_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_backend_proto_customer_v1_customer_proto_goTypes = []any{
	(Customer_LifeCycle)(0),           // 0: backend.proto.customer.v1.Customer.LifeCycle
	(Customer_ActionState)(0),         // 1: backend.proto.customer.v1.Customer.ActionState
	(Customer_State)(0),               // 2: backend.proto.customer.v1.Customer.State
	(Customer_Type)(0),                // 3: backend.proto.customer.v1.Customer.Type
	(Address_State)(0),                // 4: backend.proto.customer.v1.Address.State
	(Address_IsPrimary)(0),            // 5: backend.proto.customer.v1.Address.IsPrimary
	(CustomerContact_State)(0),        // 6: backend.proto.customer.v1.CustomerContact.State
	(CustomerContact_Type)(0),         // 7: backend.proto.customer.v1.CustomerContact.Type
	(CustomerContact_IsPrimary)(0),    // 8: backend.proto.customer.v1.CustomerContact.IsPrimary
	(Task_State)(0),                   // 9: backend.proto.customer.v1.Task.State
	(HistoryLog_Type)(0),              // 10: backend.proto.customer.v1.HistoryLog.Type
	(HistoryLog_Source)(0),            // 11: backend.proto.customer.v1.HistoryLog.Source
	(HistoryLog_Message_State)(0),     // 12: backend.proto.customer.v1.HistoryLog.Message.State
	(HistoryLog_Message_Direction)(0), // 13: backend.proto.customer.v1.HistoryLog.Message.Direction
	(HistoryLog_Call_State)(0),        // 14: backend.proto.customer.v1.HistoryLog.Call.State
	(HistoryLog_Call_Direction)(0),    // 15: backend.proto.customer.v1.HistoryLog.Call.Direction
	(HistoryLog_Task_Type)(0),         // 16: backend.proto.customer.v1.HistoryLog.Task.Type
	(*Customer)(nil),                  // 17: backend.proto.customer.v1.Customer
	(*Address)(nil),                   // 18: backend.proto.customer.v1.Address
	(*CustomerContact)(nil),           // 19: backend.proto.customer.v1.CustomerContact
	(*Task)(nil),                      // 20: backend.proto.customer.v1.Task
	(*HistoryLog)(nil),                // 21: backend.proto.customer.v1.HistoryLog
	(*CustomizeLifeCycle)(nil),        // 22: backend.proto.customer.v1.CustomizeLifeCycle
	(*CustomizeActionState)(nil),      // 23: backend.proto.customer.v1.CustomizeActionState
	(*Customer_AdditionalInfo)(nil),   // 24: backend.proto.customer.v1.Customer.AdditionalInfo
	(*HistoryLog_Message)(nil),        // 25: backend.proto.customer.v1.HistoryLog.Message
	(*HistoryLog_Call)(nil),           // 26: backend.proto.customer.v1.HistoryLog.Call
	(*HistoryLog_Note)(nil),           // 27: backend.proto.customer.v1.HistoryLog.Note
	(*HistoryLog_Task)(nil),           // 28: backend.proto.customer.v1.HistoryLog.Task
	(*HistoryLog_Convert)(nil),        // 29: backend.proto.customer.v1.HistoryLog.Convert
	(*HistoryLog_Create)(nil),         // 30: backend.proto.customer.v1.HistoryLog.Create
	(*HistoryLog_Action)(nil),         // 31: backend.proto.customer.v1.HistoryLog.Action
	(*timestamppb.Timestamp)(nil),     // 32: google.protobuf.Timestamp
}
var file_backend_proto_customer_v1_customer_proto_depIdxs = []int32{
	0,  // 0: backend.proto.customer.v1.Customer.life_cycle:type_name -> backend.proto.customer.v1.Customer.LifeCycle
	1,  // 1: backend.proto.customer.v1.Customer.action_state:type_name -> backend.proto.customer.v1.Customer.ActionState
	32, // 2: backend.proto.customer.v1.Customer.create_time:type_name -> google.protobuf.Timestamp
	32, // 3: backend.proto.customer.v1.Customer.update_time:type_name -> google.protobuf.Timestamp
	32, // 4: backend.proto.customer.v1.Customer.birth_time:type_name -> google.protobuf.Timestamp
	3,  // 5: backend.proto.customer.v1.Customer.type:type_name -> backend.proto.customer.v1.Customer.Type
	18, // 6: backend.proto.customer.v1.Customer.address:type_name -> backend.proto.customer.v1.Address
	19, // 7: backend.proto.customer.v1.Customer.contact:type_name -> backend.proto.customer.v1.CustomerContact
	24, // 8: backend.proto.customer.v1.Customer.additional_info:type_name -> backend.proto.customer.v1.Customer.AdditionalInfo
	2,  // 9: backend.proto.customer.v1.Customer.state:type_name -> backend.proto.customer.v1.Customer.State
	4,  // 10: backend.proto.customer.v1.Address.status:type_name -> backend.proto.customer.v1.Address.State
	5,  // 11: backend.proto.customer.v1.Address.is_primary:type_name -> backend.proto.customer.v1.Address.IsPrimary
	32, // 12: backend.proto.customer.v1.Address.create_time:type_name -> google.protobuf.Timestamp
	32, // 13: backend.proto.customer.v1.Address.update_time:type_name -> google.protobuf.Timestamp
	7,  // 14: backend.proto.customer.v1.CustomerContact.type:type_name -> backend.proto.customer.v1.CustomerContact.Type
	8,  // 15: backend.proto.customer.v1.CustomerContact.is_primary:type_name -> backend.proto.customer.v1.CustomerContact.IsPrimary
	6,  // 16: backend.proto.customer.v1.CustomerContact.state:type_name -> backend.proto.customer.v1.CustomerContact.State
	32, // 17: backend.proto.customer.v1.CustomerContact.create_time:type_name -> google.protobuf.Timestamp
	32, // 18: backend.proto.customer.v1.CustomerContact.update_time:type_name -> google.protobuf.Timestamp
	32, // 19: backend.proto.customer.v1.Task.complete_time:type_name -> google.protobuf.Timestamp
	9,  // 20: backend.proto.customer.v1.Task.state:type_name -> backend.proto.customer.v1.Task.State
	10, // 21: backend.proto.customer.v1.HistoryLog.type:type_name -> backend.proto.customer.v1.HistoryLog.Type
	31, // 22: backend.proto.customer.v1.HistoryLog.action:type_name -> backend.proto.customer.v1.HistoryLog.Action
	32, // 23: backend.proto.customer.v1.HistoryLog.create_time:type_name -> google.protobuf.Timestamp
	11, // 24: backend.proto.customer.v1.HistoryLog.source:type_name -> backend.proto.customer.v1.HistoryLog.Source
	12, // 25: backend.proto.customer.v1.HistoryLog.Message.state:type_name -> backend.proto.customer.v1.HistoryLog.Message.State
	13, // 26: backend.proto.customer.v1.HistoryLog.Message.direction:type_name -> backend.proto.customer.v1.HistoryLog.Message.Direction
	14, // 27: backend.proto.customer.v1.HistoryLog.Call.state:type_name -> backend.proto.customer.v1.HistoryLog.Call.State
	15, // 28: backend.proto.customer.v1.HistoryLog.Call.direction:type_name -> backend.proto.customer.v1.HistoryLog.Call.Direction
	16, // 29: backend.proto.customer.v1.HistoryLog.Task.type:type_name -> backend.proto.customer.v1.HistoryLog.Task.Type
	20, // 30: backend.proto.customer.v1.HistoryLog.Task.task:type_name -> backend.proto.customer.v1.Task
	3,  // 31: backend.proto.customer.v1.HistoryLog.Convert.origin_type:type_name -> backend.proto.customer.v1.Customer.Type
	3,  // 32: backend.proto.customer.v1.HistoryLog.Convert.target_type:type_name -> backend.proto.customer.v1.Customer.Type
	25, // 33: backend.proto.customer.v1.HistoryLog.Action.message:type_name -> backend.proto.customer.v1.HistoryLog.Message
	26, // 34: backend.proto.customer.v1.HistoryLog.Action.call:type_name -> backend.proto.customer.v1.HistoryLog.Call
	27, // 35: backend.proto.customer.v1.HistoryLog.Action.note:type_name -> backend.proto.customer.v1.HistoryLog.Note
	28, // 36: backend.proto.customer.v1.HistoryLog.Action.task:type_name -> backend.proto.customer.v1.HistoryLog.Task
	29, // 37: backend.proto.customer.v1.HistoryLog.Action.convert:type_name -> backend.proto.customer.v1.HistoryLog.Convert
	30, // 38: backend.proto.customer.v1.HistoryLog.Action.create:type_name -> backend.proto.customer.v1.HistoryLog.Create
	39, // [39:39] is the sub-list for method output_type
	39, // [39:39] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v1_customer_proto_init() }
func file_backend_proto_customer_v1_customer_proto_init() {
	if File_backend_proto_customer_v1_customer_proto != nil {
		return
	}
	file_backend_proto_customer_v1_customer_proto_msgTypes[3].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_proto_msgTypes[14].OneofWrappers = []any{
		(*HistoryLog_Action_Message)(nil),
		(*HistoryLog_Action_Call)(nil),
		(*HistoryLog_Action_Note)(nil),
		(*HistoryLog_Action_Task)(nil),
		(*HistoryLog_Action_Convert)(nil),
		(*HistoryLog_Action_Create)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v1_customer_proto_rawDesc), len(file_backend_proto_customer_v1_customer_proto_rawDesc)),
			NumEnums:      17,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_customer_v1_customer_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v1_customer_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v1_customer_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v1_customer_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v1_customer_proto = out.File
	file_backend_proto_customer_v1_customer_proto_goTypes = nil
	file_backend_proto_customer_v1_customer_proto_depIdxs = nil
}
