// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are are only three environments. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are are only three environments. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are are only three environments. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/tools/v1/environment_service.proto

package toolspb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RegisterEnvironmentsRequest
type RegisterEnvironmentsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of environments
	Environments  []*Environment `protobuf:"bytes,1,rep,name=environments,proto3" json:"environments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterEnvironmentsRequest) Reset() {
	*x = RegisterEnvironmentsRequest{}
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterEnvironmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterEnvironmentsRequest) ProtoMessage() {}

func (x *RegisterEnvironmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterEnvironmentsRequest.ProtoReflect.Descriptor instead.
func (*RegisterEnvironmentsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_environment_service_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterEnvironmentsRequest) GetEnvironments() []*Environment {
	if x != nil {
		return x.Environments
	}
	return nil
}

// RegisterEnvironmentsResponse
type RegisterEnvironmentsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of environments
	Environments  []*Environment `protobuf:"bytes,1,rep,name=environments,proto3" json:"environments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterEnvironmentsResponse) Reset() {
	*x = RegisterEnvironmentsResponse{}
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterEnvironmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterEnvironmentsResponse) ProtoMessage() {}

func (x *RegisterEnvironmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterEnvironmentsResponse.ProtoReflect.Descriptor instead.
func (*RegisterEnvironmentsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_environment_service_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterEnvironmentsResponse) GetEnvironments() []*Environment {
	if x != nil {
		return x.Environments
	}
	return nil
}

// CheckEnvironmentRequest
type CheckEnvironmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// environment identifier
	Identifier    *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckEnvironmentRequest) Reset() {
	*x = CheckEnvironmentRequest{}
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckEnvironmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEnvironmentRequest) ProtoMessage() {}

func (x *CheckEnvironmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEnvironmentRequest.ProtoReflect.Descriptor instead.
func (*CheckEnvironmentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_environment_service_proto_rawDescGZIP(), []int{2}
}

func (x *CheckEnvironmentRequest) GetIdentifier() *EnvironmentIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// CheckEnvironmentResponse
type CheckEnvironmentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// environment identifier
	Environment *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=environment,proto3" json:"environment,omitempty"`
	// environment status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// environment healthy
	Healthy       bool `protobuf:"varint,3,opt,name=healthy,proto3" json:"healthy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckEnvironmentResponse) Reset() {
	*x = CheckEnvironmentResponse{}
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckEnvironmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEnvironmentResponse) ProtoMessage() {}

func (x *CheckEnvironmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEnvironmentResponse.ProtoReflect.Descriptor instead.
func (*CheckEnvironmentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_environment_service_proto_rawDescGZIP(), []int{3}
}

func (x *CheckEnvironmentResponse) GetEnvironment() *EnvironmentIdentifier {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *CheckEnvironmentResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CheckEnvironmentResponse) GetHealthy() bool {
	if x != nil {
		return x.Healthy
	}
	return false
}

// ListEnvironmentsRequest
type ListEnvironmentsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// environment status
	Statuses      []string `protobuf:"bytes,1,rep,name=statuses,proto3" json:"statuses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListEnvironmentsRequest) Reset() {
	*x = ListEnvironmentsRequest{}
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListEnvironmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnvironmentsRequest) ProtoMessage() {}

func (x *ListEnvironmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnvironmentsRequest.ProtoReflect.Descriptor instead.
func (*ListEnvironmentsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_environment_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListEnvironmentsRequest) GetStatuses() []string {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// ListEnvironmentsResponse
type ListEnvironmentsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of environments
	Environments  []*Environment `protobuf:"bytes,1,rep,name=environments,proto3" json:"environments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListEnvironmentsResponse) Reset() {
	*x = ListEnvironmentsResponse{}
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListEnvironmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnvironmentsResponse) ProtoMessage() {}

func (x *ListEnvironmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnvironmentsResponse.ProtoReflect.Descriptor instead.
func (*ListEnvironmentsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_environment_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListEnvironmentsResponse) GetEnvironments() []*Environment {
	if x != nil {
		return x.Environments
	}
	return nil
}

// GetEnvironmentRequest
type GetEnvironmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// environment identifier
	Identifier    *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEnvironmentRequest) Reset() {
	*x = GetEnvironmentRequest{}
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEnvironmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnvironmentRequest) ProtoMessage() {}

func (x *GetEnvironmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnvironmentRequest.ProtoReflect.Descriptor instead.
func (*GetEnvironmentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_environment_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetEnvironmentRequest) GetIdentifier() *EnvironmentIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// UpdateEnvironmentRequest
type UpdateEnvironmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// environment identifier
	Identifier *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// environment status
	Status *string `protobuf:"bytes,2,opt,name=status,proto3,oneof" json:"status,omitempty"`
	// environment is managed?
	IsManaged *bool `protobuf:"varint,3,opt,name=is_managed,json=isManaged,proto3,oneof" json:"is_managed,omitempty"`
	// environment name, use to display
	Name *string `protobuf:"bytes,4,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// environment description
	Description *string `protobuf:"bytes,5,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// list of databases
	Databases []*PlatformIdentifier `protobuf:"bytes,6,rep,name=databases,proto3" json:"databases,omitempty"`
	// list of caches
	Caches []*PlatformIdentifier `protobuf:"bytes,7,rep,name=caches,proto3" json:"caches,omitempty"`
	// list of message queues
	MessageQueues []*PlatformIdentifier `protobuf:"bytes,8,rep,name=message_queues,json=messageQueues,proto3" json:"message_queues,omitempty"`
	// extra info
	Extra         *structpb.Struct `protobuf:"bytes,13,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateEnvironmentRequest) Reset() {
	*x = UpdateEnvironmentRequest{}
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEnvironmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEnvironmentRequest) ProtoMessage() {}

func (x *UpdateEnvironmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_environment_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEnvironmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateEnvironmentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_environment_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateEnvironmentRequest) GetIdentifier() *EnvironmentIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *UpdateEnvironmentRequest) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *UpdateEnvironmentRequest) GetIsManaged() bool {
	if x != nil && x.IsManaged != nil {
		return *x.IsManaged
	}
	return false
}

func (x *UpdateEnvironmentRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateEnvironmentRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateEnvironmentRequest) GetDatabases() []*PlatformIdentifier {
	if x != nil {
		return x.Databases
	}
	return nil
}

func (x *UpdateEnvironmentRequest) GetCaches() []*PlatformIdentifier {
	if x != nil {
		return x.Caches
	}
	return nil
}

func (x *UpdateEnvironmentRequest) GetMessageQueues() []*PlatformIdentifier {
	if x != nil {
		return x.MessageQueues
	}
	return nil
}

func (x *UpdateEnvironmentRequest) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

var File_backend_proto_tools_v1_environment_service_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_environment_service_proto_rawDesc = "" +
	"\n" +
	"0backend/proto/tools/v1/environment_service.proto\x12\x16backend.proto.tools.v1\x1a,backend/proto/tools/v1/resource_models.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1bbuf/validate/validate.proto\"f\n" +
	"\x1bRegisterEnvironmentsRequest\x12G\n" +
	"\fenvironments\x18\x01 \x03(\v2#.backend.proto.tools.v1.EnvironmentR\fenvironments\"g\n" +
	"\x1cRegisterEnvironmentsResponse\x12G\n" +
	"\fenvironments\x18\x01 \x03(\v2#.backend.proto.tools.v1.EnvironmentR\fenvironments\"h\n" +
	"\x17CheckEnvironmentRequest\x12M\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierR\n" +
	"identifier\"\x9d\x01\n" +
	"\x18CheckEnvironmentResponse\x12O\n" +
	"\venvironment\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierR\venvironment\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x18\n" +
	"\ahealthy\x18\x03 \x01(\bR\ahealthy\"A\n" +
	"\x17ListEnvironmentsRequest\x12&\n" +
	"\bstatuses\x18\x01 \x03(\tB\n" +
	"\xbaH\a\x92\x01\x04\x10\x05\x18\x01R\bstatuses\"c\n" +
	"\x18ListEnvironmentsResponse\x12G\n" +
	"\fenvironments\x18\x01 \x03(\v2#.backend.proto.tools.v1.EnvironmentR\fenvironments\"f\n" +
	"\x15GetEnvironmentRequest\x12M\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierR\n" +
	"identifier\"\xcf\x04\n" +
	"\x18UpdateEnvironmentRequest\x12M\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierR\n" +
	"identifier\x12\x1b\n" +
	"\x06status\x18\x02 \x01(\tH\x00R\x06status\x88\x01\x01\x12\"\n" +
	"\n" +
	"is_managed\x18\x03 \x01(\bH\x01R\tisManaged\x88\x01\x01\x12 \n" +
	"\x04name\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x18@H\x02R\x04name\x88\x01\x01\x12/\n" +
	"\vdescription\x18\x05 \x01(\tB\b\xbaH\x05r\x03\x18\x80\bH\x03R\vdescription\x88\x01\x01\x12H\n" +
	"\tdatabases\x18\x06 \x03(\v2*.backend.proto.tools.v1.PlatformIdentifierR\tdatabases\x12B\n" +
	"\x06caches\x18\a \x03(\v2*.backend.proto.tools.v1.PlatformIdentifierR\x06caches\x12Q\n" +
	"\x0emessage_queues\x18\b \x03(\v2*.backend.proto.tools.v1.PlatformIdentifierR\rmessageQueues\x122\n" +
	"\x05extra\x18\r \x01(\v2\x17.google.protobuf.StructH\x04R\x05extra\x88\x01\x01B\t\n" +
	"\a_statusB\r\n" +
	"\v_is_managedB\a\n" +
	"\x05_nameB\x0e\n" +
	"\f_descriptionB\b\n" +
	"\x06_extra2\xd8\x04\n" +
	"\x12EnvironmentService\x12\x81\x01\n" +
	"\x14RegisterEnvironments\x123.backend.proto.tools.v1.RegisterEnvironmentsRequest\x1a4.backend.proto.tools.v1.RegisterEnvironmentsResponse\x12u\n" +
	"\x10ListEnvironments\x12/.backend.proto.tools.v1.ListEnvironmentsRequest\x1a0.backend.proto.tools.v1.ListEnvironmentsResponse\x12d\n" +
	"\x0eGetEnvironment\x12-.backend.proto.tools.v1.GetEnvironmentRequest\x1a#.backend.proto.tools.v1.Environment\x12j\n" +
	"\x11UpdateEnvironment\x120.backend.proto.tools.v1.UpdateEnvironmentRequest\x1a#.backend.proto.tools.v1.Environment\x12u\n" +
	"\x10CheckEnvironment\x12/.backend.proto.tools.v1.CheckEnvironmentRequest\x1a0.backend.proto.tools.v1.CheckEnvironmentResponseBb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_environment_service_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_environment_service_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_environment_service_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_environment_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_environment_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_environment_service_proto_rawDesc), len(file_backend_proto_tools_v1_environment_service_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_environment_service_proto_rawDescData
}

var file_backend_proto_tools_v1_environment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_backend_proto_tools_v1_environment_service_proto_goTypes = []any{
	(*RegisterEnvironmentsRequest)(nil),  // 0: backend.proto.tools.v1.RegisterEnvironmentsRequest
	(*RegisterEnvironmentsResponse)(nil), // 1: backend.proto.tools.v1.RegisterEnvironmentsResponse
	(*CheckEnvironmentRequest)(nil),      // 2: backend.proto.tools.v1.CheckEnvironmentRequest
	(*CheckEnvironmentResponse)(nil),     // 3: backend.proto.tools.v1.CheckEnvironmentResponse
	(*ListEnvironmentsRequest)(nil),      // 4: backend.proto.tools.v1.ListEnvironmentsRequest
	(*ListEnvironmentsResponse)(nil),     // 5: backend.proto.tools.v1.ListEnvironmentsResponse
	(*GetEnvironmentRequest)(nil),        // 6: backend.proto.tools.v1.GetEnvironmentRequest
	(*UpdateEnvironmentRequest)(nil),     // 7: backend.proto.tools.v1.UpdateEnvironmentRequest
	(*Environment)(nil),                  // 8: backend.proto.tools.v1.Environment
	(*EnvironmentIdentifier)(nil),        // 9: backend.proto.tools.v1.EnvironmentIdentifier
	(*PlatformIdentifier)(nil),           // 10: backend.proto.tools.v1.PlatformIdentifier
	(*structpb.Struct)(nil),              // 11: google.protobuf.Struct
}
var file_backend_proto_tools_v1_environment_service_proto_depIdxs = []int32{
	8,  // 0: backend.proto.tools.v1.RegisterEnvironmentsRequest.environments:type_name -> backend.proto.tools.v1.Environment
	8,  // 1: backend.proto.tools.v1.RegisterEnvironmentsResponse.environments:type_name -> backend.proto.tools.v1.Environment
	9,  // 2: backend.proto.tools.v1.CheckEnvironmentRequest.identifier:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	9,  // 3: backend.proto.tools.v1.CheckEnvironmentResponse.environment:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	8,  // 4: backend.proto.tools.v1.ListEnvironmentsResponse.environments:type_name -> backend.proto.tools.v1.Environment
	9,  // 5: backend.proto.tools.v1.GetEnvironmentRequest.identifier:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	9,  // 6: backend.proto.tools.v1.UpdateEnvironmentRequest.identifier:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	10, // 7: backend.proto.tools.v1.UpdateEnvironmentRequest.databases:type_name -> backend.proto.tools.v1.PlatformIdentifier
	10, // 8: backend.proto.tools.v1.UpdateEnvironmentRequest.caches:type_name -> backend.proto.tools.v1.PlatformIdentifier
	10, // 9: backend.proto.tools.v1.UpdateEnvironmentRequest.message_queues:type_name -> backend.proto.tools.v1.PlatformIdentifier
	11, // 10: backend.proto.tools.v1.UpdateEnvironmentRequest.extra:type_name -> google.protobuf.Struct
	0,  // 11: backend.proto.tools.v1.EnvironmentService.RegisterEnvironments:input_type -> backend.proto.tools.v1.RegisterEnvironmentsRequest
	4,  // 12: backend.proto.tools.v1.EnvironmentService.ListEnvironments:input_type -> backend.proto.tools.v1.ListEnvironmentsRequest
	6,  // 13: backend.proto.tools.v1.EnvironmentService.GetEnvironment:input_type -> backend.proto.tools.v1.GetEnvironmentRequest
	7,  // 14: backend.proto.tools.v1.EnvironmentService.UpdateEnvironment:input_type -> backend.proto.tools.v1.UpdateEnvironmentRequest
	2,  // 15: backend.proto.tools.v1.EnvironmentService.CheckEnvironment:input_type -> backend.proto.tools.v1.CheckEnvironmentRequest
	1,  // 16: backend.proto.tools.v1.EnvironmentService.RegisterEnvironments:output_type -> backend.proto.tools.v1.RegisterEnvironmentsResponse
	5,  // 17: backend.proto.tools.v1.EnvironmentService.ListEnvironments:output_type -> backend.proto.tools.v1.ListEnvironmentsResponse
	8,  // 18: backend.proto.tools.v1.EnvironmentService.GetEnvironment:output_type -> backend.proto.tools.v1.Environment
	8,  // 19: backend.proto.tools.v1.EnvironmentService.UpdateEnvironment:output_type -> backend.proto.tools.v1.Environment
	3,  // 20: backend.proto.tools.v1.EnvironmentService.CheckEnvironment:output_type -> backend.proto.tools.v1.CheckEnvironmentResponse
	16, // [16:21] is the sub-list for method output_type
	11, // [11:16] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_environment_service_proto_init() }
func file_backend_proto_tools_v1_environment_service_proto_init() {
	if File_backend_proto_tools_v1_environment_service_proto != nil {
		return
	}
	file_backend_proto_tools_v1_resource_models_proto_init()
	file_backend_proto_tools_v1_environment_service_proto_msgTypes[7].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_environment_service_proto_rawDesc), len(file_backend_proto_tools_v1_environment_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_environment_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_environment_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_environment_service_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_environment_service_proto = out.File
	file_backend_proto_tools_v1_environment_service_proto_goTypes = nil
	file_backend_proto_tools_v1_environment_service_proto_depIdxs = nil
}
