// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cache resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cache resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cache resources. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/tools/v1/cache_service.proto

package toolspb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RegisterCacheRequest
type RegisterCacheRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cache identifier
	Identifier    *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterCacheRequest) Reset() {
	*x = RegisterCacheRequest{}
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterCacheRequest) ProtoMessage() {}

func (x *RegisterCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterCacheRequest.ProtoReflect.Descriptor instead.
func (*RegisterCacheRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cache_service_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterCacheRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// RegisterCacheResponse
type RegisterCacheResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cache
	Cache         *Cache `protobuf:"bytes,1,opt,name=cache,proto3,oneof" json:"cache,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterCacheResponse) Reset() {
	*x = RegisterCacheResponse{}
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterCacheResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterCacheResponse) ProtoMessage() {}

func (x *RegisterCacheResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterCacheResponse.ProtoReflect.Descriptor instead.
func (*RegisterCacheResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cache_service_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterCacheResponse) GetCache() *Cache {
	if x != nil {
		return x.Cache
	}
	return nil
}

// ListCachesRequest
type ListCachesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the platform where the cache is running
	Platform *string `protobuf:"bytes,2,opt,name=platform,proto3,oneof" json:"platform,omitempty"`
	// cache engine
	Engine *string `protobuf:"bytes,3,opt,name=engine,proto3,oneof" json:"engine,omitempty"`
	// cache labels
	Labels        map[string]string `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCachesRequest) Reset() {
	*x = ListCachesRequest{}
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCachesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCachesRequest) ProtoMessage() {}

func (x *ListCachesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCachesRequest.ProtoReflect.Descriptor instead.
func (*ListCachesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cache_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListCachesRequest) GetPlatform() string {
	if x != nil && x.Platform != nil {
		return *x.Platform
	}
	return ""
}

func (x *ListCachesRequest) GetEngine() string {
	if x != nil && x.Engine != nil {
		return *x.Engine
	}
	return ""
}

func (x *ListCachesRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// ListCachesResponse
type ListCachesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of cache
	Caches        []*Cache `protobuf:"bytes,1,rep,name=caches,proto3" json:"caches,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCachesResponse) Reset() {
	*x = ListCachesResponse{}
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCachesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCachesResponse) ProtoMessage() {}

func (x *ListCachesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCachesResponse.ProtoReflect.Descriptor instead.
func (*ListCachesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cache_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListCachesResponse) GetCaches() []*Cache {
	if x != nil {
		return x.Caches
	}
	return nil
}

// GetCacheRequest
type GetCacheRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cache identifier
	Identifier    *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCacheRequest) Reset() {
	*x = GetCacheRequest{}
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCacheRequest) ProtoMessage() {}

func (x *GetCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCacheRequest.ProtoReflect.Descriptor instead.
func (*GetCacheRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cache_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetCacheRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// ExecuteCacheCommandRequest
type ExecuteCacheCommandRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cache identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// Types that are valid to be assigned to CommandsType:
	//
	//	*ExecuteCacheCommandRequest_Commands
	//	*ExecuteCacheCommandRequest_RedisScript_
	CommandsType  isExecuteCacheCommandRequest_CommandsType `protobuf_oneof:"commands_type"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteCacheCommandRequest) Reset() {
	*x = ExecuteCacheCommandRequest{}
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteCacheCommandRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteCacheCommandRequest) ProtoMessage() {}

func (x *ExecuteCacheCommandRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteCacheCommandRequest.ProtoReflect.Descriptor instead.
func (*ExecuteCacheCommandRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cache_service_proto_rawDescGZIP(), []int{5}
}

func (x *ExecuteCacheCommandRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *ExecuteCacheCommandRequest) GetCommandsType() isExecuteCacheCommandRequest_CommandsType {
	if x != nil {
		return x.CommandsType
	}
	return nil
}

func (x *ExecuteCacheCommandRequest) GetCommands() string {
	if x != nil {
		if x, ok := x.CommandsType.(*ExecuteCacheCommandRequest_Commands); ok {
			return x.Commands
		}
	}
	return ""
}

func (x *ExecuteCacheCommandRequest) GetRedisScript() *ExecuteCacheCommandRequest_RedisScript {
	if x != nil {
		if x, ok := x.CommandsType.(*ExecuteCacheCommandRequest_RedisScript_); ok {
			return x.RedisScript
		}
	}
	return nil
}

type isExecuteCacheCommandRequest_CommandsType interface {
	isExecuteCacheCommandRequest_CommandsType()
}

type ExecuteCacheCommandRequest_Commands struct {
	// general commands
	Commands string `protobuf:"bytes,2,opt,name=commands,proto3,oneof"`
}

type ExecuteCacheCommandRequest_RedisScript_ struct {
	// redis script
	RedisScript *ExecuteCacheCommandRequest_RedisScript `protobuf:"bytes,3,opt,name=redis_script,json=redisScript,proto3,oneof"`
}

func (*ExecuteCacheCommandRequest_Commands) isExecuteCacheCommandRequest_CommandsType() {}

func (*ExecuteCacheCommandRequest_RedisScript_) isExecuteCacheCommandRequest_CommandsType() {}

// ExecuteCacheCommandResponse
type ExecuteCacheCommandResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// action outputs
	Output        *structpb.Value `protobuf:"bytes,2,opt,name=output,proto3" json:"output,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteCacheCommandResponse) Reset() {
	*x = ExecuteCacheCommandResponse{}
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteCacheCommandResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteCacheCommandResponse) ProtoMessage() {}

func (x *ExecuteCacheCommandResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteCacheCommandResponse.ProtoReflect.Descriptor instead.
func (*ExecuteCacheCommandResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cache_service_proto_rawDescGZIP(), []int{6}
}

func (x *ExecuteCacheCommandResponse) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *ExecuteCacheCommandResponse) GetOutput() *structpb.Value {
	if x != nil {
		return x.Output
	}
	return nil
}

// RedisScript
type ExecuteCacheCommandRequest_RedisScript struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// redis script keys
	Keys []string `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
	// redis script argv
	Argv []string `protobuf:"bytes,2,rep,name=argv,proto3" json:"argv,omitempty"`
	// redis script
	Script        string `protobuf:"bytes,3,opt,name=script,proto3" json:"script,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteCacheCommandRequest_RedisScript) Reset() {
	*x = ExecuteCacheCommandRequest_RedisScript{}
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteCacheCommandRequest_RedisScript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteCacheCommandRequest_RedisScript) ProtoMessage() {}

func (x *ExecuteCacheCommandRequest_RedisScript) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cache_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteCacheCommandRequest_RedisScript.ProtoReflect.Descriptor instead.
func (*ExecuteCacheCommandRequest_RedisScript) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cache_service_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ExecuteCacheCommandRequest_RedisScript) GetKeys() []string {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *ExecuteCacheCommandRequest_RedisScript) GetArgv() []string {
	if x != nil {
		return x.Argv
	}
	return nil
}

func (x *ExecuteCacheCommandRequest_RedisScript) GetScript() string {
	if x != nil {
		return x.Script
	}
	return ""
}

var File_backend_proto_tools_v1_cache_service_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_cache_service_proto_rawDesc = "" +
	"\n" +
	"*backend/proto/tools/v1/cache_service.proto\x12\x16backend.proto.tools.v1\x1a,backend/proto/tools/v1/resource_models.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1bbuf/validate/validate.proto\"b\n" +
	"\x14RegisterCacheRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\"[\n" +
	"\x15RegisterCacheResponse\x128\n" +
	"\x05cache\x18\x01 \x01(\v2\x1d.backend.proto.tools.v1.CacheH\x00R\x05cache\x88\x01\x01B\b\n" +
	"\x06_cache\"\x9f\x02\n" +
	"\x11ListCachesRequest\x12)\n" +
	"\bplatform\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01H\x00R\bplatform\x88\x01\x01\x12%\n" +
	"\x06engine\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01H\x01R\x06engine\x88\x01\x01\x12e\n" +
	"\x06labels\x18\x04 \x03(\v25.backend.proto.tools.v1.ListCachesRequest.LabelsEntryB\x16\xbaH\x13\x9a\x01\x10\x10\x80\x02\"\x05r\x03\x18\xff\x01*\x04r\x02\x18?R\x06labels\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\v\n" +
	"\t_platformB\t\n" +
	"\a_engine\"K\n" +
	"\x12ListCachesResponse\x125\n" +
	"\x06caches\x18\x01 \x03(\v2\x1d.backend.proto.tools.v1.CacheR\x06caches\"]\n" +
	"\x0fGetCacheRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\"\xe1\x02\n" +
	"\x1aExecuteCacheCommandRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12'\n" +
	"\bcommands\x18\x02 \x01(\tB\t\xbaH\x06r\x04\x18\x80\x80\x04H\x00R\bcommands\x12c\n" +
	"\fredis_script\x18\x03 \x01(\v2>.backend.proto.tools.v1.ExecuteCacheCommandRequest.RedisScriptH\x00R\vredisScript\x1aX\n" +
	"\vRedisScript\x12\x12\n" +
	"\x04keys\x18\x01 \x03(\tR\x04keys\x12\x12\n" +
	"\x04argv\x18\x02 \x03(\tR\x04argv\x12!\n" +
	"\x06script\x18\x03 \x01(\tB\t\xbaH\x06r\x04\x18\x80\x80\x04R\x06scriptB\x0f\n" +
	"\rcommands_type\"\x99\x01\n" +
	"\x1bExecuteCacheCommandResponse\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12.\n" +
	"\x06output\x18\x02 \x01(\v2\x16.google.protobuf.ValueR\x06output2\xb5\x03\n" +
	"\fCacheService\x12l\n" +
	"\rRegisterCache\x12,.backend.proto.tools.v1.RegisterCacheRequest\x1a-.backend.proto.tools.v1.RegisterCacheResponse\x12c\n" +
	"\n" +
	"ListCaches\x12).backend.proto.tools.v1.ListCachesRequest\x1a*.backend.proto.tools.v1.ListCachesResponse\x12R\n" +
	"\bGetCache\x12'.backend.proto.tools.v1.GetCacheRequest\x1a\x1d.backend.proto.tools.v1.Cache\x12~\n" +
	"\x13ExecuteCacheCommand\x122.backend.proto.tools.v1.ExecuteCacheCommandRequest\x1a3.backend.proto.tools.v1.ExecuteCacheCommandResponseBb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_cache_service_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_cache_service_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_cache_service_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_cache_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_cache_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_cache_service_proto_rawDesc), len(file_backend_proto_tools_v1_cache_service_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_cache_service_proto_rawDescData
}

var file_backend_proto_tools_v1_cache_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_backend_proto_tools_v1_cache_service_proto_goTypes = []any{
	(*RegisterCacheRequest)(nil),                   // 0: backend.proto.tools.v1.RegisterCacheRequest
	(*RegisterCacheResponse)(nil),                  // 1: backend.proto.tools.v1.RegisterCacheResponse
	(*ListCachesRequest)(nil),                      // 2: backend.proto.tools.v1.ListCachesRequest
	(*ListCachesResponse)(nil),                     // 3: backend.proto.tools.v1.ListCachesResponse
	(*GetCacheRequest)(nil),                        // 4: backend.proto.tools.v1.GetCacheRequest
	(*ExecuteCacheCommandRequest)(nil),             // 5: backend.proto.tools.v1.ExecuteCacheCommandRequest
	(*ExecuteCacheCommandResponse)(nil),            // 6: backend.proto.tools.v1.ExecuteCacheCommandResponse
	nil,                                            // 7: backend.proto.tools.v1.ListCachesRequest.LabelsEntry
	(*ExecuteCacheCommandRequest_RedisScript)(nil), // 8: backend.proto.tools.v1.ExecuteCacheCommandRequest.RedisScript
	(*PlatformIdentifier)(nil),                     // 9: backend.proto.tools.v1.PlatformIdentifier
	(*Cache)(nil),                                  // 10: backend.proto.tools.v1.Cache
	(*structpb.Value)(nil),                         // 11: google.protobuf.Value
}
var file_backend_proto_tools_v1_cache_service_proto_depIdxs = []int32{
	9,  // 0: backend.proto.tools.v1.RegisterCacheRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	10, // 1: backend.proto.tools.v1.RegisterCacheResponse.cache:type_name -> backend.proto.tools.v1.Cache
	7,  // 2: backend.proto.tools.v1.ListCachesRequest.labels:type_name -> backend.proto.tools.v1.ListCachesRequest.LabelsEntry
	10, // 3: backend.proto.tools.v1.ListCachesResponse.caches:type_name -> backend.proto.tools.v1.Cache
	9,  // 4: backend.proto.tools.v1.GetCacheRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	9,  // 5: backend.proto.tools.v1.ExecuteCacheCommandRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	8,  // 6: backend.proto.tools.v1.ExecuteCacheCommandRequest.redis_script:type_name -> backend.proto.tools.v1.ExecuteCacheCommandRequest.RedisScript
	9,  // 7: backend.proto.tools.v1.ExecuteCacheCommandResponse.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	11, // 8: backend.proto.tools.v1.ExecuteCacheCommandResponse.output:type_name -> google.protobuf.Value
	0,  // 9: backend.proto.tools.v1.CacheService.RegisterCache:input_type -> backend.proto.tools.v1.RegisterCacheRequest
	2,  // 10: backend.proto.tools.v1.CacheService.ListCaches:input_type -> backend.proto.tools.v1.ListCachesRequest
	4,  // 11: backend.proto.tools.v1.CacheService.GetCache:input_type -> backend.proto.tools.v1.GetCacheRequest
	5,  // 12: backend.proto.tools.v1.CacheService.ExecuteCacheCommand:input_type -> backend.proto.tools.v1.ExecuteCacheCommandRequest
	1,  // 13: backend.proto.tools.v1.CacheService.RegisterCache:output_type -> backend.proto.tools.v1.RegisterCacheResponse
	3,  // 14: backend.proto.tools.v1.CacheService.ListCaches:output_type -> backend.proto.tools.v1.ListCachesResponse
	10, // 15: backend.proto.tools.v1.CacheService.GetCache:output_type -> backend.proto.tools.v1.Cache
	6,  // 16: backend.proto.tools.v1.CacheService.ExecuteCacheCommand:output_type -> backend.proto.tools.v1.ExecuteCacheCommandResponse
	13, // [13:17] is the sub-list for method output_type
	9,  // [9:13] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_cache_service_proto_init() }
func file_backend_proto_tools_v1_cache_service_proto_init() {
	if File_backend_proto_tools_v1_cache_service_proto != nil {
		return
	}
	file_backend_proto_tools_v1_resource_models_proto_init()
	file_backend_proto_tools_v1_cache_service_proto_msgTypes[1].OneofWrappers = []any{}
	file_backend_proto_tools_v1_cache_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_tools_v1_cache_service_proto_msgTypes[5].OneofWrappers = []any{
		(*ExecuteCacheCommandRequest_Commands)(nil),
		(*ExecuteCacheCommandRequest_RedisScript_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_cache_service_proto_rawDesc), len(file_backend_proto_tools_v1_cache_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_cache_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_cache_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_cache_service_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_cache_service_proto = out.File
	file_backend_proto_tools_v1_cache_service_proto_goTypes = nil
	file_backend_proto_tools_v1_cache_service_proto_depIdxs = nil
}
