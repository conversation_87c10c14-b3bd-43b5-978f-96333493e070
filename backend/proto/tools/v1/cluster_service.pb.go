// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cluster resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cluster resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cluster resources. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/tools/v1/cluster_service.proto

package toolspb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ListClustersRequest
type ListClustersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the platform where the cluster is running
	Platform      *string `protobuf:"bytes,1,opt,name=platform,proto3,oneof" json:"platform,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClustersRequest) Reset() {
	*x = ListClustersRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClustersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClustersRequest) ProtoMessage() {}

func (x *ListClustersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClustersRequest.ProtoReflect.Descriptor instead.
func (*ListClustersRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListClustersRequest) GetPlatform() string {
	if x != nil && x.Platform != nil {
		return *x.Platform
	}
	return ""
}

// ListClustersResponse
type ListClustersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of clusters
	Clusters      []*Cluster `protobuf:"bytes,1,rep,name=clusters,proto3" json:"clusters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClustersResponse) Reset() {
	*x = ListClustersResponse{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClustersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClustersResponse) ProtoMessage() {}

func (x *ListClustersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClustersResponse.ProtoReflect.Descriptor instead.
func (*ListClustersResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListClustersResponse) GetClusters() []*Cluster {
	if x != nil {
		return x.Clusters
	}
	return nil
}

// GetClusterRequest
type GetClusterRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cluster identifier
	Identifier    *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClusterRequest) Reset() {
	*x = GetClusterRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClusterRequest) ProtoMessage() {}

func (x *GetClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClusterRequest.ProtoReflect.Descriptor instead.
func (*GetClusterRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetClusterRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// ListNodeGroupsRequest
type ListNodeGroupsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// platform
	Platform string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	// cluster
	Cluster       string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNodeGroupsRequest) Reset() {
	*x = ListNodeGroupsRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodeGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeGroupsRequest) ProtoMessage() {}

func (x *ListNodeGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeGroupsRequest.ProtoReflect.Descriptor instead.
func (*ListNodeGroupsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListNodeGroupsRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *ListNodeGroupsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

// ListNodeGroupsResponse
type ListNodeGroupsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of node groups
	NodeGroups    []*NodeGroup `protobuf:"bytes,1,rep,name=node_groups,json=nodeGroups,proto3" json:"node_groups,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNodeGroupsResponse) Reset() {
	*x = ListNodeGroupsResponse{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodeGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeGroupsResponse) ProtoMessage() {}

func (x *ListNodeGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeGroupsResponse.ProtoReflect.Descriptor instead.
func (*ListNodeGroupsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListNodeGroupsResponse) GetNodeGroups() []*NodeGroup {
	if x != nil {
		return x.NodeGroups
	}
	return nil
}

// GetNodeGroupRequest
type GetNodeGroupRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// nodegroup identifier
	Identifier    *NodeGroup_Identifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNodeGroupRequest) Reset() {
	*x = GetNodeGroupRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNodeGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeGroupRequest) ProtoMessage() {}

func (x *GetNodeGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeGroupRequest.ProtoReflect.Descriptor instead.
func (*GetNodeGroupRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetNodeGroupRequest) GetIdentifier() *NodeGroup_Identifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// ListNodesRequest
type ListNodesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// platform
	Platform string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	// cluster
	Cluster       string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNodesRequest) Reset() {
	*x = ListNodesRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodesRequest) ProtoMessage() {}

func (x *ListNodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodesRequest.ProtoReflect.Descriptor instead.
func (*ListNodesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListNodesRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *ListNodesRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

// ListNodesResponse
type ListNodesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of nodes
	Nodes         []*structpb.Struct `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNodesResponse) Reset() {
	*x = ListNodesResponse{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodesResponse) ProtoMessage() {}

func (x *ListNodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodesResponse.ProtoReflect.Descriptor instead.
func (*ListNodesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{7}
}

func (x *ListNodesResponse) GetNodes() []*structpb.Struct {
	if x != nil {
		return x.Nodes
	}
	return nil
}

// GetNamespaceRequest
type GetNodeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cluster where node in
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// node name
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNodeRequest) Reset() {
	*x = GetNodeRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeRequest) ProtoMessage() {}

func (x *GetNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeRequest.ProtoReflect.Descriptor instead.
func (*GetNodeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetNodeRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *GetNodeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// ListNamespacesRequest
type ListNamespacesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// platform
	Platform string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	// cluster
	Cluster       string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNamespacesRequest) Reset() {
	*x = ListNamespacesRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNamespacesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNamespacesRequest) ProtoMessage() {}

func (x *ListNamespacesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNamespacesRequest.ProtoReflect.Descriptor instead.
func (*ListNamespacesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListNamespacesRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *ListNamespacesRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

// ListNamespacesResponse
type ListNamespacesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of namespaces
	Namespaces    []*structpb.Struct `protobuf:"bytes,1,rep,name=namespaces,proto3" json:"namespaces,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNamespacesResponse) Reset() {
	*x = ListNamespacesResponse{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNamespacesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNamespacesResponse) ProtoMessage() {}

func (x *ListNamespacesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNamespacesResponse.ProtoReflect.Descriptor instead.
func (*ListNamespacesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListNamespacesResponse) GetNamespaces() []*structpb.Struct {
	if x != nil {
		return x.Namespaces
	}
	return nil
}

// GetNamespaceRequest
type GetNamespaceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// namespace environment
	Environment   *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=environment,proto3,oneof" json:"environment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNamespaceRequest) Reset() {
	*x = GetNamespaceRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNamespaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNamespaceRequest) ProtoMessage() {}

func (x *GetNamespaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNamespaceRequest.ProtoReflect.Descriptor instead.
func (*GetNamespaceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetNamespaceRequest) GetEnvironment() *EnvironmentIdentifier {
	if x != nil {
		return x.Environment
	}
	return nil
}

// ListResourcesRequest
type ListResourcesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cluster environment
	Environment *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=environment,proto3" json:"environment,omitempty"`
	// resource api version
	ApiVersion *string `protobuf:"bytes,2,opt,name=api_version,json=apiVersion,proto3,oneof" json:"api_version,omitempty"`
	// resource kind
	Kind string `protobuf:"bytes,3,opt,name=kind,proto3" json:"kind,omitempty"`
	// page size
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// page token
	PageToken     string `protobuf:"bytes,5,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListResourcesRequest) Reset() {
	*x = ListResourcesRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListResourcesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResourcesRequest) ProtoMessage() {}

func (x *ListResourcesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResourcesRequest.ProtoReflect.Descriptor instead.
func (*ListResourcesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListResourcesRequest) GetEnvironment() *EnvironmentIdentifier {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *ListResourcesRequest) GetApiVersion() string {
	if x != nil && x.ApiVersion != nil {
		return *x.ApiVersion
	}
	return ""
}

func (x *ListResourcesRequest) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ListResourcesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListResourcesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListResourcesResponse
type ListResourcesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of resources
	Resources []*structpb.Struct `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	// next page
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListResourcesResponse) Reset() {
	*x = ListResourcesResponse{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListResourcesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResourcesResponse) ProtoMessage() {}

func (x *ListResourcesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResourcesResponse.ProtoReflect.Descriptor instead.
func (*ListResourcesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{13}
}

func (x *ListResourcesResponse) GetResources() []*structpb.Struct {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *ListResourcesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// GetResourceRequest
type GetResourceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// namespace environment
	Environment *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=environment,proto3,oneof" json:"environment,omitempty"`
	// resource api version
	ApiVersion *string `protobuf:"bytes,2,opt,name=api_version,json=apiVersion,proto3,oneof" json:"api_version,omitempty"`
	// resource kind
	Kind string `protobuf:"bytes,3,opt,name=kind,proto3" json:"kind,omitempty"`
	// resource name
	Name          string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetResourceRequest) Reset() {
	*x = GetResourceRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceRequest) ProtoMessage() {}

func (x *GetResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceRequest.ProtoReflect.Descriptor instead.
func (*GetResourceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetResourceRequest) GetEnvironment() *EnvironmentIdentifier {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *GetResourceRequest) GetApiVersion() string {
	if x != nil && x.ApiVersion != nil {
		return *x.ApiVersion
	}
	return ""
}

func (x *GetResourceRequest) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *GetResourceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// ResetNodeGroupRequest
type ResetNodeGroupRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// nodegroup source identifier
	Source *NodeGroup_Identifier `protobuf:"bytes,1,opt,name=source,proto3,oneof" json:"source,omitempty"`
	// nodegroup target identifier
	Target *NodeGroup_Identifier `protobuf:"bytes,2,opt,name=target,proto3,oneof" json:"target,omitempty"`
	// The instance class of the node group
	InstanceClass *string `protobuf:"bytes,3,opt,name=instance_class,json=instanceClass,proto3,oneof" json:"instance_class,omitempty"`
	// The image used by the node group
	Image *string `protobuf:"bytes,4,opt,name=image,proto3,oneof" json:"image,omitempty"`
	// Minimum number of nodes in a node group
	MinSize *int32 `protobuf:"varint,5,opt,name=min_size,json=minSize,proto3,oneof" json:"min_size,omitempty"`
	// Maximum number of nodes in a node group
	MaxSize *int32 `protobuf:"varint,6,opt,name=max_size,json=maxSize,proto3,oneof" json:"max_size,omitempty"`
	// Desired number of nodes in a node group
	DesiredSize *int32 `protobuf:"varint,7,opt,name=desired_size,json=desiredSize,proto3,oneof" json:"desired_size,omitempty"`
	// disk size for instance
	DiskSize *int32 `protobuf:"varint,8,opt,name=disk_size,json=diskSize,proto3,oneof" json:"disk_size,omitempty"`
	// The labels attached to the node group
	Labels map[string]string `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// extra info
	Extra         *structpb.Struct `protobuf:"bytes,16,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetNodeGroupRequest) Reset() {
	*x = ResetNodeGroupRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetNodeGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetNodeGroupRequest) ProtoMessage() {}

func (x *ResetNodeGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetNodeGroupRequest.ProtoReflect.Descriptor instead.
func (*ResetNodeGroupRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{15}
}

func (x *ResetNodeGroupRequest) GetSource() *NodeGroup_Identifier {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *ResetNodeGroupRequest) GetTarget() *NodeGroup_Identifier {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *ResetNodeGroupRequest) GetInstanceClass() string {
	if x != nil && x.InstanceClass != nil {
		return *x.InstanceClass
	}
	return ""
}

func (x *ResetNodeGroupRequest) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

func (x *ResetNodeGroupRequest) GetMinSize() int32 {
	if x != nil && x.MinSize != nil {
		return *x.MinSize
	}
	return 0
}

func (x *ResetNodeGroupRequest) GetMaxSize() int32 {
	if x != nil && x.MaxSize != nil {
		return *x.MaxSize
	}
	return 0
}

func (x *ResetNodeGroupRequest) GetDesiredSize() int32 {
	if x != nil && x.DesiredSize != nil {
		return *x.DesiredSize
	}
	return 0
}

func (x *ResetNodeGroupRequest) GetDiskSize() int32 {
	if x != nil && x.DiskSize != nil {
		return *x.DiskSize
	}
	return 0
}

func (x *ResetNodeGroupRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResetNodeGroupRequest) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

// ResetNodeGroupsRequest
type ResetNodeGroupsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of node groups
	NodeGroups    []*ResetNodeGroupRequest `protobuf:"bytes,1,rep,name=node_groups,json=nodeGroups,proto3" json:"node_groups,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetNodeGroupsRequest) Reset() {
	*x = ResetNodeGroupsRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetNodeGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetNodeGroupsRequest) ProtoMessage() {}

func (x *ResetNodeGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetNodeGroupsRequest.ProtoReflect.Descriptor instead.
func (*ResetNodeGroupsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{16}
}

func (x *ResetNodeGroupsRequest) GetNodeGroups() []*ResetNodeGroupRequest {
	if x != nil {
		return x.NodeGroups
	}
	return nil
}

// ResetNodeGroupsResponse
type ResetNodeGroupsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// node groups
	NodeGroups    []*NodeGroup `protobuf:"bytes,1,rep,name=node_groups,json=nodeGroups,proto3" json:"node_groups,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetNodeGroupsResponse) Reset() {
	*x = ResetNodeGroupsResponse{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetNodeGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetNodeGroupsResponse) ProtoMessage() {}

func (x *ResetNodeGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetNodeGroupsResponse.ProtoReflect.Descriptor instead.
func (*ResetNodeGroupsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{17}
}

func (x *ResetNodeGroupsResponse) GetNodeGroups() []*NodeGroup {
	if x != nil {
		return x.NodeGroups
	}
	return nil
}

// ResetResourcesRequest
type ResetResourcesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// reset resource from source environment
	SourceEnvironment *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=source_environment,json=sourceEnvironment,proto3,oneof" json:"source_environment,omitempty"`
	// reset resource to source environment
	TargetEnvironment *EnvironmentIdentifier `protobuf:"bytes,2,opt,name=target_environment,json=targetEnvironment,proto3,oneof" json:"target_environment,omitempty"`
	// Specify the list of kubernetes resources to reset
	Resources     []*ResetResourcesRequest_ResourceItems `protobuf:"bytes,3,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetResourcesRequest) Reset() {
	*x = ResetResourcesRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetResourcesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetResourcesRequest) ProtoMessage() {}

func (x *ResetResourcesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetResourcesRequest.ProtoReflect.Descriptor instead.
func (*ResetResourcesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{18}
}

func (x *ResetResourcesRequest) GetSourceEnvironment() *EnvironmentIdentifier {
	if x != nil {
		return x.SourceEnvironment
	}
	return nil
}

func (x *ResetResourcesRequest) GetTargetEnvironment() *EnvironmentIdentifier {
	if x != nil {
		return x.TargetEnvironment
	}
	return nil
}

func (x *ResetResourcesRequest) GetResources() []*ResetResourcesRequest_ResourceItems {
	if x != nil {
		return x.Resources
	}
	return nil
}

// ResetResourcesResponse
type ResetResourcesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of resources
	Resources     []*structpb.Struct `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetResourcesResponse) Reset() {
	*x = ResetResourcesResponse{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetResourcesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetResourcesResponse) ProtoMessage() {}

func (x *ResetResourcesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetResourcesResponse.ProtoReflect.Descriptor instead.
func (*ResetResourcesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{19}
}

func (x *ResetResourcesResponse) GetResources() []*structpb.Struct {
	if x != nil {
		return x.Resources
	}
	return nil
}

// RestartWorkloadsRequest
type RestartWorkloadsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// reset resource from source environment
	Environment *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=environment,proto3" json:"environment,omitempty"`
	// list of workloads
	Identifiers   []*ResourceIdentifier `protobuf:"bytes,2,rep,name=identifiers,proto3" json:"identifiers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestartWorkloadsRequest) Reset() {
	*x = RestartWorkloadsRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestartWorkloadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartWorkloadsRequest) ProtoMessage() {}

func (x *RestartWorkloadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartWorkloadsRequest.ProtoReflect.Descriptor instead.
func (*RestartWorkloadsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{20}
}

func (x *RestartWorkloadsRequest) GetEnvironment() *EnvironmentIdentifier {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *RestartWorkloadsRequest) GetIdentifiers() []*ResourceIdentifier {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

// RestartWorkloadsResponse
type RestartWorkloadsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of resources identifiers
	Identifiers   []*ResourceIdentifier `protobuf:"bytes,1,rep,name=identifiers,proto3" json:"identifiers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestartWorkloadsResponse) Reset() {
	*x = RestartWorkloadsResponse{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestartWorkloadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartWorkloadsResponse) ProtoMessage() {}

func (x *RestartWorkloadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartWorkloadsResponse.ProtoReflect.Descriptor instead.
func (*RestartWorkloadsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{21}
}

func (x *RestartWorkloadsResponse) GetIdentifiers() []*ResourceIdentifier {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

// DeployApplicationsRequest
type DeployApplicationsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// environment to deploy
	Environment *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=environment,proto3" json:"environment,omitempty"`
	// list of applications to deploy
	Applications  []*DeployApplicationsRequest_DeployContext `protobuf:"bytes,2,rep,name=applications,proto3" json:"applications,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeployApplicationsRequest) Reset() {
	*x = DeployApplicationsRequest{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeployApplicationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployApplicationsRequest) ProtoMessage() {}

func (x *DeployApplicationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployApplicationsRequest.ProtoReflect.Descriptor instead.
func (*DeployApplicationsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{22}
}

func (x *DeployApplicationsRequest) GetEnvironment() *EnvironmentIdentifier {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *DeployApplicationsRequest) GetApplications() []*DeployApplicationsRequest_DeployContext {
	if x != nil {
		return x.Applications
	}
	return nil
}

// DeployApplicationsResponse
type DeployApplicationsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// environment to deploy
	Environment *EnvironmentIdentifier `protobuf:"bytes,1,opt,name=environment,proto3" json:"environment,omitempty"`
	// applications status
	Applications  []*structpb.Struct `protobuf:"bytes,2,rep,name=applications,proto3" json:"applications,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeployApplicationsResponse) Reset() {
	*x = DeployApplicationsResponse{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeployApplicationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployApplicationsResponse) ProtoMessage() {}

func (x *DeployApplicationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployApplicationsResponse.ProtoReflect.Descriptor instead.
func (*DeployApplicationsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{23}
}

func (x *DeployApplicationsResponse) GetEnvironment() *EnvironmentIdentifier {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *DeployApplicationsResponse) GetApplications() []*structpb.Struct {
	if x != nil {
		return x.Applications
	}
	return nil
}

// ResourceItems
type ResetResourcesRequest_ResourceItems struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// resource api version
	ApiVersion *string `protobuf:"bytes,1,opt,name=api_version,json=apiVersion,proto3,oneof" json:"api_version,omitempty"`
	// resource kind
	Kind string `protobuf:"bytes,2,opt,name=kind,proto3" json:"kind,omitempty"`
	// list of resources
	Resources     []*structpb.Struct `protobuf:"bytes,3,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetResourcesRequest_ResourceItems) Reset() {
	*x = ResetResourcesRequest_ResourceItems{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetResourcesRequest_ResourceItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetResourcesRequest_ResourceItems) ProtoMessage() {}

func (x *ResetResourcesRequest_ResourceItems) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetResourcesRequest_ResourceItems.ProtoReflect.Descriptor instead.
func (*ResetResourcesRequest_ResourceItems) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{18, 0}
}

func (x *ResetResourcesRequest_ResourceItems) GetApiVersion() string {
	if x != nil && x.ApiVersion != nil {
		return *x.ApiVersion
	}
	return ""
}

func (x *ResetResourcesRequest_ResourceItems) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ResetResourcesRequest_ResourceItems) GetResources() []*structpb.Struct {
	if x != nil {
		return x.Resources
	}
	return nil
}

// DeployContext
type DeployApplicationsRequest_DeployContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// app name
	App string `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
	// image name
	Image *string `protobuf:"bytes,2,opt,name=image,proto3,oneof" json:"image,omitempty"`
	// image tag
	Tag *string `protobuf:"bytes,3,opt,name=tag,proto3,oneof" json:"tag,omitempty"`
	// repository name
	Repo *string `protobuf:"bytes,4,opt,name=repo,proto3,oneof" json:"repo,omitempty"`
	// code branch
	Branch *string `protobuf:"bytes,5,opt,name=branch,proto3,oneof" json:"branch,omitempty"`
	// app version
	Version       *string `protobuf:"bytes,6,opt,name=version,proto3,oneof" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeployApplicationsRequest_DeployContext) Reset() {
	*x = DeployApplicationsRequest_DeployContext{}
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeployApplicationsRequest_DeployContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployApplicationsRequest_DeployContext) ProtoMessage() {}

func (x *DeployApplicationsRequest_DeployContext) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_cluster_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployApplicationsRequest_DeployContext.ProtoReflect.Descriptor instead.
func (*DeployApplicationsRequest_DeployContext) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP(), []int{22, 0}
}

func (x *DeployApplicationsRequest_DeployContext) GetApp() string {
	if x != nil {
		return x.App
	}
	return ""
}

func (x *DeployApplicationsRequest_DeployContext) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

func (x *DeployApplicationsRequest_DeployContext) GetTag() string {
	if x != nil && x.Tag != nil {
		return *x.Tag
	}
	return ""
}

func (x *DeployApplicationsRequest_DeployContext) GetRepo() string {
	if x != nil && x.Repo != nil {
		return *x.Repo
	}
	return ""
}

func (x *DeployApplicationsRequest_DeployContext) GetBranch() string {
	if x != nil && x.Branch != nil {
		return *x.Branch
	}
	return ""
}

func (x *DeployApplicationsRequest_DeployContext) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

var File_backend_proto_tools_v1_cluster_service_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_cluster_service_proto_rawDesc = "" +
	"\n" +
	",backend/proto/tools/v1/cluster_service.proto\x12\x16backend.proto.tools.v1\x1a,backend/proto/tools/v1/resource_models.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1bbuf/validate/validate.proto\"M\n" +
	"\x13ListClustersRequest\x12)\n" +
	"\bplatform\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01H\x00R\bplatform\x88\x01\x01B\v\n" +
	"\t_platform\"S\n" +
	"\x14ListClustersResponse\x12;\n" +
	"\bclusters\x18\x01 \x03(\v2\x1f.backend.proto.tools.v1.ClusterR\bclusters\"_\n" +
	"\x11GetClusterRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\"`\n" +
	"\x15ListNodeGroupsRequest\x12$\n" +
	"\bplatform\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01R\bplatform\x12!\n" +
	"\acluster\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x18?R\acluster\"\\\n" +
	"\x16ListNodeGroupsResponse\x12B\n" +
	"\vnode_groups\x18\x01 \x03(\v2!.backend.proto.tools.v1.NodeGroupR\n" +
	"nodeGroups\"c\n" +
	"\x13GetNodeGroupRequest\x12L\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2,.backend.proto.tools.v1.NodeGroup.IdentifierR\n" +
	"identifier\"[\n" +
	"\x10ListNodesRequest\x12$\n" +
	"\bplatform\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01R\bplatform\x12!\n" +
	"\acluster\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x18?R\acluster\"B\n" +
	"\x11ListNodesResponse\x12-\n" +
	"\x05nodes\x18\x01 \x03(\v2\x17.google.protobuf.StructR\x05nodes\"G\n" +
	"\x0eGetNodeRequest\x12!\n" +
	"\acluster\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x18?R\acluster\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"`\n" +
	"\x15ListNamespacesRequest\x12$\n" +
	"\bplatform\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01R\bplatform\x12!\n" +
	"\acluster\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x18?R\acluster\"Q\n" +
	"\x16ListNamespacesResponse\x127\n" +
	"\n" +
	"namespaces\x18\x01 \x03(\v2\x17.google.protobuf.StructR\n" +
	"namespaces\"{\n" +
	"\x13GetNamespaceRequest\x12T\n" +
	"\venvironment\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierH\x00R\venvironment\x88\x01\x01B\x0e\n" +
	"\f_environment\"\xed\x01\n" +
	"\x14ListResourcesRequest\x12O\n" +
	"\venvironment\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierR\venvironment\x12$\n" +
	"\vapi_version\x18\x02 \x01(\tH\x00R\n" +
	"apiVersion\x88\x01\x01\x12\x12\n" +
	"\x04kind\x18\x03 \x01(\tR\x04kind\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x05 \x01(\tR\tpageTokenB\x0e\n" +
	"\f_api_version\"v\n" +
	"\x15ListResourcesResponse\x125\n" +
	"\tresources\x18\x01 \x03(\v2\x17.google.protobuf.StructR\tresources\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\"\xd8\x01\n" +
	"\x12GetResourceRequest\x12T\n" +
	"\venvironment\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierH\x00R\venvironment\x88\x01\x01\x12$\n" +
	"\vapi_version\x18\x02 \x01(\tH\x01R\n" +
	"apiVersion\x88\x01\x01\x12\x12\n" +
	"\x04kind\x18\x03 \x01(\tR\x04kind\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04nameB\x0e\n" +
	"\f_environmentB\x0e\n" +
	"\f_api_version\"\xce\x05\n" +
	"\x15ResetNodeGroupRequest\x12I\n" +
	"\x06source\x18\x01 \x01(\v2,.backend.proto.tools.v1.NodeGroup.IdentifierH\x00R\x06source\x88\x01\x01\x12I\n" +
	"\x06target\x18\x02 \x01(\v2,.backend.proto.tools.v1.NodeGroup.IdentifierH\x01R\x06target\x88\x01\x01\x12*\n" +
	"\x0einstance_class\x18\x03 \x01(\tH\x02R\rinstanceClass\x88\x01\x01\x12\x19\n" +
	"\x05image\x18\x04 \x01(\tH\x03R\x05image\x88\x01\x01\x12\x1e\n" +
	"\bmin_size\x18\x05 \x01(\x05H\x04R\aminSize\x88\x01\x01\x12\x1e\n" +
	"\bmax_size\x18\x06 \x01(\x05H\x05R\amaxSize\x88\x01\x01\x12&\n" +
	"\fdesired_size\x18\a \x01(\x05H\x06R\vdesiredSize\x88\x01\x01\x12 \n" +
	"\tdisk_size\x18\b \x01(\x05H\aR\bdiskSize\x88\x01\x01\x12i\n" +
	"\x06labels\x18\r \x03(\v29.backend.proto.tools.v1.ResetNodeGroupRequest.LabelsEntryB\x16\xbaH\x13\x9a\x01\x10\x10\x80\x02\"\x05r\x03\x18\xff\x01*\x04r\x02\x18?R\x06labels\x122\n" +
	"\x05extra\x18\x10 \x01(\v2\x17.google.protobuf.StructH\bR\x05extra\x88\x01\x01\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\t\n" +
	"\a_sourceB\t\n" +
	"\a_targetB\x11\n" +
	"\x0f_instance_classB\b\n" +
	"\x06_imageB\v\n" +
	"\t_min_sizeB\v\n" +
	"\t_max_sizeB\x0f\n" +
	"\r_desired_sizeB\f\n" +
	"\n" +
	"_disk_sizeB\b\n" +
	"\x06_extra\"h\n" +
	"\x16ResetNodeGroupsRequest\x12N\n" +
	"\vnode_groups\x18\x01 \x03(\v2-.backend.proto.tools.v1.ResetNodeGroupRequestR\n" +
	"nodeGroups\"]\n" +
	"\x17ResetNodeGroupsResponse\x12B\n" +
	"\vnode_groups\x18\x01 \x03(\v2!.backend.proto.tools.v1.NodeGroupR\n" +
	"nodeGroups\"\xf9\x03\n" +
	"\x15ResetResourcesRequest\x12a\n" +
	"\x12source_environment\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierH\x00R\x11sourceEnvironment\x88\x01\x01\x12a\n" +
	"\x12target_environment\x18\x02 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierH\x01R\x11targetEnvironment\x88\x01\x01\x12Y\n" +
	"\tresources\x18\x03 \x03(\v2;.backend.proto.tools.v1.ResetResourcesRequest.ResourceItemsR\tresources\x1a\x90\x01\n" +
	"\rResourceItems\x12$\n" +
	"\vapi_version\x18\x01 \x01(\tH\x00R\n" +
	"apiVersion\x88\x01\x01\x12\x12\n" +
	"\x04kind\x18\x02 \x01(\tR\x04kind\x125\n" +
	"\tresources\x18\x03 \x03(\v2\x17.google.protobuf.StructR\tresourcesB\x0e\n" +
	"\f_api_versionB\x15\n" +
	"\x13_source_environmentB\x15\n" +
	"\x13_target_environment\"O\n" +
	"\x16ResetResourcesResponse\x125\n" +
	"\tresources\x18\x01 \x03(\v2\x17.google.protobuf.StructR\tresources\"\xb8\x01\n" +
	"\x17RestartWorkloadsRequest\x12O\n" +
	"\venvironment\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierR\venvironment\x12L\n" +
	"\videntifiers\x18\x02 \x03(\v2*.backend.proto.tools.v1.ResourceIdentifierR\videntifiers\"h\n" +
	"\x18RestartWorkloadsResponse\x12L\n" +
	"\videntifiers\x18\x01 \x03(\v2*.backend.proto.tools.v1.ResourceIdentifierR\videntifiers\"\xae\x03\n" +
	"\x19DeployApplicationsRequest\x12O\n" +
	"\venvironment\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierR\venvironment\x12c\n" +
	"\fapplications\x18\x02 \x03(\v2?.backend.proto.tools.v1.DeployApplicationsRequest.DeployContextR\fapplications\x1a\xda\x01\n" +
	"\rDeployContext\x12\x10\n" +
	"\x03app\x18\x01 \x01(\tR\x03app\x12\x19\n" +
	"\x05image\x18\x02 \x01(\tH\x00R\x05image\x88\x01\x01\x12\x15\n" +
	"\x03tag\x18\x03 \x01(\tH\x01R\x03tag\x88\x01\x01\x12\x17\n" +
	"\x04repo\x18\x04 \x01(\tH\x02R\x04repo\x88\x01\x01\x12\x1b\n" +
	"\x06branch\x18\x05 \x01(\tH\x03R\x06branch\x88\x01\x01\x12\x1d\n" +
	"\aversion\x18\x06 \x01(\tH\x04R\aversion\x88\x01\x01B\b\n" +
	"\x06_imageB\x06\n" +
	"\x04_tagB\a\n" +
	"\x05_repoB\t\n" +
	"\a_branchB\n" +
	"\n" +
	"\b_version\"\xaa\x01\n" +
	"\x1aDeployApplicationsResponse\x12O\n" +
	"\venvironment\x18\x01 \x01(\v2-.backend.proto.tools.v1.EnvironmentIdentifierR\venvironment\x12;\n" +
	"\fapplications\x18\x02 \x03(\v2\x17.google.protobuf.StructR\fapplications2\xb6\v\n" +
	"\x0eClusterService\x12i\n" +
	"\fListClusters\x12+.backend.proto.tools.v1.ListClustersRequest\x1a,.backend.proto.tools.v1.ListClustersResponse\x12X\n" +
	"\n" +
	"GetCluster\x12).backend.proto.tools.v1.GetClusterRequest\x1a\x1f.backend.proto.tools.v1.Cluster\x12o\n" +
	"\x0eListNodeGroups\x12-.backend.proto.tools.v1.ListNodeGroupsRequest\x1a..backend.proto.tools.v1.ListNodeGroupsResponse\x12^\n" +
	"\fGetNodeGroup\x12+.backend.proto.tools.v1.GetNodeGroupRequest\x1a!.backend.proto.tools.v1.NodeGroup\x12`\n" +
	"\tListNodes\x12(.backend.proto.tools.v1.ListNodesRequest\x1a).backend.proto.tools.v1.ListNodesResponse\x12J\n" +
	"\aGetNode\x12&.backend.proto.tools.v1.GetNodeRequest\x1a\x17.google.protobuf.Struct\x12o\n" +
	"\x0eListNamespaces\x12-.backend.proto.tools.v1.ListNamespacesRequest\x1a..backend.proto.tools.v1.ListNamespacesResponse\x12T\n" +
	"\fGetNamespace\x12+.backend.proto.tools.v1.GetNamespaceRequest\x1a\x17.google.protobuf.Struct\x12l\n" +
	"\rListResources\x12,.backend.proto.tools.v1.ListResourcesRequest\x1a-.backend.proto.tools.v1.ListResourcesResponse\x12R\n" +
	"\vGetResource\x12*.backend.proto.tools.v1.GetResourceRequest\x1a\x17.google.protobuf.Struct\x12r\n" +
	"\x0fResetNodeGroups\x12..backend.proto.tools.v1.ResetNodeGroupsRequest\x1a/.backend.proto.tools.v1.ResetNodeGroupsResponse\x12o\n" +
	"\x0eResetResources\x12-.backend.proto.tools.v1.ResetResourcesRequest\x1a..backend.proto.tools.v1.ResetResourcesResponse\x12u\n" +
	"\x10RestartWorkloads\x12/.backend.proto.tools.v1.RestartWorkloadsRequest\x1a0.backend.proto.tools.v1.RestartWorkloadsResponse\x12{\n" +
	"\x12DeployApplications\x121.backend.proto.tools.v1.DeployApplicationsRequest\x1a2.backend.proto.tools.v1.DeployApplicationsResponseBb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_cluster_service_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_cluster_service_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_cluster_service_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_cluster_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_cluster_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_cluster_service_proto_rawDesc), len(file_backend_proto_tools_v1_cluster_service_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_cluster_service_proto_rawDescData
}

var file_backend_proto_tools_v1_cluster_service_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_backend_proto_tools_v1_cluster_service_proto_goTypes = []any{
	(*ListClustersRequest)(nil),                     // 0: backend.proto.tools.v1.ListClustersRequest
	(*ListClustersResponse)(nil),                    // 1: backend.proto.tools.v1.ListClustersResponse
	(*GetClusterRequest)(nil),                       // 2: backend.proto.tools.v1.GetClusterRequest
	(*ListNodeGroupsRequest)(nil),                   // 3: backend.proto.tools.v1.ListNodeGroupsRequest
	(*ListNodeGroupsResponse)(nil),                  // 4: backend.proto.tools.v1.ListNodeGroupsResponse
	(*GetNodeGroupRequest)(nil),                     // 5: backend.proto.tools.v1.GetNodeGroupRequest
	(*ListNodesRequest)(nil),                        // 6: backend.proto.tools.v1.ListNodesRequest
	(*ListNodesResponse)(nil),                       // 7: backend.proto.tools.v1.ListNodesResponse
	(*GetNodeRequest)(nil),                          // 8: backend.proto.tools.v1.GetNodeRequest
	(*ListNamespacesRequest)(nil),                   // 9: backend.proto.tools.v1.ListNamespacesRequest
	(*ListNamespacesResponse)(nil),                  // 10: backend.proto.tools.v1.ListNamespacesResponse
	(*GetNamespaceRequest)(nil),                     // 11: backend.proto.tools.v1.GetNamespaceRequest
	(*ListResourcesRequest)(nil),                    // 12: backend.proto.tools.v1.ListResourcesRequest
	(*ListResourcesResponse)(nil),                   // 13: backend.proto.tools.v1.ListResourcesResponse
	(*GetResourceRequest)(nil),                      // 14: backend.proto.tools.v1.GetResourceRequest
	(*ResetNodeGroupRequest)(nil),                   // 15: backend.proto.tools.v1.ResetNodeGroupRequest
	(*ResetNodeGroupsRequest)(nil),                  // 16: backend.proto.tools.v1.ResetNodeGroupsRequest
	(*ResetNodeGroupsResponse)(nil),                 // 17: backend.proto.tools.v1.ResetNodeGroupsResponse
	(*ResetResourcesRequest)(nil),                   // 18: backend.proto.tools.v1.ResetResourcesRequest
	(*ResetResourcesResponse)(nil),                  // 19: backend.proto.tools.v1.ResetResourcesResponse
	(*RestartWorkloadsRequest)(nil),                 // 20: backend.proto.tools.v1.RestartWorkloadsRequest
	(*RestartWorkloadsResponse)(nil),                // 21: backend.proto.tools.v1.RestartWorkloadsResponse
	(*DeployApplicationsRequest)(nil),               // 22: backend.proto.tools.v1.DeployApplicationsRequest
	(*DeployApplicationsResponse)(nil),              // 23: backend.proto.tools.v1.DeployApplicationsResponse
	nil,                                             // 24: backend.proto.tools.v1.ResetNodeGroupRequest.LabelsEntry
	(*ResetResourcesRequest_ResourceItems)(nil),     // 25: backend.proto.tools.v1.ResetResourcesRequest.ResourceItems
	(*DeployApplicationsRequest_DeployContext)(nil), // 26: backend.proto.tools.v1.DeployApplicationsRequest.DeployContext
	(*Cluster)(nil),                                 // 27: backend.proto.tools.v1.Cluster
	(*PlatformIdentifier)(nil),                      // 28: backend.proto.tools.v1.PlatformIdentifier
	(*NodeGroup)(nil),                               // 29: backend.proto.tools.v1.NodeGroup
	(*NodeGroup_Identifier)(nil),                    // 30: backend.proto.tools.v1.NodeGroup.Identifier
	(*structpb.Struct)(nil),                         // 31: google.protobuf.Struct
	(*EnvironmentIdentifier)(nil),                   // 32: backend.proto.tools.v1.EnvironmentIdentifier
	(*ResourceIdentifier)(nil),                      // 33: backend.proto.tools.v1.ResourceIdentifier
}
var file_backend_proto_tools_v1_cluster_service_proto_depIdxs = []int32{
	27, // 0: backend.proto.tools.v1.ListClustersResponse.clusters:type_name -> backend.proto.tools.v1.Cluster
	28, // 1: backend.proto.tools.v1.GetClusterRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	29, // 2: backend.proto.tools.v1.ListNodeGroupsResponse.node_groups:type_name -> backend.proto.tools.v1.NodeGroup
	30, // 3: backend.proto.tools.v1.GetNodeGroupRequest.identifier:type_name -> backend.proto.tools.v1.NodeGroup.Identifier
	31, // 4: backend.proto.tools.v1.ListNodesResponse.nodes:type_name -> google.protobuf.Struct
	31, // 5: backend.proto.tools.v1.ListNamespacesResponse.namespaces:type_name -> google.protobuf.Struct
	32, // 6: backend.proto.tools.v1.GetNamespaceRequest.environment:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	32, // 7: backend.proto.tools.v1.ListResourcesRequest.environment:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	31, // 8: backend.proto.tools.v1.ListResourcesResponse.resources:type_name -> google.protobuf.Struct
	32, // 9: backend.proto.tools.v1.GetResourceRequest.environment:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	30, // 10: backend.proto.tools.v1.ResetNodeGroupRequest.source:type_name -> backend.proto.tools.v1.NodeGroup.Identifier
	30, // 11: backend.proto.tools.v1.ResetNodeGroupRequest.target:type_name -> backend.proto.tools.v1.NodeGroup.Identifier
	24, // 12: backend.proto.tools.v1.ResetNodeGroupRequest.labels:type_name -> backend.proto.tools.v1.ResetNodeGroupRequest.LabelsEntry
	31, // 13: backend.proto.tools.v1.ResetNodeGroupRequest.extra:type_name -> google.protobuf.Struct
	15, // 14: backend.proto.tools.v1.ResetNodeGroupsRequest.node_groups:type_name -> backend.proto.tools.v1.ResetNodeGroupRequest
	29, // 15: backend.proto.tools.v1.ResetNodeGroupsResponse.node_groups:type_name -> backend.proto.tools.v1.NodeGroup
	32, // 16: backend.proto.tools.v1.ResetResourcesRequest.source_environment:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	32, // 17: backend.proto.tools.v1.ResetResourcesRequest.target_environment:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	25, // 18: backend.proto.tools.v1.ResetResourcesRequest.resources:type_name -> backend.proto.tools.v1.ResetResourcesRequest.ResourceItems
	31, // 19: backend.proto.tools.v1.ResetResourcesResponse.resources:type_name -> google.protobuf.Struct
	32, // 20: backend.proto.tools.v1.RestartWorkloadsRequest.environment:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	33, // 21: backend.proto.tools.v1.RestartWorkloadsRequest.identifiers:type_name -> backend.proto.tools.v1.ResourceIdentifier
	33, // 22: backend.proto.tools.v1.RestartWorkloadsResponse.identifiers:type_name -> backend.proto.tools.v1.ResourceIdentifier
	32, // 23: backend.proto.tools.v1.DeployApplicationsRequest.environment:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	26, // 24: backend.proto.tools.v1.DeployApplicationsRequest.applications:type_name -> backend.proto.tools.v1.DeployApplicationsRequest.DeployContext
	32, // 25: backend.proto.tools.v1.DeployApplicationsResponse.environment:type_name -> backend.proto.tools.v1.EnvironmentIdentifier
	31, // 26: backend.proto.tools.v1.DeployApplicationsResponse.applications:type_name -> google.protobuf.Struct
	31, // 27: backend.proto.tools.v1.ResetResourcesRequest.ResourceItems.resources:type_name -> google.protobuf.Struct
	0,  // 28: backend.proto.tools.v1.ClusterService.ListClusters:input_type -> backend.proto.tools.v1.ListClustersRequest
	2,  // 29: backend.proto.tools.v1.ClusterService.GetCluster:input_type -> backend.proto.tools.v1.GetClusterRequest
	3,  // 30: backend.proto.tools.v1.ClusterService.ListNodeGroups:input_type -> backend.proto.tools.v1.ListNodeGroupsRequest
	5,  // 31: backend.proto.tools.v1.ClusterService.GetNodeGroup:input_type -> backend.proto.tools.v1.GetNodeGroupRequest
	6,  // 32: backend.proto.tools.v1.ClusterService.ListNodes:input_type -> backend.proto.tools.v1.ListNodesRequest
	8,  // 33: backend.proto.tools.v1.ClusterService.GetNode:input_type -> backend.proto.tools.v1.GetNodeRequest
	9,  // 34: backend.proto.tools.v1.ClusterService.ListNamespaces:input_type -> backend.proto.tools.v1.ListNamespacesRequest
	11, // 35: backend.proto.tools.v1.ClusterService.GetNamespace:input_type -> backend.proto.tools.v1.GetNamespaceRequest
	12, // 36: backend.proto.tools.v1.ClusterService.ListResources:input_type -> backend.proto.tools.v1.ListResourcesRequest
	14, // 37: backend.proto.tools.v1.ClusterService.GetResource:input_type -> backend.proto.tools.v1.GetResourceRequest
	16, // 38: backend.proto.tools.v1.ClusterService.ResetNodeGroups:input_type -> backend.proto.tools.v1.ResetNodeGroupsRequest
	18, // 39: backend.proto.tools.v1.ClusterService.ResetResources:input_type -> backend.proto.tools.v1.ResetResourcesRequest
	20, // 40: backend.proto.tools.v1.ClusterService.RestartWorkloads:input_type -> backend.proto.tools.v1.RestartWorkloadsRequest
	22, // 41: backend.proto.tools.v1.ClusterService.DeployApplications:input_type -> backend.proto.tools.v1.DeployApplicationsRequest
	1,  // 42: backend.proto.tools.v1.ClusterService.ListClusters:output_type -> backend.proto.tools.v1.ListClustersResponse
	27, // 43: backend.proto.tools.v1.ClusterService.GetCluster:output_type -> backend.proto.tools.v1.Cluster
	4,  // 44: backend.proto.tools.v1.ClusterService.ListNodeGroups:output_type -> backend.proto.tools.v1.ListNodeGroupsResponse
	29, // 45: backend.proto.tools.v1.ClusterService.GetNodeGroup:output_type -> backend.proto.tools.v1.NodeGroup
	7,  // 46: backend.proto.tools.v1.ClusterService.ListNodes:output_type -> backend.proto.tools.v1.ListNodesResponse
	31, // 47: backend.proto.tools.v1.ClusterService.GetNode:output_type -> google.protobuf.Struct
	10, // 48: backend.proto.tools.v1.ClusterService.ListNamespaces:output_type -> backend.proto.tools.v1.ListNamespacesResponse
	31, // 49: backend.proto.tools.v1.ClusterService.GetNamespace:output_type -> google.protobuf.Struct
	13, // 50: backend.proto.tools.v1.ClusterService.ListResources:output_type -> backend.proto.tools.v1.ListResourcesResponse
	31, // 51: backend.proto.tools.v1.ClusterService.GetResource:output_type -> google.protobuf.Struct
	17, // 52: backend.proto.tools.v1.ClusterService.ResetNodeGroups:output_type -> backend.proto.tools.v1.ResetNodeGroupsResponse
	19, // 53: backend.proto.tools.v1.ClusterService.ResetResources:output_type -> backend.proto.tools.v1.ResetResourcesResponse
	21, // 54: backend.proto.tools.v1.ClusterService.RestartWorkloads:output_type -> backend.proto.tools.v1.RestartWorkloadsResponse
	23, // 55: backend.proto.tools.v1.ClusterService.DeployApplications:output_type -> backend.proto.tools.v1.DeployApplicationsResponse
	42, // [42:56] is the sub-list for method output_type
	28, // [28:42] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_cluster_service_proto_init() }
func file_backend_proto_tools_v1_cluster_service_proto_init() {
	if File_backend_proto_tools_v1_cluster_service_proto != nil {
		return
	}
	file_backend_proto_tools_v1_resource_models_proto_init()
	file_backend_proto_tools_v1_cluster_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_tools_v1_cluster_service_proto_msgTypes[11].OneofWrappers = []any{}
	file_backend_proto_tools_v1_cluster_service_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_tools_v1_cluster_service_proto_msgTypes[14].OneofWrappers = []any{}
	file_backend_proto_tools_v1_cluster_service_proto_msgTypes[15].OneofWrappers = []any{}
	file_backend_proto_tools_v1_cluster_service_proto_msgTypes[18].OneofWrappers = []any{}
	file_backend_proto_tools_v1_cluster_service_proto_msgTypes[25].OneofWrappers = []any{}
	file_backend_proto_tools_v1_cluster_service_proto_msgTypes[26].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_cluster_service_proto_rawDesc), len(file_backend_proto_tools_v1_cluster_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_cluster_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_cluster_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_cluster_service_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_cluster_service_proto = out.File
	file_backend_proto_tools_v1_cluster_service_proto_goTypes = nil
	file_backend_proto_tools_v1_cluster_service_proto_depIdxs = nil
}
