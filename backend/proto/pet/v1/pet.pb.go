// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/pet/v1/pet.proto

package petpb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet type
type Pet_PetType int32

const (
	// unspecified
	Pet_PET_TYPE_UNSPECIFIED Pet_PetType = 0
	// dog
	Pet_DOG Pet_PetType = 1
	// cat
	Pet_CAT Pet_PetType = 2
	// bird
	Pet_BIRD Pet_PetType = 3
	// rabbit
	Pet_RABBIT Pet_PetType = 4
	// guinea pig
	Pet_GUINEA_PIG Pet_PetType = 5
	// horse
	Pet_HORSE Pet_PetType = 6
	// rat
	Pet_RAT Pet_PetType = 7
	// mouse
	Pet_MOUSE Pet_PetType = 8
	// hamster
	Pet_HAMSTER Pet_PetType = 9
	// chinchilla
	Pet_CHINCHILLA Pet_PetType = 10
	// other
	Pet_OTHER Pet_PetType = 11
)

// Enum value maps for Pet_PetType.
var (
	Pet_PetType_name = map[int32]string{
		0:  "PET_TYPE_UNSPECIFIED",
		1:  "DOG",
		2:  "CAT",
		3:  "BIRD",
		4:  "RABBIT",
		5:  "GUINEA_PIG",
		6:  "HORSE",
		7:  "RAT",
		8:  "MOUSE",
		9:  "HAMSTER",
		10: "CHINCHILLA",
		11: "OTHER",
	}
	Pet_PetType_value = map[string]int32{
		"PET_TYPE_UNSPECIFIED": 0,
		"DOG":                  1,
		"CAT":                  2,
		"BIRD":                 3,
		"RABBIT":               4,
		"GUINEA_PIG":           5,
		"HORSE":                6,
		"RAT":                  7,
		"MOUSE":                8,
		"HAMSTER":              9,
		"CHINCHILLA":           10,
		"OTHER":                11,
	}
)

func (x Pet_PetType) Enum() *Pet_PetType {
	p := new(Pet_PetType)
	*p = x
	return p
}

func (x Pet_PetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Pet_PetType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_pet_v1_pet_proto_enumTypes[0].Descriptor()
}

func (Pet_PetType) Type() protoreflect.EnumType {
	return &file_backend_proto_pet_v1_pet_proto_enumTypes[0]
}

func (x Pet_PetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Pet_PetType.Descriptor instead.
func (Pet_PetType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_proto_rawDescGZIP(), []int{0, 0}
}

// gender
type Pet_PetGender int32

const (
	// unspecified
	Pet_PET_GENDER_UNSPECIFIED Pet_PetGender = 0
	// male
	Pet_MALE Pet_PetGender = 1
	// female
	Pet_FEMALE Pet_PetGender = 2
	// unknown
	Pet_UNKNOWN Pet_PetGender = 3
)

// Enum value maps for Pet_PetGender.
var (
	Pet_PetGender_name = map[int32]string{
		0: "PET_GENDER_UNSPECIFIED",
		1: "MALE",
		2: "FEMALE",
		3: "UNKNOWN",
	}
	Pet_PetGender_value = map[string]int32{
		"PET_GENDER_UNSPECIFIED": 0,
		"MALE":                   1,
		"FEMALE":                 2,
		"UNKNOWN":                3,
	}
)

func (x Pet_PetGender) Enum() *Pet_PetGender {
	p := new(Pet_PetGender)
	*p = x
	return p
}

func (x Pet_PetGender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Pet_PetGender) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_pet_v1_pet_proto_enumTypes[1].Descriptor()
}

func (Pet_PetGender) Type() protoreflect.EnumType {
	return &file_backend_proto_pet_v1_pet_proto_enumTypes[1]
}

func (x Pet_PetGender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Pet_PetGender.Descriptor instead.
func (Pet_PetGender) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_proto_rawDescGZIP(), []int{0, 1}
}

// pet status
type Pet_State int32

const (
	// unspecified
	Pet_STATE_UNSPECIFIED Pet_State = 0
	// active
	Pet_ACTIVE Pet_State = 1
	// inactive
	Pet_INACTIVE Pet_State = 2
)

// Enum value maps for Pet_State.
var (
	Pet_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
	}
	Pet_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
	}
)

func (x Pet_State) Enum() *Pet_State {
	p := new(Pet_State)
	*p = x
	return p
}

func (x Pet_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Pet_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_pet_v1_pet_proto_enumTypes[2].Descriptor()
}

func (Pet_State) Type() protoreflect.EnumType {
	return &file_backend_proto_pet_v1_pet_proto_enumTypes[2]
}

func (x Pet_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Pet_State.Descriptor instead.
func (Pet_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_proto_rawDescGZIP(), []int{0, 2}
}

// pet life status
type Pet_LifeState int32

const (
	// unspecified
	Pet_LIFE_STATE_UNSPECIFIED Pet_LifeState = 0
	// active
	Pet_LIFE_STATE_ACTIVE Pet_LifeState = 1
	// pass away
	Pet_LIFE_STATE_DIE Pet_LifeState = 2
)

// Enum value maps for Pet_LifeState.
var (
	Pet_LifeState_name = map[int32]string{
		0: "LIFE_STATE_UNSPECIFIED",
		1: "LIFE_STATE_ACTIVE",
		2: "LIFE_STATE_DIE",
	}
	Pet_LifeState_value = map[string]int32{
		"LIFE_STATE_UNSPECIFIED": 0,
		"LIFE_STATE_ACTIVE":      1,
		"LIFE_STATE_DIE":         2,
	}
)

func (x Pet_LifeState) Enum() *Pet_LifeState {
	p := new(Pet_LifeState)
	*p = x
	return p
}

func (x Pet_LifeState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Pet_LifeState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_pet_v1_pet_proto_enumTypes[3].Descriptor()
}

func (Pet_LifeState) Type() protoreflect.EnumType {
	return &file_backend_proto_pet_v1_pet_proto_enumTypes[3]
}

func (x Pet_LifeState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Pet_LifeState.Descriptor instead.
func (Pet_LifeState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_proto_rawDescGZIP(), []int{0, 3}
}

// Pet 表示宠物信息
type Pet struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet name
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// pet type
	PetType Pet_PetType `protobuf:"varint,6,opt,name=pet_type,json=petType,proto3,enum=backend.proto.pet.v1.Pet_PetType" json:"pet_type,omitempty"`
	// breed
	Breed string `protobuf:"bytes,7,opt,name=breed,proto3" json:"breed,omitempty"`
	// gender
	Gender Pet_PetGender `protobuf:"varint,8,opt,name=gender,proto3,enum=backend.proto.pet.v1.Pet_PetGender" json:"gender,omitempty"`
	// mixed
	Mixed bool `protobuf:"varint,9,opt,name=mixed,proto3" json:"mixed,omitempty"`
	// pet state
	State         Pet_State `protobuf:"varint,10,opt,name=state,proto3,enum=backend.proto.pet.v1.Pet_State" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pet) Reset() {
	*x = Pet{}
	mi := &file_backend_proto_pet_v1_pet_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pet) ProtoMessage() {}

func (x *Pet) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pet.ProtoReflect.Descriptor instead.
func (*Pet) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_proto_rawDescGZIP(), []int{0}
}

func (x *Pet) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Pet) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *Pet) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Pet) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *Pet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Pet) GetPetType() Pet_PetType {
	if x != nil {
		return x.PetType
	}
	return Pet_PET_TYPE_UNSPECIFIED
}

func (x *Pet) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *Pet) GetGender() Pet_PetGender {
	if x != nil {
		return x.Gender
	}
	return Pet_PET_GENDER_UNSPECIFIED
}

func (x *Pet) GetMixed() bool {
	if x != nil {
		return x.Mixed
	}
	return false
}

func (x *Pet) GetState() Pet_State {
	if x != nil {
		return x.State
	}
	return Pet_STATE_UNSPECIFIED
}

var File_backend_proto_pet_v1_pet_proto protoreflect.FileDescriptor

const file_backend_proto_pet_v1_pet_proto_rawDesc = "" +
	"\n" +
	"\x1ebackend/proto/pet/v1/pet.proto\x12\x14backend.proto.pet.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\"\xec\x05\n" +
	"\x03Pet\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vcustomer_id\x18\x03 \x01(\x03R\n" +
	"customerId\x12\x1f\n" +
	"\vbusiness_id\x18\x04 \x01(\x03R\n" +
	"businessId\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12<\n" +
	"\bpet_type\x18\x06 \x01(\x0e2!.backend.proto.pet.v1.Pet.PetTypeR\apetType\x12\x14\n" +
	"\x05breed\x18\a \x01(\tR\x05breed\x12;\n" +
	"\x06gender\x18\b \x01(\x0e2#.backend.proto.pet.v1.Pet.PetGenderR\x06gender\x12\x14\n" +
	"\x05mixed\x18\t \x01(\bR\x05mixed\x12:\n" +
	"\x05state\x18\n" +
	" \x01(\x0e2\x1f.backend.proto.pet.v1.Pet.StateB\x03\xe0A\x03R\x05state\"\xa2\x01\n" +
	"\aPetType\x12\x18\n" +
	"\x14PET_TYPE_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03DOG\x10\x01\x12\a\n" +
	"\x03CAT\x10\x02\x12\b\n" +
	"\x04BIRD\x10\x03\x12\n" +
	"\n" +
	"\x06RABBIT\x10\x04\x12\x0e\n" +
	"\n" +
	"GUINEA_PIG\x10\x05\x12\t\n" +
	"\x05HORSE\x10\x06\x12\a\n" +
	"\x03RAT\x10\a\x12\t\n" +
	"\x05MOUSE\x10\b\x12\v\n" +
	"\aHAMSTER\x10\t\x12\x0e\n" +
	"\n" +
	"CHINCHILLA\x10\n" +
	"\x12\t\n" +
	"\x05OTHER\x10\v\"J\n" +
	"\tPetGender\x12\x1a\n" +
	"\x16PET_GENDER_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04MALE\x10\x01\x12\n" +
	"\n" +
	"\x06FEMALE\x10\x02\x12\v\n" +
	"\aUNKNOWN\x10\x03\"8\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\"R\n" +
	"\tLifeState\x12\x1a\n" +
	"\x16LIFE_STATE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11LIFE_STATE_ACTIVE\x10\x01\x12\x12\n" +
	"\x0eLIFE_STATE_DIE\x10\x02B\\\n" +
	"\x1ecom.moego.backend.proto.pet.v1P\x01Z8github.com/MoeGolibrary/moego/backend/proto/pet/v1;petpbb\x06proto3"

var (
	file_backend_proto_pet_v1_pet_proto_rawDescOnce sync.Once
	file_backend_proto_pet_v1_pet_proto_rawDescData []byte
)

func file_backend_proto_pet_v1_pet_proto_rawDescGZIP() []byte {
	file_backend_proto_pet_v1_pet_proto_rawDescOnce.Do(func() {
		file_backend_proto_pet_v1_pet_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_pet_v1_pet_proto_rawDesc), len(file_backend_proto_pet_v1_pet_proto_rawDesc)))
	})
	return file_backend_proto_pet_v1_pet_proto_rawDescData
}

var file_backend_proto_pet_v1_pet_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_backend_proto_pet_v1_pet_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_pet_v1_pet_proto_goTypes = []any{
	(Pet_PetType)(0),   // 0: backend.proto.pet.v1.Pet.PetType
	(Pet_PetGender)(0), // 1: backend.proto.pet.v1.Pet.PetGender
	(Pet_State)(0),     // 2: backend.proto.pet.v1.Pet.State
	(Pet_LifeState)(0), // 3: backend.proto.pet.v1.Pet.LifeState
	(*Pet)(nil),        // 4: backend.proto.pet.v1.Pet
}
var file_backend_proto_pet_v1_pet_proto_depIdxs = []int32{
	0, // 0: backend.proto.pet.v1.Pet.pet_type:type_name -> backend.proto.pet.v1.Pet.PetType
	1, // 1: backend.proto.pet.v1.Pet.gender:type_name -> backend.proto.pet.v1.Pet.PetGender
	2, // 2: backend.proto.pet.v1.Pet.state:type_name -> backend.proto.pet.v1.Pet.State
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_backend_proto_pet_v1_pet_proto_init() }
func file_backend_proto_pet_v1_pet_proto_init() {
	if File_backend_proto_pet_v1_pet_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_pet_v1_pet_proto_rawDesc), len(file_backend_proto_pet_v1_pet_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_pet_v1_pet_proto_goTypes,
		DependencyIndexes: file_backend_proto_pet_v1_pet_proto_depIdxs,
		EnumInfos:         file_backend_proto_pet_v1_pet_proto_enumTypes,
		MessageInfos:      file_backend_proto_pet_v1_pet_proto_msgTypes,
	}.Build()
	File_backend_proto_pet_v1_pet_proto = out.File
	file_backend_proto_pet_v1_pet_proto_goTypes = nil
	file_backend_proto_pet_v1_pet_proto_depIdxs = nil
}
