// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0132::request-field-types=disabled
//     aip.dev/not-precedent: 使用自定义过滤器类型 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用自定义的响应字段结构 --)
// (-- api-linter: core::0132::request-parent-field=disabled
//     aip.dev/not-precedent: 使用int64类型的parent字段表示customer id --)
// (-- api-linter: core::0132::request-parent-behavior=disabled
//     aip.dev/not-precedent: 不需要behavior --)
// (-- api-linter: core::0132::request-parent-reference=disabled
//     aip.dev/not-precedent: 不需要reference，业务不支持 --)

syntax = "proto3";

package backend.proto.pet.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/pet/v1;petpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.pet.v1";

import "buf/validate/validate.proto";
import "google/api/field_behavior.proto";
import "backend/proto/pet/v1/pet.proto";
import "google/protobuf/empty.proto";
// PetService 宠物服务
// 提供宠物相关的操作，包括搜索、索引、创建、更新和列表查询
service PetService {
  //
  // pet document in elasticsearch
  //
  // search pet by term (pet name/customer name)
  rpc SearchPet(SearchPetRequest) returns (SearchPetResponse);
  // 同步客户和宠物数据到Elasticsearch
  // 目前仅用于Airflow调度任务
  rpc IndexPetDocument(IndexPetDocumentRequest) returns (IndexPetDocumentResponse);
  //
  // pet metadata operations
  //
  // create pet in database, this operation not sync to elasticsearch
  rpc CreatePet(CreatePetRequest) returns (google.protobuf.Empty);
  // batch create pet in database, this operation not sync to elasticsearch
  rpc BatchCreatePets(BatchCreatePetsRequest) returns (BatchCreatePetsResponse);
  // 更新宠物信息
  rpc UpdatePet(UpdatePetRequest) returns (google.protobuf.Empty);
  // 列出宠物，数据源为数据库
  // 不提供单独的get方法，可以通过List单个数据实现
  rpc ListPet(ListPetRequest) returns (ListPetResponse);
  // 删除宠物，数据源为数据库
  rpc DeletePet(DeletePetRequest) returns (google.protobuf.Empty);
}

// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// SearchPetRequest 搜索宠物文档请求
message SearchPetRequest {
  // 搜索条件
  message Filter {
    // 按宠物ID搜索
    repeated int64 pet_ids = 2 [
      (google.api.field_behavior) = REQUIRED,
      (buf.validate.field).repeated.min_items = 1,
      (buf.validate.field).repeated.unique = true
    ];
  }
  // 搜索条件，使用 oneof 实现可扩展的搜索条件
  oneof search_criteria {
    // 搜索词（用于搜索宠物名称或客户名称）
    string term = 1 [(buf.validate.field).string.min_len = 1, (buf.validate.field).string.max_len = 100];
    // Filter pet_filter = 2;
    Filter pet_filter = 2;
  }
  // company id 
  optional int64 company_id = 3;
  // 分页大小
  int32 page_size = 10 [
    (google.api.field_behavior) = OPTIONAL,
    (buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000
  ];
  // 分页游标
  optional string page_token = 4;
}


// SearchPetResponse 搜索宠物文档响应
message SearchPetResponse {
  // pet 文档
  message Pet {
    // 客户信息
    message Client {
      // 客户ID
      int64 id = 1;
      // 客户名
      string given_name = 2;
      // 客户姓
      string family_name = 3;
      // 客户全名
      string name = 4;
    }
    // 宠物ID
    int64 id = 1;
    // 客户ID
    int64 customer_id = 2;
    // 宠物名称
    string name = 3;
    // 宠物头像路径
    string avatar_path = 4;
    // 宠物类型
    int32 pet_type = 5;
    // 宠物品种
    string breed = 6;
    // 宠物体重
    string weight = 7;
    // 宠物毛发类型
    string coat_type = 8;
    // 关联的客户信息
    Client client = 9;
  }
  // 宠物列表
  repeated Pet pets = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
  // next page token
  string next_page_token = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
}

// IndexPetDocumentRequest
// 同步数据到ES的请求
message IndexPetDocumentRequest {
  // 同步数据到ES的flag
  message Company {
      // company ids, 如果用company id同步, 会找到所有与该company 关联的客户, 然后同步其信息与pet 信息
      repeated int64 company_ids = 1 [(google.api.field_behavior) = REQUIRED];
    }
  // 客户信息
  message Customer {
    // customer ids(client ids), 根据customer id 同步会一起更新该customer相关的pet信息到es
    repeated int64 customer_ids = 1 [(google.api.field_behavior) = REQUIRED];
  }
  // pet
  message Pet {
    // pet ids, 同步pet的同时会更新该pet 关联的customer信息到es
    repeated int64 pet_ids = 1 [(google.api.field_behavior) = REQUIRED];
  }
  // flags
  oneof flags {
    // company 维度
    Company company = 1;
    // customer 维度
    Customer customer = 2;
    // pet 维度
    Pet pet = 3;
  }
}

// IndexPetDocumentResponse
// 同步数据到ES的响应
message IndexPetDocumentResponse {
   // 错误信息, 表示索引失败的宠物ID
   message Error {
    // 错误类型，例如：
    // - document_missing_exception: 文档不存在
    // - version_conflict_engine_exception: 版本冲突
    // - mapper_parsing_exception: 字段映射错误
    // - 更多错误类型请参考 OpenSearch 文档
    string error_type = 1;
    // 错误具体原因描述
    string error_reason = 2;
    // error id
    int64 id = 3;
  }
  // 成功索引的ID
  repeated int64 success_ids = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
  // 错误的ID
  repeated Error errors = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
}

// CreatePetRequest
message CreatePetRequest {
  // 宠物信息
  Pet pet = 1;
}

// BatchCreatePetRequest
message BatchCreatePetsRequest {
  // 宠物信息
  repeated CreatePetRequest requests = 1 [(google.api.field_behavior) = REQUIRED];
  // 客户ID (customer_id)
  // (-- api-linter: core::0233::request-parent-reference=disabled
  //     aip.dev/not-precedent: 不需要reference，业务不支持 --)
  int64 parent = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
}

// BatchCreatePetResponse
message BatchCreatePetsResponse {
  // 宠物信息
  repeated Pet pets = 1;
}

// UpdatePetRequest
message UpdatePetRequest {
  // pet
  message UpdatePet {
    // pet id
    int64 id = 1;
    // pet name
    optional string name = 2;
    // pet type
    optional Pet.PetType pet_type = 3;
    // pet gender
    optional Pet.PetGender pet_gender = 4;
    // pet breed
    optional string breed = 5;
  }
  // update pet info 
  UpdatePet pet = 1;
}

// ListPetRequest
message ListPetRequest {
  // 过滤条件
  message Filter {
    // (-- api-linter: core::0132::request-field-types=disabled
    //     aip.dev/not-precedent: 使用自定义过滤器类型 --)
    // 按宠物ID列表过滤
    repeated int64 pet_ids = 1 [
      (buf.validate.field).repeated.unique = true
    ];
    // pet type
    optional int32 pet_type = 2;
  }

  // 过滤条件
  Filter filter = 1;

  // parent, 这里指的是customer id, 必填
  int64 parent = 2 [(buf.validate.field).int64.gt = 0];
                               
  // 分页大小
  int32 page_size = 100 [
    (google.api.field_behavior) = OPTIONAL,
    (buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000
  ];
  
  // 排序字段
  optional string order_by = 101;
  
  // 分页参数，使用 oneof 实现不同分页方式
  oneof pagination {
    // 分页令牌，用于基于游标的分页（无限滚动）
    string page_token = 102;
    
    // 页码，用于基于偏移量的分页（深分页）
    int32 page = 103 [(buf.validate.field).int32.gte = 1];
  }
}

// ListPetResponse
message ListPetResponse {
  // 宠物列表
  repeated Pet pets = 1;
  // 分页信息
  oneof pagination {
    // total, 只有深分页时才会有内容
    int32 total = 2;
    // next page token, 用于无线流
    string next_page_token = 3;
  }
}

// DeletePetRequest
message DeletePetRequest {
  // pet id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}



// ErrCode 定义错误码枚举
// 
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because 
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 118800;
}
