// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/sales/v1/moego_pay_custom_fee_approval_service.proto

package salespb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// approval state
type MoegoPayCustomFeeApproval_ApprovalState int32

const (
	// unspecified
	MoegoPayCustomFeeApproval_APPROVAL_STATE_UNSPECIFIED MoegoPayCustomFeeApproval_ApprovalState = 0
	// ignored
	MoegoPayCustomFeeApproval_IGNORED MoegoPayCustomFeeApproval_ApprovalState = 1
	// pending
	MoegoPayCustomFeeApproval_PENDING MoegoPayCustomFeeApproval_ApprovalState = 2
	// approved
	MoegoPayCustomFeeApproval_APPROVED MoegoPayCustomFeeApproval_ApprovalState = 3
	// rejected
	MoegoPayCustomFeeApproval_REJECTED MoegoPayCustomFeeApproval_ApprovalState = 4
)

// Enum value maps for MoegoPayCustomFeeApproval_ApprovalState.
var (
	MoegoPayCustomFeeApproval_ApprovalState_name = map[int32]string{
		0: "APPROVAL_STATE_UNSPECIFIED",
		1: "IGNORED",
		2: "PENDING",
		3: "APPROVED",
		4: "REJECTED",
	}
	MoegoPayCustomFeeApproval_ApprovalState_value = map[string]int32{
		"APPROVAL_STATE_UNSPECIFIED": 0,
		"IGNORED":                    1,
		"PENDING":                    2,
		"APPROVED":                   3,
		"REJECTED":                   4,
	}
)

func (x MoegoPayCustomFeeApproval_ApprovalState) Enum() *MoegoPayCustomFeeApproval_ApprovalState {
	p := new(MoegoPayCustomFeeApproval_ApprovalState)
	*p = x
	return p
}

func (x MoegoPayCustomFeeApproval_ApprovalState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MoegoPayCustomFeeApproval_ApprovalState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_enumTypes[0].Descriptor()
}

func (MoegoPayCustomFeeApproval_ApprovalState) Type() protoreflect.EnumType {
	return &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_enumTypes[0]
}

func (x MoegoPayCustomFeeApproval_ApprovalState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MoegoPayCustomFeeApproval_ApprovalState.Descriptor instead.
func (MoegoPayCustomFeeApproval_ApprovalState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{10, 0}
}

// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: We need to do this because 没必要嵌套一层，不好用. --)
//
// CreateMoegoPayCustomFeeApprovalRequest
type CreateMoegoPayCustomFeeApprovalRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// creator
	Creator string `protobuf:"bytes,1,opt,name=creator,proto3" json:"creator,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// account id
	AccountId int64 `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// owner email
	OwnerEmail string `protobuf:"bytes,4,opt,name=owner_email,json=ownerEmail,proto3" json:"owner_email,omitempty"`
	// 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
	TerminalPercentage string `protobuf:"bytes,5,opt,name=terminal_percentage,json=terminalPercentage,proto3" json:"terminal_percentage,omitempty"`
	// 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
	TerminalFixed string `protobuf:"bytes,6,opt,name=terminal_fixed,json=terminalFixed,proto3" json:"terminal_fixed,omitempty"`
	// 非终端收款手续费百分比，数字格式
	NonTerminalPercentage string `protobuf:"bytes,7,opt,name=non_terminal_percentage,json=nonTerminalPercentage,proto3" json:"non_terminal_percentage,omitempty"`
	// 非终端收款固定手续费，单位为 USD，数字格式
	NonTerminalFixed string `protobuf:"bytes,8,opt,name=non_terminal_fixed,json=nonTerminalFixed,proto3" json:"non_terminal_fixed,omitempty"`
	// 每月最低交易额要求，单位为 USD，数字格式
	MinVolume string `protobuf:"bytes,9,opt,name=min_volume,json=minVolume,proto3" json:"min_volume,omitempty"`
	// spif
	Spif *string `protobuf:"bytes,10,opt,name=spif,proto3,oneof" json:"spif,omitempty"`
	// opportunity id
	OpportunityId *string `protobuf:"bytes,11,opt,name=opportunity_id,json=opportunityId,proto3,oneof" json:"opportunity_id,omitempty"`
	// contract id
	ContractId    *string `protobuf:"bytes,12,opt,name=contract_id,json=contractId,proto3,oneof" json:"contract_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) Reset() {
	*x = CreateMoegoPayCustomFeeApprovalRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMoegoPayCustomFeeApprovalRequest) ProtoMessage() {}

func (x *CreateMoegoPayCustomFeeApprovalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMoegoPayCustomFeeApprovalRequest.ProtoReflect.Descriptor instead.
func (*CreateMoegoPayCustomFeeApprovalRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetOwnerEmail() string {
	if x != nil {
		return x.OwnerEmail
	}
	return ""
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetTerminalPercentage() string {
	if x != nil {
		return x.TerminalPercentage
	}
	return ""
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetTerminalFixed() string {
	if x != nil {
		return x.TerminalFixed
	}
	return ""
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetNonTerminalPercentage() string {
	if x != nil {
		return x.NonTerminalPercentage
	}
	return ""
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetNonTerminalFixed() string {
	if x != nil {
		return x.NonTerminalFixed
	}
	return ""
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetMinVolume() string {
	if x != nil {
		return x.MinVolume
	}
	return ""
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetSpif() string {
	if x != nil && x.Spif != nil {
		return *x.Spif
	}
	return ""
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetOpportunityId() string {
	if x != nil && x.OpportunityId != nil {
		return *x.OpportunityId
	}
	return ""
}

func (x *CreateMoegoPayCustomFeeApprovalRequest) GetContractId() string {
	if x != nil && x.ContractId != nil {
		return *x.ContractId
	}
	return ""
}

// GetMoegoPayCustomFeeApprovalRequest
type GetMoegoPayCustomFeeApprovalRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMoegoPayCustomFeeApprovalRequest) Reset() {
	*x = GetMoegoPayCustomFeeApprovalRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMoegoPayCustomFeeApprovalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMoegoPayCustomFeeApprovalRequest) ProtoMessage() {}

func (x *GetMoegoPayCustomFeeApprovalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMoegoPayCustomFeeApprovalRequest.ProtoReflect.Descriptor instead.
func (*GetMoegoPayCustomFeeApprovalRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetMoegoPayCustomFeeApprovalRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: We need to do this because this model does not have a parent. --)
//
// ListMoegoPayCustomFeeApprovalsRequest
type ListMoegoPayCustomFeeApprovalsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// page size
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// page token
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// query filters
	Filters       *MoegoPayCustomFeeApprovalQueryFilters `protobuf:"bytes,3,opt,name=filters,proto3" json:"filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMoegoPayCustomFeeApprovalsRequest) Reset() {
	*x = ListMoegoPayCustomFeeApprovalsRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMoegoPayCustomFeeApprovalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMoegoPayCustomFeeApprovalsRequest) ProtoMessage() {}

func (x *ListMoegoPayCustomFeeApprovalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMoegoPayCustomFeeApprovalsRequest.ProtoReflect.Descriptor instead.
func (*ListMoegoPayCustomFeeApprovalsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListMoegoPayCustomFeeApprovalsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListMoegoPayCustomFeeApprovalsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListMoegoPayCustomFeeApprovalsRequest) GetFilters() *MoegoPayCustomFeeApprovalQueryFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

// ListMoegoPayCustomFeeApprovalsResponse
type ListMoegoPayCustomFeeApprovalsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// approval list
	MoegoPayCustomFeeApprovals []*MoegoPayCustomFeeApproval `protobuf:"bytes,1,rep,name=moego_pay_custom_fee_approvals,json=moegoPayCustomFeeApprovals,proto3" json:"moego_pay_custom_fee_approvals,omitempty"`
	// next page token
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMoegoPayCustomFeeApprovalsResponse) Reset() {
	*x = ListMoegoPayCustomFeeApprovalsResponse{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMoegoPayCustomFeeApprovalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMoegoPayCustomFeeApprovalsResponse) ProtoMessage() {}

func (x *ListMoegoPayCustomFeeApprovalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMoegoPayCustomFeeApprovalsResponse.ProtoReflect.Descriptor instead.
func (*ListMoegoPayCustomFeeApprovalsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListMoegoPayCustomFeeApprovalsResponse) GetMoegoPayCustomFeeApprovals() []*MoegoPayCustomFeeApproval {
	if x != nil {
		return x.MoegoPayCustomFeeApprovals
	}
	return nil
}

func (x *ListMoegoPayCustomFeeApprovalsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// CountMoegoPayCustomFeeApprovalsRequest
type CountMoegoPayCustomFeeApprovalsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// query filters
	Filters       *MoegoPayCustomFeeApprovalQueryFilters `protobuf:"bytes,3,opt,name=filters,proto3" json:"filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountMoegoPayCustomFeeApprovalsRequest) Reset() {
	*x = CountMoegoPayCustomFeeApprovalsRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountMoegoPayCustomFeeApprovalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountMoegoPayCustomFeeApprovalsRequest) ProtoMessage() {}

func (x *CountMoegoPayCustomFeeApprovalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountMoegoPayCustomFeeApprovalsRequest.ProtoReflect.Descriptor instead.
func (*CountMoegoPayCustomFeeApprovalsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{4}
}

func (x *CountMoegoPayCustomFeeApprovalsRequest) GetFilters() *MoegoPayCustomFeeApprovalQueryFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

// CountMoegoPayCustomFeeApprovalsResponse
type CountMoegoPayCustomFeeApprovalsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// count
	Count         int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountMoegoPayCustomFeeApprovalsResponse) Reset() {
	*x = CountMoegoPayCustomFeeApprovalsResponse{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountMoegoPayCustomFeeApprovalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountMoegoPayCustomFeeApprovalsResponse) ProtoMessage() {}

func (x *CountMoegoPayCustomFeeApprovalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountMoegoPayCustomFeeApprovalsResponse.ProtoReflect.Descriptor instead.
func (*CountMoegoPayCustomFeeApprovalsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{5}
}

func (x *CountMoegoPayCustomFeeApprovalsResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// ApproveMoegoPayCustomFeeApprovalRequest
type ApproveMoegoPayCustomFeeApprovalRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// handler
	Handler       string `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApproveMoegoPayCustomFeeApprovalRequest) Reset() {
	*x = ApproveMoegoPayCustomFeeApprovalRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveMoegoPayCustomFeeApprovalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveMoegoPayCustomFeeApprovalRequest) ProtoMessage() {}

func (x *ApproveMoegoPayCustomFeeApprovalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveMoegoPayCustomFeeApprovalRequest.ProtoReflect.Descriptor instead.
func (*ApproveMoegoPayCustomFeeApprovalRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{6}
}

func (x *ApproveMoegoPayCustomFeeApprovalRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ApproveMoegoPayCustomFeeApprovalRequest) GetHandler() string {
	if x != nil {
		return x.Handler
	}
	return ""
}

// ApproveMoegoPayCustomFeeApprovalResponse
type ApproveMoegoPayCustomFeeApprovalResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// approval
	MoegoPayCustomFeeApproval *MoegoPayCustomFeeApproval `protobuf:"bytes,1,opt,name=moego_pay_custom_fee_approval,json=moegoPayCustomFeeApproval,proto3" json:"moego_pay_custom_fee_approval,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *ApproveMoegoPayCustomFeeApprovalResponse) Reset() {
	*x = ApproveMoegoPayCustomFeeApprovalResponse{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveMoegoPayCustomFeeApprovalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveMoegoPayCustomFeeApprovalResponse) ProtoMessage() {}

func (x *ApproveMoegoPayCustomFeeApprovalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveMoegoPayCustomFeeApprovalResponse.ProtoReflect.Descriptor instead.
func (*ApproveMoegoPayCustomFeeApprovalResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{7}
}

func (x *ApproveMoegoPayCustomFeeApprovalResponse) GetMoegoPayCustomFeeApproval() *MoegoPayCustomFeeApproval {
	if x != nil {
		return x.MoegoPayCustomFeeApproval
	}
	return nil
}

// RejectMoegoPayCustomFeeApprovalRequest
type RejectMoegoPayCustomFeeApprovalRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// handler
	Handler       string `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RejectMoegoPayCustomFeeApprovalRequest) Reset() {
	*x = RejectMoegoPayCustomFeeApprovalRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectMoegoPayCustomFeeApprovalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectMoegoPayCustomFeeApprovalRequest) ProtoMessage() {}

func (x *RejectMoegoPayCustomFeeApprovalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectMoegoPayCustomFeeApprovalRequest.ProtoReflect.Descriptor instead.
func (*RejectMoegoPayCustomFeeApprovalRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{8}
}

func (x *RejectMoegoPayCustomFeeApprovalRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RejectMoegoPayCustomFeeApprovalRequest) GetHandler() string {
	if x != nil {
		return x.Handler
	}
	return ""
}

// RejectMoegoPayCustomFeeApprovalResponse
type RejectMoegoPayCustomFeeApprovalResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// approval
	MoegoPayCustomFeeApproval *MoegoPayCustomFeeApproval `protobuf:"bytes,1,opt,name=moego_pay_custom_fee_approval,json=moegoPayCustomFeeApproval,proto3" json:"moego_pay_custom_fee_approval,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *RejectMoegoPayCustomFeeApprovalResponse) Reset() {
	*x = RejectMoegoPayCustomFeeApprovalResponse{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectMoegoPayCustomFeeApprovalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectMoegoPayCustomFeeApprovalResponse) ProtoMessage() {}

func (x *RejectMoegoPayCustomFeeApprovalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectMoegoPayCustomFeeApprovalResponse.ProtoReflect.Descriptor instead.
func (*RejectMoegoPayCustomFeeApprovalResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{9}
}

func (x *RejectMoegoPayCustomFeeApprovalResponse) GetMoegoPayCustomFeeApproval() *MoegoPayCustomFeeApproval {
	if x != nil {
		return x.MoegoPayCustomFeeApproval
	}
	return nil
}

// MoegoPayCustomFeeApproval
type MoegoPayCustomFeeApproval struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// metadata
	Metadata *MoegoPayCustomFeeApproval_Metadata `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// account id
	AccountId int64 `protobuf:"varint,4,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// owner email
	OwnerEmail string `protobuf:"bytes,5,opt,name=owner_email,json=ownerEmail,proto3" json:"owner_email,omitempty"`
	// 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
	TerminalPercentage string `protobuf:"bytes,6,opt,name=terminal_percentage,json=terminalPercentage,proto3" json:"terminal_percentage,omitempty"`
	// 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
	TerminalFixed string `protobuf:"bytes,7,opt,name=terminal_fixed,json=terminalFixed,proto3" json:"terminal_fixed,omitempty"`
	// 非终端收款手续费百分比，数字格式
	NonTerminalPercentage string `protobuf:"bytes,8,opt,name=non_terminal_percentage,json=nonTerminalPercentage,proto3" json:"non_terminal_percentage,omitempty"`
	// 非终端收款固定手续费，单位为 USD，数字格式
	NonTerminalFixed string `protobuf:"bytes,9,opt,name=non_terminal_fixed,json=nonTerminalFixed,proto3" json:"non_terminal_fixed,omitempty"`
	// 每月最低交易额要求，单位为 USD，数字格式
	MinVolume string `protobuf:"bytes,10,opt,name=min_volume,json=minVolume,proto3" json:"min_volume,omitempty"`
	// creator
	Creator string `protobuf:"bytes,11,opt,name=creator,proto3" json:"creator,omitempty"`
	// handler (approved / rejected by)
	Handler *string `protobuf:"bytes,12,opt,name=handler,proto3,oneof" json:"handler,omitempty"`
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we do not use field behavior. --)
	//
	// approval state
	ApprovalState MoegoPayCustomFeeApproval_ApprovalState `protobuf:"varint,13,opt,name=approval_state,json=approvalState,proto3,enum=backend.proto.sales.v1.MoegoPayCustomFeeApproval_ApprovalState" json:"approval_state,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// handle time (approve / reject time)
	HandleTime    *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=handle_time,json=handleTime,proto3,oneof" json:"handle_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoegoPayCustomFeeApproval) Reset() {
	*x = MoegoPayCustomFeeApproval{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoegoPayCustomFeeApproval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoegoPayCustomFeeApproval) ProtoMessage() {}

func (x *MoegoPayCustomFeeApproval) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoegoPayCustomFeeApproval.ProtoReflect.Descriptor instead.
func (*MoegoPayCustomFeeApproval) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{10}
}

func (x *MoegoPayCustomFeeApproval) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval) GetMetadata() *MoegoPayCustomFeeApproval_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *MoegoPayCustomFeeApproval) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MoegoPayCustomFeeApproval) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *MoegoPayCustomFeeApproval) GetOwnerEmail() string {
	if x != nil {
		return x.OwnerEmail
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval) GetTerminalPercentage() string {
	if x != nil {
		return x.TerminalPercentage
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval) GetTerminalFixed() string {
	if x != nil {
		return x.TerminalFixed
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval) GetNonTerminalPercentage() string {
	if x != nil {
		return x.NonTerminalPercentage
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval) GetNonTerminalFixed() string {
	if x != nil {
		return x.NonTerminalFixed
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval) GetMinVolume() string {
	if x != nil {
		return x.MinVolume
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval) GetHandler() string {
	if x != nil && x.Handler != nil {
		return *x.Handler
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval) GetApprovalState() MoegoPayCustomFeeApproval_ApprovalState {
	if x != nil {
		return x.ApprovalState
	}
	return MoegoPayCustomFeeApproval_APPROVAL_STATE_UNSPECIFIED
}

func (x *MoegoPayCustomFeeApproval) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *MoegoPayCustomFeeApproval) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *MoegoPayCustomFeeApproval) GetHandleTime() *timestamppb.Timestamp {
	if x != nil {
		return x.HandleTime
	}
	return nil
}

// Query filters for MoegoPayCustomFeeApproval
type MoegoPayCustomFeeApprovalQueryFilters struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company_id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// account_id
	AccountId *int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// owner email (prefix like)
	OwnerEmail *string `protobuf:"bytes,3,opt,name=owner_email,json=ownerEmail,proto3,oneof" json:"owner_email,omitempty"`
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we do not use field behavior, and this field is not output only. --)
	//
	// approval states
	ApprovalStates []MoegoPayCustomFeeApproval_ApprovalState `protobuf:"varint,4,rep,packed,name=approval_states,json=approvalStates,proto3,enum=backend.proto.sales.v1.MoegoPayCustomFeeApproval_ApprovalState" json:"approval_states,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MoegoPayCustomFeeApprovalQueryFilters) Reset() {
	*x = MoegoPayCustomFeeApprovalQueryFilters{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoegoPayCustomFeeApprovalQueryFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoegoPayCustomFeeApprovalQueryFilters) ProtoMessage() {}

func (x *MoegoPayCustomFeeApprovalQueryFilters) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoegoPayCustomFeeApprovalQueryFilters.ProtoReflect.Descriptor instead.
func (*MoegoPayCustomFeeApprovalQueryFilters) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{11}
}

func (x *MoegoPayCustomFeeApprovalQueryFilters) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *MoegoPayCustomFeeApprovalQueryFilters) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *MoegoPayCustomFeeApprovalQueryFilters) GetOwnerEmail() string {
	if x != nil && x.OwnerEmail != nil {
		return *x.OwnerEmail
	}
	return ""
}

func (x *MoegoPayCustomFeeApprovalQueryFilters) GetApprovalStates() []MoegoPayCustomFeeApproval_ApprovalState {
	if x != nil {
		return x.ApprovalStates
	}
	return nil
}

// metadata, fields marked as optional mean their values may not exist
type MoegoPayCustomFeeApproval_Metadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// spif
	Spif *string `protobuf:"bytes,1,opt,name=spif,proto3,oneof" json:"spif,omitempty"`
	// opportunity id
	OpportunityId *string `protobuf:"bytes,2,opt,name=opportunity_id,json=opportunityId,proto3,oneof" json:"opportunity_id,omitempty"`
	// contract id
	ContractId    *string `protobuf:"bytes,3,opt,name=contract_id,json=contractId,proto3,oneof" json:"contract_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoegoPayCustomFeeApproval_Metadata) Reset() {
	*x = MoegoPayCustomFeeApproval_Metadata{}
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoegoPayCustomFeeApproval_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoegoPayCustomFeeApproval_Metadata) ProtoMessage() {}

func (x *MoegoPayCustomFeeApproval_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoegoPayCustomFeeApproval_Metadata.ProtoReflect.Descriptor instead.
func (*MoegoPayCustomFeeApproval_Metadata) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *MoegoPayCustomFeeApproval_Metadata) GetSpif() string {
	if x != nil && x.Spif != nil {
		return *x.Spif
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval_Metadata) GetOpportunityId() string {
	if x != nil && x.OpportunityId != nil {
		return *x.OpportunityId
	}
	return ""
}

func (x *MoegoPayCustomFeeApproval_Metadata) GetContractId() string {
	if x != nil && x.ContractId != nil {
		return *x.ContractId
	}
	return ""
}

var File_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto protoreflect.FileDescriptor

const file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDesc = "" +
	"\n" +
	"Bbackend/proto/sales/v1/moego_pay_custom_fee_approval_service.proto\x12\x16backend.proto.sales.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd9\x05\n" +
	"&CreateMoegoPayCustomFeeApprovalRequest\x12!\n" +
	"\acreator\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\acreator\x12&\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12&\n" +
	"\n" +
	"account_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\taccountId\x12(\n" +
	"\vowner_email\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\n" +
	"ownerEmail\x12O\n" +
	"\x13terminal_percentage\x18\x05 \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\x12terminalPercentage\x12E\n" +
	"\x0eterminal_fixed\x18\x06 \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\rterminalFixed\x12V\n" +
	"\x17non_terminal_percentage\x18\a \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\x15nonTerminalPercentage\x12L\n" +
	"\x12non_terminal_fixed\x18\b \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\x10nonTerminalFixed\x12=\n" +
	"\n" +
	"min_volume\x18\t \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\tminVolume\x12\x17\n" +
	"\x04spif\x18\n" +
	" \x01(\tH\x00R\x04spif\x88\x01\x01\x12*\n" +
	"\x0eopportunity_id\x18\v \x01(\tH\x01R\ropportunityId\x88\x01\x01\x12$\n" +
	"\vcontract_id\x18\f \x01(\tH\x02R\n" +
	"contractId\x88\x01\x01B\a\n" +
	"\x05_spifB\x11\n" +
	"\x0f_opportunity_idB\x0e\n" +
	"\f_contract_id\">\n" +
	"#GetMoegoPayCustomFeeApprovalRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id\"\xc5\x01\n" +
	"%ListMoegoPayCustomFeeApprovalsRequest\x12$\n" +
	"\tpage_size\x18\x01 \x01(\x05B\a\xbaH\x04\x1a\x02 \x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tR\tpageToken\x12W\n" +
	"\afilters\x18\x03 \x01(\v2=.backend.proto.sales.v1.MoegoPayCustomFeeApprovalQueryFiltersR\afilters\"\xc7\x01\n" +
	"&ListMoegoPayCustomFeeApprovalsResponse\x12u\n" +
	"\x1emoego_pay_custom_fee_approvals\x18\x01 \x03(\v21.backend.proto.sales.v1.MoegoPayCustomFeeApprovalR\x1amoegoPayCustomFeeApprovals\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\"\x81\x01\n" +
	"&CountMoegoPayCustomFeeApprovalsRequest\x12W\n" +
	"\afilters\x18\x03 \x01(\v2=.backend.proto.sales.v1.MoegoPayCustomFeeApprovalQueryFiltersR\afilters\"?\n" +
	"'CountMoegoPayCustomFeeApprovalsResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\"e\n" +
	"'ApproveMoegoPayCustomFeeApprovalRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id\x12!\n" +
	"\ahandler\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\ahandler\"\x9f\x01\n" +
	"(ApproveMoegoPayCustomFeeApprovalResponse\x12s\n" +
	"\x1dmoego_pay_custom_fee_approval\x18\x01 \x01(\v21.backend.proto.sales.v1.MoegoPayCustomFeeApprovalR\x19moegoPayCustomFeeApproval\"d\n" +
	"&RejectMoegoPayCustomFeeApprovalRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id\x12!\n" +
	"\ahandler\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\ahandler\"\x9e\x01\n" +
	"'RejectMoegoPayCustomFeeApprovalResponse\x12s\n" +
	"\x1dmoego_pay_custom_fee_approval\x18\x01 \x01(\v21.backend.proto.sales.v1.MoegoPayCustomFeeApprovalR\x19moegoPayCustomFeeApproval\"\xc3\b\n" +
	"\x19MoegoPayCustomFeeApproval\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12V\n" +
	"\bmetadata\x18\x02 \x01(\v2:.backend.proto.sales.v1.MoegoPayCustomFeeApproval.MetadataR\bmetadata\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x12\x1d\n" +
	"\n" +
	"account_id\x18\x04 \x01(\x03R\taccountId\x12\x1f\n" +
	"\vowner_email\x18\x05 \x01(\tR\n" +
	"ownerEmail\x12/\n" +
	"\x13terminal_percentage\x18\x06 \x01(\tR\x12terminalPercentage\x12%\n" +
	"\x0eterminal_fixed\x18\a \x01(\tR\rterminalFixed\x126\n" +
	"\x17non_terminal_percentage\x18\b \x01(\tR\x15nonTerminalPercentage\x12,\n" +
	"\x12non_terminal_fixed\x18\t \x01(\tR\x10nonTerminalFixed\x12\x1d\n" +
	"\n" +
	"min_volume\x18\n" +
	" \x01(\tR\tminVolume\x12\x18\n" +
	"\acreator\x18\v \x01(\tR\acreator\x12\x1d\n" +
	"\ahandler\x18\f \x01(\tH\x00R\ahandler\x88\x01\x01\x12f\n" +
	"\x0eapproval_state\x18\r \x01(\x0e2?.backend.proto.sales.v1.MoegoPayCustomFeeApproval.ApprovalStateR\rapprovalState\x12;\n" +
	"\vcreate_time\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12@\n" +
	"\vhandle_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\n" +
	"handleTime\x88\x01\x01\x1a\xa1\x01\n" +
	"\bMetadata\x12\x17\n" +
	"\x04spif\x18\x01 \x01(\tH\x00R\x04spif\x88\x01\x01\x12*\n" +
	"\x0eopportunity_id\x18\x02 \x01(\tH\x01R\ropportunityId\x88\x01\x01\x12$\n" +
	"\vcontract_id\x18\x03 \x01(\tH\x02R\n" +
	"contractId\x88\x01\x01B\a\n" +
	"\x05_spifB\x11\n" +
	"\x0f_opportunity_idB\x0e\n" +
	"\f_contract_id\"e\n" +
	"\rApprovalState\x12\x1e\n" +
	"\x1aAPPROVAL_STATE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aIGNORED\x10\x01\x12\v\n" +
	"\aPENDING\x10\x02\x12\f\n" +
	"\bAPPROVED\x10\x03\x12\f\n" +
	"\bREJECTED\x10\x04B\n" +
	"\n" +
	"\b_handlerB\x0e\n" +
	"\f_handle_time\"\xad\x02\n" +
	"%MoegoPayCustomFeeApprovalQueryFilters\x12\"\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03H\x00R\tcompanyId\x88\x01\x01\x12\"\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03H\x01R\taccountId\x88\x01\x01\x12$\n" +
	"\vowner_email\x18\x03 \x01(\tH\x02R\n" +
	"ownerEmail\x88\x01\x01\x12h\n" +
	"\x0fapproval_states\x18\x04 \x03(\x0e2?.backend.proto.sales.v1.MoegoPayCustomFeeApproval.ApprovalStateR\x0eapprovalStatesB\r\n" +
	"\v_company_idB\r\n" +
	"\v_account_idB\x0e\n" +
	"\f_owner_email2\xde\a\n" +
	" MoegoPayCustomFeeApprovalService\x12\x94\x01\n" +
	"\x1fCreateMoegoPayCustomFeeApproval\x12>.backend.proto.sales.v1.CreateMoegoPayCustomFeeApprovalRequest\x1a1.backend.proto.sales.v1.MoegoPayCustomFeeApproval\x12\x8e\x01\n" +
	"\x1cGetMoegoPayCustomFeeApproval\x12;.backend.proto.sales.v1.GetMoegoPayCustomFeeApprovalRequest\x1a1.backend.proto.sales.v1.MoegoPayCustomFeeApproval\x12\x9f\x01\n" +
	"\x1eListMoegoPayCustomFeeApprovals\x12=.backend.proto.sales.v1.ListMoegoPayCustomFeeApprovalsRequest\x1a>.backend.proto.sales.v1.ListMoegoPayCustomFeeApprovalsResponse\x12\xa2\x01\n" +
	"\x1fCountMoegoPayCustomFeeApprovals\x12>.backend.proto.sales.v1.CountMoegoPayCustomFeeApprovalsRequest\x1a?.backend.proto.sales.v1.CountMoegoPayCustomFeeApprovalsResponse\x12\xa5\x01\n" +
	" ApproveMoegoPayCustomFeeApproval\x12?.backend.proto.sales.v1.ApproveMoegoPayCustomFeeApprovalRequest\<EMAIL>\x12\xa2\x01\n" +
	"\x1fRejectMoegoPayCustomFeeApproval\x12>.backend.proto.sales.v1.RejectMoegoPayCustomFeeApprovalRequest\x1a?.backend.proto.sales.v1.RejectMoegoPayCustomFeeApprovalResponseBb\n" +
	" com.moego.backend.proto.sales.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespbb\x06proto3"

var (
	file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescOnce sync.Once
	file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescData []byte
)

func file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescGZIP() []byte {
	file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDesc), len(file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDesc)))
	})
	return file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDescData
}

var file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_goTypes = []any{
	(MoegoPayCustomFeeApproval_ApprovalState)(0),     // 0: backend.proto.sales.v1.MoegoPayCustomFeeApproval.ApprovalState
	(*CreateMoegoPayCustomFeeApprovalRequest)(nil),   // 1: backend.proto.sales.v1.CreateMoegoPayCustomFeeApprovalRequest
	(*GetMoegoPayCustomFeeApprovalRequest)(nil),      // 2: backend.proto.sales.v1.GetMoegoPayCustomFeeApprovalRequest
	(*ListMoegoPayCustomFeeApprovalsRequest)(nil),    // 3: backend.proto.sales.v1.ListMoegoPayCustomFeeApprovalsRequest
	(*ListMoegoPayCustomFeeApprovalsResponse)(nil),   // 4: backend.proto.sales.v1.ListMoegoPayCustomFeeApprovalsResponse
	(*CountMoegoPayCustomFeeApprovalsRequest)(nil),   // 5: backend.proto.sales.v1.CountMoegoPayCustomFeeApprovalsRequest
	(*CountMoegoPayCustomFeeApprovalsResponse)(nil),  // 6: backend.proto.sales.v1.CountMoegoPayCustomFeeApprovalsResponse
	(*ApproveMoegoPayCustomFeeApprovalRequest)(nil),  // 7: backend.proto.sales.v1.ApproveMoegoPayCustomFeeApprovalRequest
	(*ApproveMoegoPayCustomFeeApprovalResponse)(nil), // 8: backend.proto.sales.v1.ApproveMoegoPayCustomFeeApprovalResponse
	(*RejectMoegoPayCustomFeeApprovalRequest)(nil),   // 9: backend.proto.sales.v1.RejectMoegoPayCustomFeeApprovalRequest
	(*RejectMoegoPayCustomFeeApprovalResponse)(nil),  // 10: backend.proto.sales.v1.RejectMoegoPayCustomFeeApprovalResponse
	(*MoegoPayCustomFeeApproval)(nil),                // 11: backend.proto.sales.v1.MoegoPayCustomFeeApproval
	(*MoegoPayCustomFeeApprovalQueryFilters)(nil),    // 12: backend.proto.sales.v1.MoegoPayCustomFeeApprovalQueryFilters
	(*MoegoPayCustomFeeApproval_Metadata)(nil),       // 13: backend.proto.sales.v1.MoegoPayCustomFeeApproval.Metadata
	(*timestamppb.Timestamp)(nil),                    // 14: google.protobuf.Timestamp
}
var file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_depIdxs = []int32{
	12, // 0: backend.proto.sales.v1.ListMoegoPayCustomFeeApprovalsRequest.filters:type_name -> backend.proto.sales.v1.MoegoPayCustomFeeApprovalQueryFilters
	11, // 1: backend.proto.sales.v1.ListMoegoPayCustomFeeApprovalsResponse.moego_pay_custom_fee_approvals:type_name -> backend.proto.sales.v1.MoegoPayCustomFeeApproval
	12, // 2: backend.proto.sales.v1.CountMoegoPayCustomFeeApprovalsRequest.filters:type_name -> backend.proto.sales.v1.MoegoPayCustomFeeApprovalQueryFilters
	11, // 3: backend.proto.sales.v1.ApproveMoegoPayCustomFeeApprovalResponse.moego_pay_custom_fee_approval:type_name -> backend.proto.sales.v1.MoegoPayCustomFeeApproval
	11, // 4: backend.proto.sales.v1.RejectMoegoPayCustomFeeApprovalResponse.moego_pay_custom_fee_approval:type_name -> backend.proto.sales.v1.MoegoPayCustomFeeApproval
	13, // 5: backend.proto.sales.v1.MoegoPayCustomFeeApproval.metadata:type_name -> backend.proto.sales.v1.MoegoPayCustomFeeApproval.Metadata
	0,  // 6: backend.proto.sales.v1.MoegoPayCustomFeeApproval.approval_state:type_name -> backend.proto.sales.v1.MoegoPayCustomFeeApproval.ApprovalState
	14, // 7: backend.proto.sales.v1.MoegoPayCustomFeeApproval.create_time:type_name -> google.protobuf.Timestamp
	14, // 8: backend.proto.sales.v1.MoegoPayCustomFeeApproval.update_time:type_name -> google.protobuf.Timestamp
	14, // 9: backend.proto.sales.v1.MoegoPayCustomFeeApproval.handle_time:type_name -> google.protobuf.Timestamp
	0,  // 10: backend.proto.sales.v1.MoegoPayCustomFeeApprovalQueryFilters.approval_states:type_name -> backend.proto.sales.v1.MoegoPayCustomFeeApproval.ApprovalState
	1,  // 11: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.CreateMoegoPayCustomFeeApproval:input_type -> backend.proto.sales.v1.CreateMoegoPayCustomFeeApprovalRequest
	2,  // 12: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.GetMoegoPayCustomFeeApproval:input_type -> backend.proto.sales.v1.GetMoegoPayCustomFeeApprovalRequest
	3,  // 13: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.ListMoegoPayCustomFeeApprovals:input_type -> backend.proto.sales.v1.ListMoegoPayCustomFeeApprovalsRequest
	5,  // 14: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.CountMoegoPayCustomFeeApprovals:input_type -> backend.proto.sales.v1.CountMoegoPayCustomFeeApprovalsRequest
	7,  // 15: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.ApproveMoegoPayCustomFeeApproval:input_type -> backend.proto.sales.v1.ApproveMoegoPayCustomFeeApprovalRequest
	9,  // 16: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.RejectMoegoPayCustomFeeApproval:input_type -> backend.proto.sales.v1.RejectMoegoPayCustomFeeApprovalRequest
	11, // 17: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.CreateMoegoPayCustomFeeApproval:output_type -> backend.proto.sales.v1.MoegoPayCustomFeeApproval
	11, // 18: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.GetMoegoPayCustomFeeApproval:output_type -> backend.proto.sales.v1.MoegoPayCustomFeeApproval
	4,  // 19: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.ListMoegoPayCustomFeeApprovals:output_type -> backend.proto.sales.v1.ListMoegoPayCustomFeeApprovalsResponse
	6,  // 20: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.CountMoegoPayCustomFeeApprovals:output_type -> backend.proto.sales.v1.CountMoegoPayCustomFeeApprovalsResponse
	8,  // 21: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.ApproveMoegoPayCustomFeeApproval:output_type -> backend.proto.sales.v1.ApproveMoegoPayCustomFeeApprovalResponse
	10, // 22: backend.proto.sales.v1.MoegoPayCustomFeeApprovalService.RejectMoegoPayCustomFeeApproval:output_type -> backend.proto.sales.v1.RejectMoegoPayCustomFeeApprovalResponse
	17, // [17:23] is the sub-list for method output_type
	11, // [11:17] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_init() }
func file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_init() {
	if File_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto != nil {
		return
	}
	file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[11].OneofWrappers = []any{}
	file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes[12].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDesc), len(file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_msgTypes,
	}.Build()
	File_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto = out.File
	file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_goTypes = nil
	file_backend_proto_sales_v1_moego_pay_custom_fee_approval_service_proto_depIdxs = nil
}
