// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-package=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-multiple-files=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        (unknown)
// source: backend/proto/cs_page_watcher/v1/cs_page_watcher.proto

package cs_page_watcher

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CompleteJiraRequest is CompleteIncidentRequest
type CompleteJiraRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// jira_key ...
	JiraKey       string `protobuf:"bytes,1,opt,name=jira_key,json=jiraKey,proto3" json:"jira_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CompleteJiraRequest) Reset() {
	*x = CompleteJiraRequest{}
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompleteJiraRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteJiraRequest) ProtoMessage() {}

func (x *CompleteJiraRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteJiraRequest.ProtoReflect.Descriptor instead.
func (*CompleteJiraRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescGZIP(), []int{0}
}

func (x *CompleteJiraRequest) GetJiraKey() string {
	if x != nil {
		return x.JiraKey
	}
	return ""
}

// CompleteIncidentRequest is CompleteIncidentRequest
type CompleteIncidentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id ...
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// title ...
	Title         string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CompleteIncidentRequest) Reset() {
	*x = CompleteIncidentRequest{}
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompleteIncidentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteIncidentRequest) ProtoMessage() {}

func (x *CompleteIncidentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteIncidentRequest.ProtoReflect.Descriptor instead.
func (*CompleteIncidentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescGZIP(), []int{1}
}

func (x *CompleteIncidentRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CompleteIncidentRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

// DefaultResponse is DefaultResponse
type DefaultResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message is the message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	// error_code is error_code
	ErrorCode     int32 `protobuf:"varint,2,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DefaultResponse) Reset() {
	*x = DefaultResponse{}
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DefaultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DefaultResponse) ProtoMessage() {}

func (x *DefaultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DefaultResponse.ProtoReflect.Descriptor instead.
func (*DefaultResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescGZIP(), []int{2}
}

func (x *DefaultResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DefaultResponse) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

// TriggerPageRequest is the body of TriggerPage
type TriggerPageRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// jira_key is jira_key
	JiraKey       string `protobuf:"bytes,1,opt,name=jira_key,json=jiraKey,proto3" json:"jira_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerPageRequest) Reset() {
	*x = TriggerPageRequest{}
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerPageRequest) ProtoMessage() {}

func (x *TriggerPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerPageRequest.ProtoReflect.Descriptor instead.
func (*TriggerPageRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescGZIP(), []int{3}
}

func (x *TriggerPageRequest) GetJiraKey() string {
	if x != nil {
		return x.JiraKey
	}
	return ""
}

// TriggerAdminTaskRequest is the body of TriggerAdminTask
type TriggerAdminTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// jira_key is jira_key
	JiraKey       string `protobuf:"bytes,1,opt,name=jira_key,json=jiraKey,proto3" json:"jira_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerAdminTaskRequest) Reset() {
	*x = TriggerAdminTaskRequest{}
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerAdminTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerAdminTaskRequest) ProtoMessage() {}

func (x *TriggerAdminTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerAdminTaskRequest.ProtoReflect.Descriptor instead.
func (*TriggerAdminTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescGZIP(), []int{4}
}

func (x *TriggerAdminTaskRequest) GetJiraKey() string {
	if x != nil {
		return x.JiraKey
	}
	return ""
}

// RunTaskRequest is the request for RunTask
type RunTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// task is the name of the task to run
	Task          string `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RunTaskRequest) Reset() {
	*x = RunTaskRequest{}
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RunTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunTaskRequest) ProtoMessage() {}

func (x *RunTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunTaskRequest.ProtoReflect.Descriptor instead.
func (*RunTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescGZIP(), []int{5}
}

func (x *RunTaskRequest) GetTask() string {
	if x != nil {
		return x.Task
	}
	return ""
}

var File_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto protoreflect.FileDescriptor

const file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDesc = "" +
	"\n" +
	"6backend/proto/cs_page_watcher/v1/cs_page_watcher.proto\x12 backend.proto.cs_page_watcher.v1\"0\n" +
	"\x13CompleteJiraRequest\x12\x19\n" +
	"\bjira_key\x18\x01 \x01(\tR\ajiraKey\"?\n" +
	"\x17CompleteIncidentRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\"J\n" +
	"\x0fDefaultResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\x12\x1d\n" +
	"\n" +
	"error_code\x18\x02 \x01(\x05R\terrorCode\"/\n" +
	"\x12TriggerPageRequest\x12\x19\n" +
	"\bjira_key\x18\x01 \x01(\tR\ajiraKey\"4\n" +
	"\x17TriggerAdminTaskRequest\x12\x19\n" +
	"\bjira_key\x18\x01 \x01(\tR\ajiraKey\"$\n" +
	"\x0eRunTaskRequest\x12\x12\n" +
	"\x04task\x18\x01 \x01(\tR\x04task2\xfe\x04\n" +
	"\x14CSPageWatcherService\x12v\n" +
	"\vTriggerPage\x124.backend.proto.cs_page_watcher.v1.TriggerPageRequest\x1a1.backend.proto.cs_page_watcher.v1.DefaultResponse\x12\x80\x01\n" +
	"\x10TriggerAdminTask\x129.backend.proto.cs_page_watcher.v1.TriggerAdminTaskRequest\x1a1.backend.proto.cs_page_watcher.v1.DefaultResponse\x12\x80\x01\n" +
	"\x10CompleteIncident\x129.backend.proto.cs_page_watcher.v1.CompleteIncidentRequest\x1a1.backend.proto.cs_page_watcher.v1.DefaultResponse\x12x\n" +
	"\fCompleteJira\x125.backend.proto.cs_page_watcher.v1.CompleteJiraRequest\x1a1.backend.proto.cs_page_watcher.v1.DefaultResponse\x12n\n" +
	"\aRunTask\x120.backend.proto.cs_page_watcher.v1.RunTaskRequest\x1a1.backend.proto.cs_page_watcher.v1.DefaultResponseBPZNgithub.com/MoeGolibrary/moego/backend/proto/cs_page_watcher/v1;cs_page_watcherb\x06proto3"

var (
	file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescOnce sync.Once
	file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescData []byte
)

func file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescGZIP() []byte {
	file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescOnce.Do(func() {
		file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDesc), len(file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDesc)))
	})
	return file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDescData
}

var file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_goTypes = []any{
	(*CompleteJiraRequest)(nil),     // 0: backend.proto.cs_page_watcher.v1.CompleteJiraRequest
	(*CompleteIncidentRequest)(nil), // 1: backend.proto.cs_page_watcher.v1.CompleteIncidentRequest
	(*DefaultResponse)(nil),         // 2: backend.proto.cs_page_watcher.v1.DefaultResponse
	(*TriggerPageRequest)(nil),      // 3: backend.proto.cs_page_watcher.v1.TriggerPageRequest
	(*TriggerAdminTaskRequest)(nil), // 4: backend.proto.cs_page_watcher.v1.TriggerAdminTaskRequest
	(*RunTaskRequest)(nil),          // 5: backend.proto.cs_page_watcher.v1.RunTaskRequest
}
var file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_depIdxs = []int32{
	3, // 0: backend.proto.cs_page_watcher.v1.CSPageWatcherService.TriggerPage:input_type -> backend.proto.cs_page_watcher.v1.TriggerPageRequest
	4, // 1: backend.proto.cs_page_watcher.v1.CSPageWatcherService.TriggerAdminTask:input_type -> backend.proto.cs_page_watcher.v1.TriggerAdminTaskRequest
	1, // 2: backend.proto.cs_page_watcher.v1.CSPageWatcherService.CompleteIncident:input_type -> backend.proto.cs_page_watcher.v1.CompleteIncidentRequest
	0, // 3: backend.proto.cs_page_watcher.v1.CSPageWatcherService.CompleteJira:input_type -> backend.proto.cs_page_watcher.v1.CompleteJiraRequest
	5, // 4: backend.proto.cs_page_watcher.v1.CSPageWatcherService.RunTask:input_type -> backend.proto.cs_page_watcher.v1.RunTaskRequest
	2, // 5: backend.proto.cs_page_watcher.v1.CSPageWatcherService.TriggerPage:output_type -> backend.proto.cs_page_watcher.v1.DefaultResponse
	2, // 6: backend.proto.cs_page_watcher.v1.CSPageWatcherService.TriggerAdminTask:output_type -> backend.proto.cs_page_watcher.v1.DefaultResponse
	2, // 7: backend.proto.cs_page_watcher.v1.CSPageWatcherService.CompleteIncident:output_type -> backend.proto.cs_page_watcher.v1.DefaultResponse
	2, // 8: backend.proto.cs_page_watcher.v1.CSPageWatcherService.CompleteJira:output_type -> backend.proto.cs_page_watcher.v1.DefaultResponse
	2, // 9: backend.proto.cs_page_watcher.v1.CSPageWatcherService.RunTask:output_type -> backend.proto.cs_page_watcher.v1.DefaultResponse
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_init() }
func file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_init() {
	if File_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDesc), len(file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_goTypes,
		DependencyIndexes: file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_depIdxs,
		MessageInfos:      file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_msgTypes,
	}.Build()
	File_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto = out.File
	file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_goTypes = nil
	file_backend_proto_cs_page_watcher_v1_cs_page_watcher_proto_depIdxs = nil
}
