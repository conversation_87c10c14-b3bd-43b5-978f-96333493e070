// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-package=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-multiple-files=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
syntax = "proto3";

package backend.proto.cs_page_watcher.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/cs_page_watcher/v1;cs_page_watcher";

// CSPageWatcherService 定义了 CSPageWatcher 服务的接口。
service CSPageWatcherService {
    // TriggerPage start a cs page from jira ticket
    rpc TriggerPage(TriggerPageRequest) returns (DefaultResponse);
    // TriggerAdminTask assigns jira ticket to appropriate commander
    rpc TriggerAdminTask(TriggerAdminTaskRequest) returns (DefaultResponse);
    // CompleteIncident ...
    rpc CompleteIncident(CompleteIncidentRequest) returns (DefaultResponse);
    // CompleteJira ...
    rpc CompleteJira(CompleteJiraRequest) returns (DefaultResponse);
    // RunTask executes a specific task by name
    rpc RunTask(RunTaskRequest) returns (DefaultResponse);
}

// CompleteJiraRequest is CompleteIncidentRequest
message CompleteJiraRequest{
    // jira_key ...
    string jira_key=1;
}

// CompleteIncidentRequest is CompleteIncidentRequest
message CompleteIncidentRequest{
    // id ...
    string id=1;
    // title ...
    string title=2;
}

// DefaultResponse is DefaultResponse
message DefaultResponse{
    // message is the message
    string message=1;
    // error_code is error_code
    int32 error_code=2;
}

// TriggerPageRequest is the body of TriggerPage
message TriggerPageRequest{
    // jira_key is jira_key
    string jira_key=1;
}

// TriggerAdminTaskRequest is the body of TriggerAdminTask
message TriggerAdminTaskRequest{
    // jira_key is jira_key
    string jira_key=1;
}
// RunTaskRequest is the request for RunTask
message RunTaskRequest{
    // task is the name of the task to run
    string task=1;
}
