package greeter_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/data_migration/logic/greeter"
)

// 为了保证代码质量和可维护性，请务必编写单元测试
// 这个测试文件作为示例，展示了如何编写基本的单元测试
// 请保证项目中至少有一个测试文件，它可以作为编写其他测试的参考
func TestGreeter(t *testing.T) {
	logic := greeter.NewByParams(nil)
	t.Run("test hello", func(t *testing.T) {
		hello, err := logic.Hello(context.Background())
		assert.Nil(t, err)
		assert.Equal(t, "Hello", hello)
	})
}
