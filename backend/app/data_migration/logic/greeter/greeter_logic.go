package greeter

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Logic struct {
	db db.IDMappingReadWriter
}

func NewLogic() *Logic {
	return &Logic{db: db.NewIDMappingReadWriter()}
}

// NewByParams is used to create a new Logic instance with parameters
// This method is for unit test to mock dependencies
func NewByParams(db db.IDMappingReadWriter) *Logic {
	return &Logic{db: db}
}

// Hello is a test method
func (l *Logic) Hello(ctx context.Context) (string, error) {
	// this is a test
	msg := "Hello"
	// log message
	// use framework logger
	log.DebugContextf(ctx, msg)
	// return Hello
	return msg, nil
}
