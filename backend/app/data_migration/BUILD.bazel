load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "data_migration_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/data_migration",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/data_migration/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/data_migration/v1:data_migration",
    ],
)

go_binary(
    name = "data_migration",
    embed = [":data_migration_lib"],
    visibility = ["//visibility:public"],
)
