package main

import (
	"github.com/MoeGolibrary/moego/backend/app/data_migration/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	dataimportpb "github.com/MoeGolibrary/moego/backend/proto/data_migration/v1"
)

func main() {
	s := rpc.NewServer()

	// 这里需要注册grpc服务
	grpc.Register(s, &dataimportpb.DataImportService_ServiceDesc, service.NewDataImportService())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
