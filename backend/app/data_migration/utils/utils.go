package datamigrationutils

import (
	"strconv"
	"strings"

	moneypb "google.golang.org/genproto/googleapis/type/money"
)

// StringToMoney 将字符串金额（如 "12.34"）转换为 moneypb.Money，假设货币为美元（"USD"）
func StringToMoney(value string) *moneypb.Money {
	value = strings.TrimSpace(value)
	if value == "" {
		return &moneypb.Money{
			CurrencyCode: "USD",
			Units:        0,
			Nanos:        0,
		}
	}

	// 处理负号
	negative := false
	if strings.HasPrefix(value, "-") {
		negative = true
		value = strings.TrimPrefix(value, "-")
	}

	parts := strings.SplitN(value, ".", 2)
	units := int64(0)
	nanos := int32(0)

	// 整数部分
	if len(parts) > 0 && parts[0] != "" {
		u, err := strconv.ParseInt(parts[0], 10, 64)
		if err == nil {
			units = u
		}
	}

	// 小数部分
	if len(parts) == 2 && parts[1] != "" {
		frac := parts[1]
		// 保证最多9位小数
		if len(frac) > 9 {
			frac = frac[:9]
		}
		// 补齐到9位
		frac = frac + strings.Repeat("0", 9-len(frac))
		n, err := strconv.ParseInt(frac, 10, 32)
		if err == nil {
			nanos = int32(n)
		}
	}

	if negative {
		units = -units
		nanos = -nanos
	}

	return &moneypb.Money{
		CurrencyCode: "USD",
		Units:        units,
		Nanos:        nanos,
	}
}
