secrets:
  - name: 'moego/testing/datasource'
    prefix: 'secret.datasource.'

server:
  filter:
    - opentelemetry
    - debuglog
    - recovery
    - validation
  service:
    - name: backend.proto.data_migration.v1.DataImportService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 6000000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 6000000
  service:
    - callee: moego-svc-offering
      target: dns://moego-svc-offering:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-membership
      target: dns://moego-svc-membership:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-server-retail
      target: http://moego-service-retail:9207
      protocol: http
    - callee: postgres.moego_data_migration
      target: dsn://postgresql://${secret.datasource.postgres.moego_data_migration.username}:${secret.datasource.postgres.moego_data_migration.password}@${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_data_migration
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_data_migration
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
