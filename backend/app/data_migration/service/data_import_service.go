package service

import (
	"context"

	dataimport "github.com/MoeGolibrary/moego/backend/app/data_migration/logic/data_import"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/logic/greeter"
	dataimportpb "github.com/MoeGolibrary/moego/backend/proto/data_migration/v1"
)

type DataImportService struct {
	gr *greeter.Logic
	di *dataimport.Logic
	dataimportpb.UnimplementedDataImportServiceServer
}

func NewDataImportService() *DataImportService {
	return &DataImportService{
		gr: greeter.NewLogic(),
		di: dataimport.NewLogic(),
	}
}

func (s *DataImportService) SendPing(ctx context.Context,
	_ *dataimportpb.SendPingRequest) (*dataimportpb.SendPingResponse, error) {
	msg, err := s.gr.Hello(ctx)
	if err != nil {
		return nil, err
	}

	return &dataimportpb.SendPingResponse{Pong: msg}, nil
}

func (s *DataImportService) ImportData(ctx context.Context,
	req *dataimportpb.ImportDataRequest) (*dataimportpb.ImportDataResponse, error) {
	err := s.di.ParseAndRunExcelImport(ctx, req.CompanyId, req.Content)
	if err != nil {
		return nil, err
	}

	return &dataimportpb.ImportDataResponse{}, nil
}
