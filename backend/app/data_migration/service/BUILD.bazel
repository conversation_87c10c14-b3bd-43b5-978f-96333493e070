load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = ["data_import_service.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/data_migration/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/data_migration/logic/data_import",
        "//backend/app/data_migration/logic/greeter",
        "//backend/proto/data_migration/v1:data_migration",
    ],
)
