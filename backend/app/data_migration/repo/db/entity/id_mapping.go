package entity

import (
	"time"

	"gorm.io/gorm"
)

type IDMapping struct {
	ID        int64      `gorm:"column:id;primaryKey"`
	CompanyID int64      `gorm:"column:company_id"`
	Model     string     `gorm:"column:model"`
	MID       string     `gorm:"column:mid"`
	RealID    string     `gorm:"column:real_id"`
	CreatedAt time.Time  `gorm:"column:created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at"`
}

func (*IDMapping) TableName() string {
	return "moego_data_migration.public.id_mapping"
}

type IDMappingQueryOption func(*gorm.DB) *gorm.DB

func (*IDMapping) WithCompanyID(companyID int64) IDMappingQueryOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("company_id = ?", companyID)
	}
}

func (*IDMapping) WithModel(model string) IDMappingQueryOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("model = ?", model)
	}
}

func (*IDMapping) WithMIDs(mids ...string) IDMappingQueryOption {
	if len(mids) == 0 {
		return func(db *gorm.DB) *gorm.DB {
			return db
		}
	}
	if len(mids) == 1 {
		return func(db *gorm.DB) *gorm.DB {
			return db.Where("mid = ?", mids[0])
		}
	}

	return func(db *gorm.DB) *gorm.DB {
		return db.Where("mid in (?)", mids)
	}
}

func (*IDMapping) WithoutDeleted() IDMappingQueryOption {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("deleted_at is null")
	}
}
