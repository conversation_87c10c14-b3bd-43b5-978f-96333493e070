package db

import (
	"sync"

	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

var (
	db   *gorm.DB
	once sync.Once
)

func getDB() *gorm.DB {
	once.Do(func() {
		// 这个名字是自定义的，要和config.yaml中的数据库配置名字一致
		// 得到的 db 是一个原生的 *gorm.DB
		newDB, err := igorm.NewClientProxy("postgres.moego_data_migration")
		if err != nil {
			panic(err)
		}
		db = newDB
	})

	return db
}
