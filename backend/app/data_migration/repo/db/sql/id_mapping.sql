-- postgresql
create table id_mapping (
    id          bigserial primary key,
    company_id  bigint       not null,
    model       varchar(255) not null,
    mid         varchar(255) not null,
    real_id     varchar(255) not null,
    created_at TIMESTAMP WITH TIME ZONE not null DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE not null DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

create unique index uk_id_mapping_cid_model_mid on id_mapping (company_id, model, mid);