package db

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/db/entity"
)

type IDMappingReadWriter interface {
	Get(ctx context.Context, id int64) (*entity.IDMapping, error)
	List(ctx context.Context, opts ...entity.IDMappingQueryOption) ([]*entity.IDMapping, error)
	BatchCreate(ctx context.Context, mappings []*entity.IDMapping) error
}

type idMappingRWImpl struct {
	db *gorm.DB
}

func NewIDMappingReadWriter() IDMappingReadWriter {
	return &idMappingRWImpl{
		db: getDB(),
	}
}

func (i *idMappingRWImpl) Get(ctx context.Context, id int64) (*entity.IDMapping, error) {
	idMapping := &entity.IDMapping{}
	err := i.db.WithContext(ctx).First(idMapping, id).Error

	return idMapping, err
}

func (i *idMappingRWImpl) List(ctx context.Context, opts ...entity.IDMappingQueryOption) (
	[]*entity.IDMapping, error) {
	var idMappings []*entity.IDMapping
	query := i.db.WithContext(ctx)
	for _, opt := range opts {
		query = opt(query)
	}
	err := query.Find(&idMappings).Error

	return idMappings, err
}

func (i *idMappingRWImpl) BatchCreate(ctx context.Context, mappings []*entity.IDMapping) error {
	if len(mappings) == 0 {
		return nil
	}

	return i.db.WithContext(ctx).Clauses(
		clause.OnConflict{
			Columns:   []clause.Column{{Name: "company_id"}, {Name: "model"}, {Name: "mid"}},
			DoUpdates: clause.AssignmentColumns([]string{"real_id", "updated_at"}),
		},
	).Create(&mappings).Error
}
