load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "rpc",
    srcs = [
        "entity.go",
        "rpc.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/data_migration/repo/rpc",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/framework/http",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/membership/v1:membership",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/organization/v1:organization",
    ],
)
