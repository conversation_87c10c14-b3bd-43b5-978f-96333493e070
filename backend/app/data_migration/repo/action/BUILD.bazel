load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "action",
    srcs = [
        "action.go",
        "context.go",
        "membership_action.go",
        "membership_discount_benefit_action.go",
        "membership_quantity_benefit_action.go",
        "subscription_action.go",
        "subscription_entitlement_action.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/data_migration/repo/action",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/data_migration/repo/db",
        "//backend/app/data_migration/repo/db/entity",
        "//backend/app/data_migration/repo/excel",
        "//backend/app/data_migration/repo/rpc",
        "//backend/app/data_migration/utils",
        "//backend/common/rpc/framework/log",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/membership/v1:membership",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/membership/v1:membership",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v1:utils",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "action_test",
    srcs = ["action_test.go"],
    embed = [":action"],
)
