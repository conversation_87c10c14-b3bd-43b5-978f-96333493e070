package action

import (
	"fmt"
	"strconv"

	membershippb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	membershipsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/db/entity"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/excel"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type MembershipDiscountBenefitAction struct {
	ctx                *Context
	DiscountBenefits   []excel.MembershipDiscountBenefit
	MembershipMappings map[string]int64
	ServiceMappings    map[string]int64 // TODO: add on service 需要单独领出来？
	ProductMappings    map[string]int64
}

func NewMembershipDiscountBenefitAction(ctx *Context,
	discountBenefits []excel.MembershipDiscountBenefit) *MembershipDiscountBenefitAction {
	return &MembershipDiscountBenefitAction{
		ctx:                ctx,
		DiscountBenefits:   discountBenefits,
		MembershipMappings: map[string]int64{},
		ServiceMappings:    map[string]int64{},
		ProductMappings:    map[string]int64{},
	}
}

func (a *MembershipDiscountBenefitAction) Prepare() error {
	if len(a.DiscountBenefits) == 0 {
		log.InfoContextf(a.ctx, "MembershipDiscountBenefit action: no data to prepare, skipping")

		return nil
	}
	// load membership mappings
	mappings, err := a.ctx.GetMappingRW().
		List(a.ctx,
			(&entity.IDMapping{}).WithCompanyID(a.ctx.CompanyID),
			(&entity.IDMapping{}).WithModel((&MembershipAction{}).Name()),
			(&entity.IDMapping{}).WithoutDeleted(),
		)
	if err != nil {
		return err
	}

	for _, m := range mappings {
		realID, err := strconv.ParseInt(m.RealID, 10, 64)
		if err != nil {
			return err
		}
		a.MembershipMappings[m.MID] = realID
	}
	// check membership exist
	for _, db := range a.DiscountBenefits {
		if _, ok := a.MembershipMappings[db.MembershipMID]; !ok {
			return fmt.Errorf("membership not found: %s", db.MembershipMID)
		}
	}
	// load service
	resp, err := a.ctx.GetRPC().Offering.GetServiceList(
		a.ctx,
		&offeringsvcpb.GetServiceListRequest{
			TokenCompanyId: a.ctx.CompanyID,
		},
	)
	if err != nil {
		return err
	}

	// build service mappings
	for _, category := range resp.GetCategoryList() {
		for _, service := range category.GetServices() {
			a.ServiceMappings[service.Name] = service.ServiceId
		}
	}

	// check service exist
	for _, db := range a.DiscountBenefits {
		if db.TargetType != membershippb.TargetType_SERVICE && db.TargetType != membershippb.TargetType_ADDON {
			continue
		}
		for _, name := range db.TargetNames {
			if name == "*" {
				continue
			}
			if _, ok := a.ServiceMappings[name]; !ok {
				return fmt.Errorf("service not found: %s", name)
			}
		}
	}

	// load product
	products, err := a.ctx.GetRPC().ListProducts(a.ctx, a.ctx.CompanyID)
	if err != nil {
		return err
	}

	// build product mappings
	for _, product := range products {
		a.ProductMappings[product.Name] = product.ID
	}

	// check product exist
	for _, db := range a.DiscountBenefits {
		if db.TargetType != membershippb.TargetType_PRODUCT {
			continue
		}
		for _, name := range db.TargetNames {
			if name == "*" {
				continue
			}
			if _, ok := a.ProductMappings[name]; !ok {
				return fmt.Errorf("product not found: %s", name)
			}
		}
	}

	return nil
}

func (a *MembershipDiscountBenefitAction) Import() (map[string]string, error) {
	if len(a.DiscountBenefits) == 0 {
		log.InfoContextf(a.ctx, "MembershipDiscountBenefit action: no data to import, skipping")

		return nil, nil
	}
	models := make([]*membershippb.DiscountBenefitData, len(a.DiscountBenefits))
	for i, db := range a.DiscountBenefits {

		// parse target type and ids
		var mappings map[string]int64
		switch db.TargetType {
		case membershippb.TargetType_SERVICE, membershippb.TargetType_ADDON:
			mappings = a.ServiceMappings
		case membershippb.TargetType_PRODUCT:
			mappings = a.ProductMappings
		default:
			return nil, fmt.Errorf("do not support target type: %s", db.TargetType)
		}

		var targetIDs []int64
		isAll := false
		for _, name := range db.TargetNames {
			if name == "*" {
				isAll = true
				// add all ids
				for _, serviceID := range mappings {
					targetIDs = append(targetIDs, serviceID)
				}
			} else {
				targetIDs = append(targetIDs, mappings[name])
			}
		}

		models[i] = &membershippb.DiscountBenefitData{
			Mid:           db.MID,
			MembershipId:  a.MembershipMappings[db.MembershipMID],
			TargetType:    db.TargetType,
			TargetIds:     targetIDs,
			IsAll:         isAll,
			DiscountUnit:  db.DiscountUnit,
			DiscountValue: db.DiscountValue,
		}
	}
	log.InfoContextf(a.ctx, "MembershipDiscountBenefit action: importing %d discount benefits", len(models))
	resp, err := a.ctx.GetRPC().MembershipDM.ImportDiscountBenefits(a.ctx,
		&membershipsvcpb.ImportDiscountBenefitsRequest{
			Data: models,
		})
	if err != nil {
		return nil, err
	}
	log.InfoContextf(a.ctx,
		"MembershipDiscountBenefit action: imported %d discount benefits successfully, %d failed",
		len(resp.GetImported()), len(resp.GetFailed()))

	mappings := map[string]string{}
	for _, m := range resp.GetImported() {
		mappings[m.Mid] = strconv.FormatInt(m.Id, 10)
	}

	for _, m := range resp.GetFailed() {
		log.ErrorContextf(a.ctx, "import membership discount benefit failed, mid: %s", m.Mid)
		// TODO: handle failure
	}

	return mappings, nil
}

func (a *MembershipDiscountBenefitAction) Name() string {
	return "MembershipDiscountBenefit"
}

func (a *MembershipDiscountBenefitAction) DependOn() []string {
	if len(a.DiscountBenefits) == 0 {
		return nil
	}

	return []string{"Membership"}
}
