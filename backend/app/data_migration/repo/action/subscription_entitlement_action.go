package action

import (
	"fmt"
	"strconv"

	membershippb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	membershipsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/db/entity"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/excel"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type SubscriptionEntitlementAction struct {
	ctx                     *Context
	Entitlements            []excel.SubscriptionEntitlement
	SubscriptionMappings    map[string]int64
	QuantityBenefitMappings map[string]int64
}

func NewSubscriptionEntitlementAction(ctx *Context,
	entitlements []excel.SubscriptionEntitlement) *SubscriptionEntitlementAction {
	return &SubscriptionEntitlementAction{
		ctx:                     ctx,
		Entitlements:            entitlements,
		SubscriptionMappings:    make(map[string]int64),
		QuantityBenefitMappings: make(map[string]int64),
	}
}

func (a *SubscriptionEntitlementAction) Prepare() error {
	if len(a.Entitlements) == 0 {
		log.InfoContextf(a.ctx, "SubscriptionEntitlement action: no data to prepare, skipping")

		return nil
	}
	// 加载 Subscription 映射
	subMappings, err := a.ctx.GetMappingRW().List(a.ctx,
		(&entity.IDMapping{}).WithCompanyID(a.ctx.CompanyID),
		(&entity.IDMapping{}).WithModel((&SubscriptionAction{}).Name()),
		(&entity.IDMapping{}).WithoutDeleted(),
	)
	if err != nil {
		return err
	}

	for _, m := range subMappings {
		realID, err := strconv.ParseInt(m.RealID, 10, 64)
		if err != nil {
			return err
		}
		a.SubscriptionMappings[m.MID] = realID
	}

	// 加载 QuantityBenefit 映射
	qbMappings, err := a.ctx.GetMappingRW().List(a.ctx,
		(&entity.IDMapping{}).WithCompanyID(a.ctx.CompanyID),
		(&entity.IDMapping{}).WithModel((&MembershipQuantityBenefitAction{}).Name()),
		(&entity.IDMapping{}).WithoutDeleted(),
	)
	if err != nil {
		return err
	}

	for _, m := range qbMappings {
		realID, err := strconv.ParseInt(m.RealID, 10, 64)
		if err != nil {
			return err
		}
		a.QuantityBenefitMappings[m.MID] = realID
	}

	// 检查所有 Entitlements 的 SubscriptionMID 和 QuantityBenefitMID 是否存在
	for _, e := range a.Entitlements {
		if _, ok := a.SubscriptionMappings[e.SubscriptionMID]; !ok {
			return fmt.Errorf("subscription not found: %s", e.SubscriptionMID)
		}
		if _, ok := a.QuantityBenefitMappings[e.QuantityBenefitMID]; !ok {
			return fmt.Errorf("quantity benefit not found: %s", e.QuantityBenefitMID)
		}
	}

	return nil
}

func (a *SubscriptionEntitlementAction) Import() (map[string]string, error) {
	if len(a.Entitlements) == 0 {
		log.InfoContextf(a.ctx, "SubscriptionEntitlement action: no data to import, skipping")

		return nil, nil
	}
	models := make([]*membershippb.QuantityEntitlementData, len(a.Entitlements))
	for i, e := range a.Entitlements {
		models[i] = &membershippb.QuantityEntitlementData{
			Mid:            e.MID,
			SubscriptionId: a.SubscriptionMappings[e.SubscriptionMID],
			BenefitId:      a.QuantityBenefitMappings[e.QuantityBenefitMID],
			Remaining:      e.Remaining,
			IsLimited:      e.IsLimited,
		}
	}
	log.InfoContextf(a.ctx, "SubscriptionEntitlement action: importing %d discount entitlements", len(models))
	resp, err := a.ctx.GetRPC().MembershipDM.ImportQuantityEntitlements(a.ctx,
		&membershipsvcpb.ImportQuantityEntitlementsRequest{
			Data: models,
		})
	if err != nil {
		return nil, err
	}
	log.InfoContextf(a.ctx,
		"SubscriptionEntitlement action: imported %d discount entitlements successfully, %d failed",
		len(resp.GetImported()), len(resp.GetFailed()))

	mappings := map[string]string{}
	for _, m := range resp.GetImported() {
		mappings[m.Mid] = strconv.FormatInt(m.Id, 10)
	}

	for _, m := range resp.GetFailed() {
		log.ErrorContextf(a.ctx, "import subscription entitlement failed, mid: %s", m.Mid)
		// TODO: handle failure
	}

	return mappings, nil
}

func (a *SubscriptionEntitlementAction) Name() string {
	return "SubscriptionEntitlement"
}

func (a *SubscriptionEntitlementAction) DependOn() []string {
	if len(a.Entitlements) == 0 {
		return nil
	}

	return []string{"Subscription"}
}
