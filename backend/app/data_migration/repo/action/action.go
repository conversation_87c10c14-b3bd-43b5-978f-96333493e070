package action

import "fmt"

type Action interface {
	Prepare() error
	Import() (map[string]string, error)
	Name() string
	DependOn() []string
}

// SortActions 使用 Kahn 算法对 actions 做依赖分析并拓扑排序，按照依赖关系返回有序的 actions
// 算法详情: https://jingsam.github.io/2020/08/11/topological-sort.html
func SortActions(actions []Action) ([]Action, error) {
	actionMap := make(map[string]Action)
	inDegree := make(map[string]int)
	deps := make(map[string][]string)

	for _, act := range actions {
		name := act.Name()
		actionMap[name] = act
		inDegree[name] = 0
	}

	for _, act := range actions {
		for _, dep := range act.DependOn() {
			deps[dep] = append(deps[dep], act.Name())
			inDegree[act.Name()]++
		}
	}

	var queue []string
	for name := range actionMap {
		if inDegree[name] == 0 {
			queue = append(queue, name)
		}
	}

	var order []string
	for len(queue) > 0 {
		n := queue[0]
		queue = queue[1:]
		order = append(order, n)
		for _, m := range deps[n] {
			inDegree[m]--
			if inDegree[m] == 0 {
				queue = append(queue, m)
			}
		}
	}

	if len(order) != len(actions) {
		return nil, fmt.Errorf("cyclic dependency detected in actions")
	}

	sorted := make([]Action, 0, len(order))
	for _, name := range order {
		sorted = append(sorted, actionMap[name])
	}

	return sorted, nil
}
