package action

import (
	"fmt"
	"strconv"

	membershippb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	membershipsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	utilspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/excel"
	datamigrationutils "github.com/MoeGolibrary/moego/backend/app/data_migration/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type MembershipAction struct {
	ctx            *Context
	Memberships    []excel.Membership
	TaxNameMapping map[string]int64
}

func NewMembershipAction(ctx *Context, m []excel.Membership) *MembershipAction {
	return &MembershipAction{
		ctx:            ctx,
		Memberships:    m,
		TaxNameMapping: map[string]int64{},
	}
}

func (a *MembershipAction) Prepare() error {
	if len(a.Memberships) == 0 {
		log.InfoContextf(a.ctx, "Membership action: no data to prepare, skipping")

		return nil
	}
	// query all tax rules
	rules, err := a.ctx.GetRPC().Tax.ListTaxRule(a.ctx, &organizationsvcpb.ListTaxRuleRequest{
		Tenant: &organizationpb.Tenant{
			CompanyId: a.ctx.CompanyID,
		},
	})
	if err != nil {
		return err
	}

	// build mappings
	for _, r := range rules.GetRules() {
		a.TaxNameMapping[r.GetName()] = r.GetId()
	}

	// check tax rules exist
	for _, m := range a.Memberships {
		if _, ok := a.TaxNameMapping[m.TaxName]; !ok {
			return fmt.Errorf("tax not found: %s", m.TaxName)
		}
	}

	return nil
}

func (a *MembershipAction) Import() (map[string]string, error) {
	if len(a.Memberships) == 0 {
		log.InfoContextf(a.ctx, "Membership action: no data to import, skipping")

		return nil, nil
	}
	models := make([]*membershippb.MembershipData, len(a.Memberships))
	for i, m := range a.Memberships {
		models[i] = &membershippb.MembershipData{
			Mid:         m.MID,
			CompanyId:   a.ctx.CompanyID,
			Name:        m.Name,
			Description: m.Description,
			Price:       datamigrationutils.StringToMoney(m.Price),
			TaxId:       a.TaxNameMapping[m.TaxName],
			BillingCyclePeriod: &utilspb.TimePeriod{
				Period: m.BillingCyclePeriodUnit,
				Value:  m.BillingCyclePeriodValue,
			},
			BillingCycleDayOfWeek: m.BillingCycleDayOfWeek,
		}
	}

	log.InfoContextf(a.ctx, "Membership action: importing %d memberships", len(models))
	resp, err := a.ctx.GetRPC().MembershipDM.ImportMemberships(a.ctx, &membershipsvcpb.ImportMembershipsRequest{
		Data: models,
	})
	if err != nil {
		return nil, err
	}
	log.InfoContextf(a.ctx, "Membership action: imported %d memberships successfully, %d failed",
		len(resp.GetImported()), len(resp.GetFailed()))

	mappings := map[string]string{}
	for _, m := range resp.GetImported() {
		mappings[m.Mid] = strconv.FormatInt(m.Id, 10)
	}

	for _, m := range resp.GetFailed() {
		log.ErrorContextf(a.ctx, "import membership failed, mid: %s", m.Mid)
		// TODO: handle failure
	}

	return mappings, nil
}

func (a *MembershipAction) Name() string {
	return "Membership"
}

func (a *MembershipAction) DependOn() []string {
	return nil
}
