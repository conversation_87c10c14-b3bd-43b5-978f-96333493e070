package jira

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

var setupTestSvc sync.Once

func setUp() {
	setupTestSvc.Do(func() {
		currentDir, _ := os.Getwd()
		fmt.Printf("currentDir:%v\n", currentDir)
		currentDir = currentDir[:len(currentDir)-len("repo/jira")]
		rpc.ServerConfigPath = currentDir + "config/local/config.yaml"
		_ = rpc.NewServer()
	})
}

func TestIssueRepositoryIns_GetNewOrUpdatedBugTickets(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	r, err := NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)
	yesterday := time.Now().AddDate(0, 0, -1)
	issues, err := r.GetNewOrUpdatedBugTickets(yesterday, "Bug Report", false)
	assert.Nil(t, err)
	for _, issue := range issues {
		t.Logf("issue: %s", lo.Must(json.Marshal(issue)))
	}
}

func TestIssueRepositoryIns_CloseIssue(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	r, err := NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)
	err = r.CloseIssue("CS-30650")
	assert.Nil(t, err)

}

func TestIssueRepositoryIns_GetIssueDetails(t *testing.T) {
	t.Skip("manual test")
	setUp()
	configloader.InitNacosConfigAndGet(context.Background())
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	r, err := NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)
	issue, err := r.GetIssueDetails("CS-31366")
	assert.Nil(t, err)
	t.Logf("issue: %s", lo.Must(json.Marshal(issue)))
	assert.Equal(t, issue.Key, "CS-31366")
	assert.Equal(t, "Staff(Payroll/Shift/permission)", issue.Components[0])
	assert.Equal(t, "Non-T1 Ticket (General)", issue.T1OrGeneral)
	assert.Equal(t, "P3-Moderate", issue.IssuePriority)
	assert.Equal(t, "No", issue.SLABreach)
	assert.Equal(t, "", issue.CustomerStage)
	assert.Equal(t, "", issue.Description)
	assert.Equal(t, `biz go to shift management > list view > to edit an override, but it is not supported to save the change - <EMAIL>

See video:

!IMG_3706 (1).mov|width=1080,height=1920,alt="IMG_3706 (1).mov"!`, issue.IssueDescription)
	assert.Equal(t, "<EMAIL>", issue.DevEngineer[0].EmailAddress)
	assert.Equal(t, "21 hours", issue.ResolutionTimeCustom)
	assert.Equal(t, "Wayne Wei", issue.CreatedByCustom)
	assert.Equal(t, "ERP", issue.JiraSquad)
}

func TestIssueRepositoryIns_SetAssignee(t *testing.T) {
	t.Skip("manual test: requires real Jira credentials and issue")
	setUp()
	configloader.InitNacosConfigAndGet(context.Background())

	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	r, err := NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)

	// Test with a real issue key and assignee email
	// Replace these with valid values for your test
	issueKey := "IFRBE-2157"           // Replace with a valid issue key
	assigneeEmail := "<EMAIL>" // Replace with a valid assignee email

	err = r.SetAssignee(issueKey, assigneeEmail)
	assert.Nil(t, err)

	// Verify the assignee was set correctly
	issue, err := r.GetIssueDetails(issueKey)
	assert.Nil(t, err)
	assert.Equal(t, assigneeEmail, issue.Assignee.EmailAddress)

	// Test that it DOES overwrite an existing assignee (new behavior)
	// Use a different assignee email
	anotherAssigneeEmail := "<EMAIL>"
	err = r.SetAssignee(issueKey, anotherAssigneeEmail)
	assert.Nil(t, err)

	// The assignee should now be the new one (changed behavior)
	issue, err = r.GetIssueDetails(issueKey)
	assert.Nil(t, err)
	assert.Equal(t, anotherAssigneeEmail, issue.Assignee.EmailAddress) // Should be the new assignee
}

func TestIssueRepositoryIns_SetCauseAndSolution(t *testing.T) {
	t.Skip("manual test: requires real Jira credentials and issue")
	setUp()
	configloader.InitNacosConfigAndGet(context.Background())

	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("jira token:%v", cfg.CsPageWatcher.JiraToken)
	r, err := NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)

	// Test with a real issue key and cause/solution text
	// Replace these with valid values for your test
	issueKey := "CS-32206" // Replace with a valid issue key
	causeAndSolution := "Root cause: Database connection timeout. Solution: Increased connection pool size and added retry logic."

	err = r.SetCauseAndSolution(issueKey, causeAndSolution)
	assert.Nil(t, err)

	// Note: We can't directly verify the custom field value here since it's not part of our Issue struct
	// In a real test, you might want to fetch the raw Jira issue and check the customfield_10084 value
	t.Logf("Successfully set cause and solution for issue %s", issueKey)
}

func TestIssueRepositoryIns_SetFeatureDomains(t *testing.T) {
	t.Skip("manual test: requires real Jira credentials and issue")
	setUp()
	configloader.InitNacosConfigAndGet(context.Background())

	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("jira token:%v", cfg.CsPageWatcher.JiraToken)
	r, err := NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)

	// Test with a real issue key and feature domains text
	// Replace these with valid values for your test
	issueKey := "CS-32344"            // Replace with a valid issue key
	featureDomains := "Uncategorized" // Using the default value we set in the implementation

	err = r.SetFeatureDomains(issueKey, featureDomains)
	assert.Nil(t, err)

	// Note: We can't directly verify the custom field value here since it's not part of our Issue struct
	// In a real test, you might want to fetch the raw Jira issue and check the customfield_11580 value
	t.Logf("Successfully set feature domains for issue %s", issueKey)
	issue, err := r.GetIssueDetails(issueKey)
	assert.Nil(t, err)
	assert.Equal(t, issue.FeatureDomains, "Uncategorized")
}
