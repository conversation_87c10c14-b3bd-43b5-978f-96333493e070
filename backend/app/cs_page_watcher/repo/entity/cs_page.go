package entity

import (
	"time"
)

// CsPage represents the cs_page table in the database.
type CsPage struct {
	CsPageJiraTicket         string    `gorm:"primaryKey;column:cs_page_jira_ticket"`
	DatadogIncidentID        string    `gorm:"column:datadog_incident_id;not null;default:''"`
	IncidentSlackChannelID   string    `gorm:"column:incident_slack_channel_id;not null;default:''"`
	T1NotifyMessageTimestamp string    `gorm:"column:t1_notify_message_timestamp;not null;default:''"`
	CreateTime               time.Time `gorm:"column:create_time;autoCreateTime"`
}

// TableName specifies the table name for GORM.
func (CsPage) TableName() string {
	return "cs_page"
}
