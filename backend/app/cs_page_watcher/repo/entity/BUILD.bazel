load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "entity",
    srcs = [
        "cs_page.go",
        "reader_writer.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/database/gorm",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "entity_test",
    srcs = ["reader_writer_test.go"],
    embed = [":entity"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//assert",
        "@io_gorm_gorm//:gorm",
    ],
)
