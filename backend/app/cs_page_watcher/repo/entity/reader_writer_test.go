package entity

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

var setupTestSvc sync.Once

func setUp() {
	setupTestSvc.Do(func() {
		currentDir, _ := os.Getwd()
		fmt.Printf("currentDir:%v\n", currentDir)
		currentDir = currentDir[:len(currentDir)-len("repo/entity")]
		rpc.ServerConfigPath = currentDir + "config/testing/config.yaml"
		_ = rpc.NewServer()
	})
}

func Test_csPageReaderWriterImpl_GetCsPage1(t *testing.T) {
	t.Skip("manual test")
	setUp()
	db := NewCsPageReaderWriter()
	ticket := "CS-30728"
	ret, err := db.GetCsPage(&CsPage{
		CsPageJiraTicket: ticket,
	})
	if err != nil {
		t.Errorf("err:%v", err)
		return
	}
	t.Logf("ret:%s", lo.Must(json.Marshal(ret)))
}

func Test_csPageReaderWriterImpl_CreateCsPage(t *testing.T) {
	t.Skip("manual test")
	setUp()

	db := NewCsPageReaderWriter()
	ticket := "IFRBE-2054"
	err := db.CreateCsPage(&CsPage{
		CsPageJiraTicket:         ticket,
		DatadogIncidentID:        "110",
		IncidentSlackChannelID:   "110",
		T1NotifyMessageTimestamp: "12345678.12345",
		CreateTime:               time.Now(),
	})
	assert.Nil(t, err)

	page, err := db.GetCsPage(&CsPage{
		CsPageJiraTicket: ticket,
	})
	assert.Nil(t, err)
	t.Logf("page:%s", lo.Must(json.Marshal(page)))
	assert.Equal(t, ticket, page.CsPageJiraTicket)

	err = db.DeleteCsPage(ticket)
	assert.Nil(t, err)

	_, err = db.GetCsPage(&CsPage{
		CsPageJiraTicket: ticket,
	})
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		t.Fail()
	}
	return
}
