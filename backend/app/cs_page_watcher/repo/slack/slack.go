package slack

import (
	"fmt"

	"github.com/slack-go/slack"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Client interface {
	AddMembersToChannel(channelID string, memberEmails []string) error
	AddEmojiToMessage(channelID string, messageTS string, emoji string) error
	Join<PERSON>hannel(channelID string) error
	SendMessage(channelID string, message string) (timestampTS string, err error)
	SendMessageToThread(channelID string, messageTS string, message string) error
	SendMessageTo<PERSON>erson(memberEmail string, message string) (err error)

	LookUpByEmail(emailList []string) (userIDList []string)
}

type slackClient struct {
	api *slack.Client
}

func NewClient(token string) Client {
	return &slackClient{
		api: slack.New(token),
	}
}

func (s *slackClient) AddMembersToChannel(channelID string, memberEmails []string) error {
	userIDList := s.LookUpByEmail(memberEmails)
	if len(userIDList) == 0 {
		return fmt.Errorf("AddMembersToChannel: no user found")
	}
	_, err := s.api.InviteUsersToConversation(channelID, userIDList...)
	if err != nil {
		return fmt.Errorf("AddMembersToChannel: failed to add members to channel %s: %w", channelID, err)
	}

	return nil
}

func (s *slackClient) SendMessageToThread(channelID string, messageTS string, message string) error {
	_, _, err := s.api.PostMessage(
		channelID,
		slack.MsgOptionText(message, false),
		slack.MsgOptionTS(messageTS),
	)
	if err != nil {
		return fmt.Errorf("failed to send message to thread %s in channel %s: %w", messageTS, channelID, err)
	}

	return nil
}

func (s *slackClient) AddEmojiToMessage(channelID string, messageTS string, emoji string) error {
	err := s.api.AddReaction(emoji, slack.ItemRef{
		Channel:   channelID,
		Timestamp: messageTS,
	})
	if err != nil {
		return fmt.Errorf("failed to add emoji %s to message %s in channel %s: %w", emoji, messageTS, channelID, err)
	}

	return nil
}

func (s *slackClient) JoinChannel(channelID string) error {
	_, _, _, err := s.api.JoinConversation(channelID)
	if err != nil {
		return fmt.Errorf("failed to join channel %s: %w", channelID, err)
	}

	return nil
}

func (s *slackClient) LookUpByEmail(emailList []string) (userIDList []string) {
	userIDList = make([]string, 0, len(emailList))
	for _, email := range emailList {
		if email == "" {
			userIDList = append(userIDList, "UNKNOWN_USER")

			continue
		}
		user, err := s.api.GetUserByEmail(email)
		if err != nil {
			log.Errorf("failed to look up user by email %s: %v", email, err)
			userIDList = append(userIDList, "UNKNOWN_USER")

			continue
		}
		userIDList = append(userIDList, user.ID)
	}

	return userIDList
}

func (s *slackClient) SendMessage(channelID string, message string) (timestampTS string, err error) {
	_, timestampTS, err = s.api.PostMessage(channelID, slack.MsgOptionText(message, false))
	if err != nil {
		return "", fmt.Errorf("failed to send message to channel %s: %w", channelID, err)
	}

	return timestampTS, nil
}

func (s *slackClient) SendMessageToPerson(memberEmail string, message string) (err error) {
	userIDList := s.LookUpByEmail([]string{memberEmail})
	if len(userIDList) == 0 {
		return fmt.Errorf("SendMessageToPerson: no user found for email %s", memberEmail)
	}

	// Open a direct message channel with the user
	channel, _, _, err := s.api.OpenConversation(&slack.OpenConversationParameters{
		Users: []string{userIDList[0]},
	})
	if err != nil {
		return fmt.Errorf("SendMessageToPerson: failed to open IM channel with user %s: %w", memberEmail, err)
	}

	_, _, err = s.api.PostMessage(channel.ID, slack.MsgOptionText(message, false))
	if err != nil {
		return fmt.Errorf("failed to send message to person %s: %w", memberEmail, err)
	}

	return nil
}
