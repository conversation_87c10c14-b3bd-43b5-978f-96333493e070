load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "datadog",
    srcs = [
        "client.go",
        "incident.go",
        "incident_triger.go",
        "incident_trigger_strategy.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/datadog",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/cs_page_watcher/configloader",
        "//backend/app/cs_page_watcher/global",
        "//backend/app/cs_page_watcher/repo/entity",
        "//backend/app/cs_page_watcher/repo/jira",
        "//backend/app/cs_page_watcher/repo/slack",
        "//backend/common/rpc/framework/log",
        "@com_github_cenkalti_backoff_v4//:backoff",
        "@com_github_datadog_datadog_api_client_go_v2//api/datadog",
        "@com_github_datadog_datadog_api_client_go_v2//api/datadogV2",
        "@com_github_samber_lo//:lo",
    ],
)

go_test(
    name = "datadog_test",
    srcs = ["incident_triger_test.go"],
    embed = [":datadog"],
    deps = [
        "//backend/app/cs_page_watcher/configloader",
        "//backend/app/cs_page_watcher/global",
        "//backend/app/cs_page_watcher/repo/entity",
        "//backend/app/cs_page_watcher/repo/jira",
        "//backend/app/cs_page_watcher/repo/slack",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "@com_github_stretchr_testify//assert",
    ],
)
