package datadog

import (
	"context"
	"fmt"
	"time"

	backoff "github.com/cenkalti/backoff/v4"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

const (
	// UnknownDevEmail is the default email for unknown dev engineers
	UnknownDevEmail = "unknown"
)

// IncidentTriggerStrategy 策略接口
type IncidentTriggerStrategy interface {
	Trigger(ctx context.Context, incident *Incident) error
}

// FirstTimeTriggerStrategy 首次触发策略
type FirstTimeTriggerStrategy struct {
	impl *IncidentGatewayImpl
}

func (s *FirstTimeTriggerStrategy) Trigger(ctx context.Context, incident *Incident) error {
	var datadogIncidentID int64
	var incidentSlackChannelID string
	var t1NotifyMessageTimestamp string
	var responder = &DutyResponder{}

	ddAPIKey := s.impl.cfg.CsPageWatcher.DatadogAPIKey
	ddAPPKey := s.impl.cfg.CsPageWatcher.DatadogAPPKey

	if incident.NeedCreateIncident {
		var err error
		datadogIncidentID, incidentSlackChannelID, responder, err =
			s.impl.handleIncidentCreation(ctx, incident, ddAPIKey, ddAPPKey)
		if err != nil {
			return err
		}
	}

	devEmail := responder.commanderEmail
	if devEmail == "" {
		devEmail = UnknownDevEmail
		if len(incident.Issue.DevEngineer) > 0 {
			devEmail = incident.Issue.DevEngineer[0].EmailAddress
		}
	}

	if incident.NeedT1Slack {
		var err error
		t1NotifyMessageTimestamp, err = s.impl.handleT1SlackNotification(ctx, incident, devEmail)
		if err != nil {
			log.ErrorContextf(ctx, "TriggerIncident: sendInitMessage2T1Channel failed after retries: %v", err)
			t1NotifyMessageTimestamp = ""
		}
	}

	if incident.NeedBuzzAssignee {
		err := backoff.Retry(func() error {
			return s.impl.slackNotifyAssignee(ctx, incident, devEmail)
		}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
		if err != nil {
			log.ErrorContextf(ctx, "TriggerIncident: slackNotifyAssignee failed after retries: %v", err)
		}
	}

	return s.impl.csPageReaderWriter.CreateOrUpdateCsPage(&entity.CsPage{
		CsPageJiraTicket:         incident.Issue.Key,
		DatadogIncidentID:        fmt.Sprint(datadogIncidentID),
		IncidentSlackChannelID:   incidentSlackChannelID,
		T1NotifyMessageTimestamp: t1NotifyMessageTimestamp,
		CreateTime:               time.Now(),
	})
}

// RepeatTriggerStrategy 重复触发策略
type RepeatTriggerStrategy struct {
	impl   *IncidentGatewayImpl
	dbPage *entity.CsPage
}

func (s *RepeatTriggerStrategy) Trigger(ctx context.Context, incident *Incident) error {
	var datadogIncidentID int64
	var incidentSlackChannelID string
	var t1NotifyMessageTimestamp string
	var responder = &DutyResponder{}

	ddAPIKey := s.impl.cfg.CsPageWatcher.DatadogAPIKey
	ddAPPKey := s.impl.cfg.CsPageWatcher.DatadogAPPKey
	var datadogIncidentIDStr string
	// 仅当 DatadogIncidentID 为空或 "0" 时才创建 incident
	if s.dbPage.DatadogIncidentID == "" || s.dbPage.DatadogIncidentID == "0" {
		var err error
		if incident.NeedCreateIncident {
			datadogIncidentID, incidentSlackChannelID, responder, err =
				s.impl.handleIncidentCreation(ctx, incident, ddAPIKey, ddAPPKey)
			if err != nil {
				return err
			}
			datadogIncidentIDStr = fmt.Sprint(datadogIncidentID)
		}
	} else {
		// 说明incident在之前已经被创建了
		var err error
		datadogIncidentIDStr = s.dbPage.DatadogIncidentID
		incidentSlackChannelID = s.dbPage.IncidentSlackChannelID
		// 重新获取commander
		responder, err = s.impl.getCommanderByTaskType(ddAPIKey, ddAPPKey,
			incident.OncallTeam, global.OncallTaskType)
		if err != nil {
			return err
		}
	}

	devEmail := responder.commanderEmail
	if devEmail == "" {
		devEmail = UnknownDevEmail
		if len(incident.Issue.DevEngineer) > 0 {
			devEmail = incident.Issue.DevEngineer[0].EmailAddress
		}
	}

	// 仅当 T1NotifyMessageTimestamp 为空时才发消息
	if s.dbPage.T1NotifyMessageTimestamp == "" {
		if incident.NeedT1Slack {
			var err error
			t1NotifyMessageTimestamp, err = s.impl.handleT1SlackNotification(ctx, incident, devEmail)
			if err != nil {
				log.ErrorContextf(ctx, "TriggerIncident: sendInitMessage2T1Channel failed after retries: %v", err)
				t1NotifyMessageTimestamp = ""
			}
		}
	} else {
		t1NotifyMessageTimestamp = s.dbPage.T1NotifyMessageTimestamp
	}

	if incident.NeedBuzzAssignee {
		err := backoff.Retry(func() error {
			return s.impl.slackNotifyAssignee(ctx, incident, devEmail)
		}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
		if err != nil {
			log.ErrorContextf(ctx, "TriggerIncident: slackNotifyAssignee failed after retries: %v", err)
		}
	}

	return s.impl.csPageReaderWriter.CreateOrUpdateCsPage(&entity.CsPage{
		CsPageJiraTicket:         incident.Issue.Key,
		DatadogIncidentID:        datadogIncidentIDStr,
		IncidentSlackChannelID:   incidentSlackChannelID,
		T1NotifyMessageTimestamp: t1NotifyMessageTimestamp,
		CreateTime:               time.Now(),
	})
}
