load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "configloader",
    srcs = [
        "configloader.go",
        "nacos.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/config",
        "//backend/common/rpc/framework/log",
        "@in_gopkg_yaml_v3//:yaml_v3",
    ],
)
