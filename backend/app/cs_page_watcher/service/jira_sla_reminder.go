package service

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/jiraslareminder"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ScheduledJiraReminder struct {
	slaReminder          jiraslareminder.SLAReminder
	slackClient          slack.Client
	jiraRepo             jira.IssueRepository
	slaReminderChannelID string
}

func NewScheduledJiraReminder(
	slaReminder jiraslareminder.SLAReminder,
	slackClient slack.Client,
	jiraRepo jira.IssueRepository,
	slaReminderChannelID string,
) *ScheduledJiraReminder {
	return &ScheduledJiraReminder{
		slaReminder:          slaReminder,
		slackClient:          slackClient,
		jiraRepo:             jiraRepo,
		slaReminderChannelID: slaReminderChannelID,
	}
}

func (s *ScheduledJiraReminder) Remind(issueType string, taskName string, isClosed bool, remindBreak bool) {
	log.Infof("Remind: Running scheduled Jira SLA reminder for issue type: %s", issueType)

	issues, err := s.jiraRepo.GetNewOrUpdatedBugTickets(time.Now().AddDate(0, 0, -30), issueType, isClosed)
	if err != nil {
		log.Errorf("Remind: Error fetching Jira tickets: %v", err)

		return
	}

	squadGroups, noSquadGroup := s.remind(issues, remindBreak)

	s.sendSlackReminders(squadGroups, noSquadGroup, taskName)
}

func (s *ScheduledJiraReminder) sendSlackReminders(squadGroups map[string][]string, noSquadGroup []string,
	taskName string) {
	var sortedSquads []string
	for squad := range squadGroups {
		sortedSquads = append(sortedSquads, squad)
	}
	sort.Strings(sortedSquads)

	for _, squad := range sortedSquads {
		messages := squadGroups[squad]
		if len(messages) > 0 {
			channelID, ok := global.SquadSlackChannelMapping[squad]
			if !ok || channelID == "" {
				log.Warnf("Remind: No Slack channel configured for squad %s. Skipping.", squad)

				continue
			}
			fullMessage := s.formatSlackMessage(squad, messages, taskName)
			_, err := s.slackClient.SendMessage(channelID, fullMessage)
			if err != nil {
				log.Errorf("Remind: Error sending Slack message to %s for squad %s: %v", channelID, squad, err)
			} else {
				log.Infof("Remind: Sent SLA reminder to Slack channel %s for squad %s, message:%v",
					channelID, squad, fullMessage)
			}
		}
	}

	if len(noSquadGroup) > 0 {
		if s.slaReminderChannelID == "" {
			log.Warnf(
				"Remind: SLAReminderChannelID is not configured. Cannot send Slack message for no-squad issues.")
		} else {
			fullMessage := s.formatSlackMessage("Other", noSquadGroup, taskName)
			_, err := s.slackClient.SendMessage(s.slaReminderChannelID, fullMessage)
			if err != nil {
				log.Errorf("Remind: Error sending Slack message to %s for no-squad issues: %v",
					s.slaReminderChannelID, err)
			} else {
				log.Infof("Remind: Sent SLA reminder to Slack channel %s for no-squad issues, message:%v",
					s.slaReminderChannelID, fullMessage)
			}
		}
	}
}

func (s *ScheduledJiraReminder) remind(issues []*jira.Issue, remindBreak bool) (
	squadGroups map[string][]string, noSquadGroup []string) {
	results, err := s.slaReminder.EvaluateJiraTickets(issues)
	if err != nil {
		return
	}

	var filteredResults []*jiraslareminder.SLAResult
	if remindBreak {
		filteredResults = s.filterSLABreakResults(results)
	} else {
		filteredResults = s.filterSLAResults(results)
	}

	if len(filteredResults) == 0 {
		log.Infof("Remind: No SLAResults found.")

		return
	}

	email2UserIDMap := s.getEmailToUserIDMap(filteredResults)
	issueMap := lo.KeyBy(issues, func(issue *jira.Issue) string {
		return issue.Key
	})

	return s.groupMessagesBySquad(filteredResults, issueMap, email2UserIDMap)
}

func (s *ScheduledJiraReminder) filterSLAResults(results []*jiraslareminder.SLAResult) []*jiraslareminder.SLAResult {
	var filteredResults []*jiraslareminder.SLAResult
	for _, res := range results {
		if !res.IsSLABreached && res.TimeUntilBreach > 0 && res.TimeUntilBreach <= 24*time.Hour {
			filteredResults = append(filteredResults, res)
		}
	}

	return filteredResults
}

func (s *ScheduledJiraReminder) filterSLABreakResults(
	results []*jiraslareminder.SLAResult) []*jiraslareminder.SLAResult {
	var filteredResults []*jiraslareminder.SLAResult
	for _, res := range results {
		if res.IsSLABreached || res.TimeUntilBreach < 0 {
			filteredResults = append(filteredResults, res)
		}
	}

	return filteredResults
}

func (s *ScheduledJiraReminder) getEmailToUserIDMap(results []*jiraslareminder.SLAResult) map[string]string {
	var emails []string
	emailSet := make(map[string]struct{})
	for _, res := range results {
		if res.DevEngineerEmail != "" {
			if _, ok := emailSet[res.DevEngineerEmail]; !ok {
				emails = append(emails, res.DevEngineerEmail)
				emailSet[res.DevEngineerEmail] = struct{}{}
			}
		}
		if res.AssigneeEmail != "" {
			if _, ok := emailSet[res.AssigneeEmail]; !ok {
				emails = append(emails, res.AssigneeEmail)
				emailSet[res.AssigneeEmail] = struct{}{}
			}
		}
	}

	userIDList := s.slackClient.LookUpByEmail(emails)
	email2UserIDMap := make(map[string]string)
	for i, email := range emails {
		if i < len(userIDList) {
			email2UserIDMap[email] = userIDList[i]
		}
	}

	return email2UserIDMap
}

func (s *ScheduledJiraReminder) groupMessagesBySquad(results []*jiraslareminder.SLAResult,
	issueMap map[string]*jira.Issue,
	email2UserIDMap map[string]string) (map[string][]string, []string) {
	squadGroups := make(map[string][]string)
	var noSquadGroup []string

	for _, slaResult := range results {
		issue, ok := issueMap[slaResult.JiraKey]
		if !ok {
			continue
		}

		jiraURL := fmt.Sprintf("https://moego.atlassian.net/browse/%s", slaResult.JiraKey)
		msg := fmt.Sprintf("<%s|%s> [%s] %s (Expires in: %s)",
			jiraURL, slaResult.JiraKey, issue.IssuePriority, issue.Summary, formatDuration(slaResult.TimeUntilBreach))

		if slaResult.DevEngineerEmail != "" {
			if userID, ok := email2UserIDMap[slaResult.DevEngineerEmail]; ok && userID != "" {
				msg += fmt.Sprintf(" <@%s>", userID)
			}
		}

		if slaResult.AssigneeEmail != "" {
			if userID, ok := email2UserIDMap[slaResult.AssigneeEmail]; ok && userID != "" {
				msg += fmt.Sprintf(" <@%s>", userID)
			}
		}

		if issue.JiraSquad != "" {
			squadGroups[issue.JiraSquad] = append(squadGroups[issue.JiraSquad], msg)
		} else {
			noSquadGroup = append(noSquadGroup, msg)
		}
	}

	return squadGroups, noSquadGroup
}

func (s *ScheduledJiraReminder) formatSlackMessage(squad string, messages []string, taskName string) string {
	var formattedMessages []string
	if taskName != "" {
		formattedMessages = append(formattedMessages, fmt.Sprintf("*Task: %s*", taskName))
	}
	if squad != "" {
		formattedMessages = append(formattedMessages, fmt.Sprintf("--- %s ---", squad))
	}
	formattedMessages = append(formattedMessages, messages...)

	return strings.Join(formattedMessages, "\n")
}

func formatDuration(d time.Duration) string {
	hours := int(d.Hours())
	minutes := int(d.Minutes()) % 60

	return fmt.Sprintf("%dh %dm", hours, minutes)
}
