package global

import (
	"context"
	"fmt"
	"os"
	"sync"
	"testing"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

var setupTestSvc sync.Once

func setUp() {
	setupTestSvc.Do(func() {
		currentDir, _ := os.Getwd()
		fmt.Printf("currentDir:%v\n", currentDir)
		currentDir = currentDir[:len(currentDir)-len("global")]
		rpc.ServerConfigPath = currentDir + "config/local/config.yaml"
		_ = rpc.NewServer()
	})
}

func TestGetTeamFromComponents_Manual(t *testing.T) {
	t.Skip("This is a manual test case. To run it, use the command: go test -v -run ^TestGetTeamFromComponents_Manual$")

	// 1. Mock the mapping for the test.
	// mockMapping := map[string]string{
	// 	"Payments, Finance & QuickBooks":          "Fintech",
	// 	"Client/pet/leads":                        "CRM",
	// 	"Mobile Grooming(SS/CACD/Map view)":       "ERP",
	// 	"Platform(Mobile App performance/System)": "BE-Platform",
	// 	"System notification":                     "FE-Platform",
	// }
	setUp()
	configloader.InitNacosConfigAndGet(context.Background())
	mockMapping := configloader.GlobalNacosCsPageConfig.Load().ComponentsSquadsMapping

	// 2. Define the test cases for manual verification.
	testCases := []struct {
		name         string
		components   []string
		issueKey     string
		expectedTeam string
	}{
		{
			name:         "Valid Fintech Component",
			components:   []string{"Payments, Finance & QuickBooks"},
			issueKey:     "JIRA-101",
			expectedTeam: "Fintech",
		},
		{
			name:         "Valid CRM Component",
			components:   []string{"Client/pet/leads"},
			issueKey:     "JIRA-102",
			expectedTeam: "CRM",
		},
		{
			name:         "Valid ERP Component",
			components:   []string{"Mobile Grooming(SS/CACD/Map view)"},
			issueKey:     "JIRA-103",
			expectedTeam: "ERP",
		},
		{
			name:         "Valid BE-Platform Component",
			components:   []string{"Platform(Mobile App performance/System)"},
			issueKey:     "JIRA-104",
			expectedTeam: "BE-Platform",
		},
		{
			name:         "Valid FE-Platform Component",
			components:   []string{"System notification"},
			issueKey:     "JIRA-105",
			expectedTeam: "FE-Platform",
		},
		{
			name:         "Component not in map",
			components:   []string{"An unknown component"},
			issueKey:     "JIRA-106",
			expectedTeam: "",
		},
		{
			name:         "No components provided",
			components:   []string{},
			issueKey:     "JIRA-107",
			expectedTeam: "",
		},
	}

	// 3. Execute the test cases and print the results for manual review.
	fmt.Println("\n--- Running Manual Test for GetTeamFromComponents ---")
	for _, tc := range testCases {
		actualTeam := GetTeamFromComponents(tc.components, tc.issueKey, mockMapping)

		fmt.Printf("\n--- Test Case: %s ---\n", tc.name)
		fmt.Printf("Input Components: %v\n", tc.components)
		fmt.Printf("Expected Team:    '%s'\n", tc.expectedTeam)
		fmt.Printf("Actual Team:      '%s'\n", actualTeam)

		if tc.expectedTeam == actualTeam {
			fmt.Println("Result:           PASSED")
		} else {
			fmt.Println("Result:           FAILED")
		}
	}
	fmt.Println("\n--- Manual Test Finished ---")
}
