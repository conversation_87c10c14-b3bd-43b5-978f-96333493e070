load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "jiraslareminder",
    srcs = ["jirasla_reminder.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/jiraslareminder",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/cs_page_watcher/global",
        "//backend/app/cs_page_watcher/repo/jira",
    ],
)

go_test(
    name = "jiraslareminder_test",
    srcs = ["jirasla_reminder_test.go"],
    embed = [":jiraslareminder"],
    deps = [
        "//backend/app/cs_page_watcher/global",
        "//backend/app/cs_page_watcher/repo/jira",
        "@com_github_stretchr_testify//assert",
    ],
)
