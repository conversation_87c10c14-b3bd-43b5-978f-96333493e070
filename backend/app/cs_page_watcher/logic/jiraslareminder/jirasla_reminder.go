package jiraslareminder

import (
	"time"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
)

// SLAReminder defines the interface for SLA evaluation.
type SLAReminder interface {
	EvaluateJiraTickets(tickets []*jira.Issue) ([]*SLAResult, error)
}

// SLAResult represents the SLA evaluation result for a single Jira ticket.
type SLAResult struct {
	JiraKey          string
	IsSLABreached    bool
	TimeUntilBreach  time.Duration
	DevEngineerEmail string
	AssigneeEmail    string
}

type slaReminder struct{}

func NewSLAReminder() SLAReminder {
	return &slaReminder{}
}

func (s *slaReminder) EvaluateJiraTickets(tickets []*jira.Issue) ([]*SLAResult, error) {
	var results []*SLAResult

	for _, ticket := range tickets {
		sla, err := getSLAByPriority(ticket.IssuePriority)
		if err != nil {
			return nil, err
		}

		now := time.Now()
		expectedResolutionTime := ticket.Created.Add(sla)

		isSLABreached := now.After(expectedResolutionTime)
		timeUntilBreach := expectedResolutionTime.Sub(now)

		var devEmail string
		if len(ticket.DevEngineer) > 0 {
			devEmail = ticket.DevEngineer[0].EmailAddress
		}

		results = append(results, &SLAResult{
			JiraKey:          ticket.Key,
			IsSLABreached:    isSLABreached,
			TimeUntilBreach:  timeUntilBreach,
			DevEngineerEmail: devEmail,
			AssigneeEmail:    ticket.Assignee.EmailAddress,
		})
	}

	return results, nil
}

func getSLAByPriority(priority string) (time.Duration, error) {
	switch priority {
	case global.PriorityP0:
		return time.Hour * 3, nil
	case global.PriorityP1:
		return time.Hour * 24, nil
	case global.PriorityP2:
		return time.Hour * 72, nil
	case global.PriorityP3:
		return time.Hour * 168, nil
	case global.PriorityP4:
		return time.Hour * 360, nil
	case global.PriorityP5:
		return time.Hour * 720, nil
	default:
		return 0, nil
	}
}
