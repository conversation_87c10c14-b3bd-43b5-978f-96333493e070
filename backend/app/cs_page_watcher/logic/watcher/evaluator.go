package watcher

import (
	"fmt"
	"sync/atomic"
	"time"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/datadog"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// dedicateOnVacation 是一个开关，当设置为 true 时，将跳过 Dedicate 团队并直接使用 Components 对应的 Oncall 团队
// 在生产环境中设置为 true，表示 Dedicate 团队正在休假
var dedicateOnVacation = true

// IncidentEvaluator 定义了评估一个Jira Bug是否符合告警条件的核心业务逻辑。
type IncidentEvaluator interface {
	// EvaluateBug 判断一个Jira Bug是否应该触发告警。
	// 返回一个布尔值表示是否需要告警，以及一个表示告警内容的Incident对象（如果需要）。
	EvaluateBug(bug *jira.Issue) (incident *datadog.Incident, err error)
}

func NewJiraIncidentEvaluator(csPageConfig *atomic.Pointer[configloader.CSPageConfig]) IncidentEvaluator {
	e := new(JiraIncidentEvaluator)
	e.timeGetter = time.Now
	e.csPageConfig = csPageConfig

	return e
}

type JiraIncidentEvaluator struct {
	timeGetter   func() time.Time
	csPageConfig *atomic.Pointer[configloader.CSPageConfig]
}

// EvaluateBug 如果incident的返回值是nil，则不会再有下一步的动作
func (j *JiraIncidentEvaluator) EvaluateBug(issue *jira.Issue) (retIncident *datadog.Incident, err error) {
	switch issue.IssuePriority {
	case global.PriorityP0:
		retIncident, err = j.evaluateP0Incident(issue)
	case global.PriorityP1:
		retIncident, err = j.evaluateP1Incident(issue)
	default:
		// p234以及unknown的都走这个分支
		retIncident, err = j.evaluateP234Incident(issue)
	}
	if err == nil && retIncident != nil {
		refUsers := append(j.csPageConfig.Load().RefUser, issue.Assignee.EmailAddress, issue.Reporter.EmailAddress)
		retIncident.RefUsers = refUsers

		// 同时，将Create by添加到refUsers
		createdByEmail, ok := global.MoegoContacts[issue.CreatedByCustom]
		if ok {
			retIncident.RefUsers = append(retIncident.RefUsers, createdByEmail)
		} else {
			log.Warnf("EvaluateBug: no create by found for issue %v, %v", issue.Key, issue.CreatedByCustom)
		}
	}

	return
}

func (j *JiraIncidentEvaluator) evaluateP0Incident(issue *jira.Issue) (retIncident *datadog.Incident, err error) {
	switch issue.T1OrGeneral {
	case global.TierT1:
		// 所有p0 t1 的流程NeedCreateIncident, NeedT1Slack, 但不BuzzAssignee，因为Assignee已经在群里了
		team, err := j.evaluateDedicateOrOncall(issue, j.timeGetter())
		if err != nil {
			return nil, err
		}
		retIncident = &datadog.Incident{
			OncallTeam:         team,
			IssuePriority:      issue.IssuePriority,
			Tier:               issue.T1OrGeneral,
			CustomerStage:      issue.CustomerStage,
			NeedCreateIncident: true,
			NeedT1Slack:        true,
			NeedBuzzAssignee:   false,
			Issue:              issue,
		}
	default: // This corresponds to incident.TierOther
		oncallTeam, err := j.evaluateDedicateOrOncall(issue, j.timeGetter())
		if err != nil {
			return nil, err
		}
		// The evaluateDedicateOrOncall function already
		// handles the case where no team is found by returning an error.
		// If err is nil here, it means oncallTeam has been successfully determined.

		retIncident = &datadog.Incident{
			OncallTeam:         oncallTeam,
			IssuePriority:      issue.IssuePriority,
			Tier:               global.TierOther,
			CustomerStage:      issue.CustomerStage,
			NeedCreateIncident: true,
			NeedT1Slack:        false,
			Issue:              issue,
		}
	}

	return
}

func (j *JiraIncidentEvaluator) evaluateP234Incident(issue *jira.Issue) (retIncident *datadog.Incident, err error) {
	switch issue.T1OrGeneral {
	case global.TierT1:
		// t1 p234的ticket，不管是golive或其它阶段，都只在t1 bug tracker channel登记，也不BuzzAssignee
		retIncident = &datadog.Incident{
			OncallTeam:         global.TeamDedicate,
			IssuePriority:      issue.IssuePriority,
			Tier:               issue.T1OrGeneral,
			CustomerStage:      issue.CustomerStage,
			NeedCreateIncident: false,
			NeedT1Slack:        true,
			NeedBuzzAssignee:   false,
			Issue:              issue,
		}
	default: //不是t1的用户暂时不处理
	}

	return
}

// evaluateDedicateOrOncall 北京时间凌晨12点-下午2点 返回Dedicate，其它时间返回OncallTeam
// 当 dedicateOnVacation 开关为 true 时，将跳过 Dedicate 团队并直接使用 Components 对应的 Oncall 团队
func (j *JiraIncidentEvaluator) evaluateDedicateOrOncall(issue *jira.Issue, now time.Time) (team string, err error) {
	// Get current time in Beijing timezone
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Errorf("Failed to load location Asia/Shanghai: %v", err)

		return "", err
	}

	// Use the provided 'now' parameter, adjusted to Beijing timezone
	currentTimeInBeijing := now.In(loc)
	hour := currentTimeInBeijing.Hour()

	// 如果 dedicateOnVacation 为 true，则跳过 Dedicate 团队，直接使用 Components 对应的 Oncall 团队
	// 否则，北京时间 12 AM (0:00) - 2 PM (14:00) 是 Dedicate，其他时间是 Oncall
	if !dedicateOnVacation && hour >= 0 && hour < 14 {
		return global.TeamDedicate, nil
	}

	oncallTeam := global.GetTeamFromComponents(issue.Components, issue.Key,
		j.csPageConfig.Load().ComponentsSquadsMapping)
	if oncallTeam == "" {
		err = fmt.Errorf("no team found for issue %v", issue.Components)

		return
	}

	return oncallTeam, nil
}

func (j *JiraIncidentEvaluator) evaluateP1Incident(issue *jira.Issue) (retIncident *datadog.Incident, err error) {
	switch issue.T1OrGeneral {
	case global.TierT1:
		// P1 T1 不创建incident，只BuzzAssignee
		retIncident = &datadog.Incident{
			OncallTeam:         global.TeamDedicate, // 默认TeamDedicate，这个字段在不创建incident的时候无用
			IssuePriority:      issue.IssuePriority,
			Tier:               issue.T1OrGeneral,
			CustomerStage:      issue.CustomerStage,
			NeedCreateIncident: false,
			NeedT1Slack:        true,
			NeedBuzzAssignee:   true,
			Issue:              issue,
		}
	}

	return
}
