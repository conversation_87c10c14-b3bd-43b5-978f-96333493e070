package watcher

import (
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
)

// TestNewJiraIncidentEvaluator 测试JiraIncidentEvaluator的构造函数
func TestNewJiraIncidentEvaluator(t *testing.T) {
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	evaluator := NewJiraIncidentEvaluator(&mockCfg)
	assert.NotNil(t, evaluator)
}

// TestJiraIncidentEvaluator_EvaluateBug_P234 测试评估P2, P3, P4优先级的bug
func TestJiraIncidentEvaluator_EvaluateBug_P234(t *testing.T) {
	mockConfig := &configloader.CSPageConfig{
		ComponentsSquadsMapping: map[string]string{
			"Workflow": global.SquadCRM,
		},
		RefUser: []string{"<EMAIL>"},
	}
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	mockCfg.Store(mockConfig)

	evaluator := &JiraIncidentEvaluator{
		timeGetter:   time.Now,
		csPageConfig: &mockCfg,
	}

	// 测试P2 T1的情况
	p2Issue := &jira.Issue{
		IssuePriority: global.PriorityP2,
		T1OrGeneral:   global.TierT1,
		Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
		Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
	}
	incident, err := evaluator.EvaluateBug(p2Issue)
	assert.NoError(t, err)
	assert.NotNil(t, incident)
	assert.Equal(t, false, incident.NeedCreateIncident)
	assert.Equal(t, true, incident.NeedT1Slack)
	assert.Equal(t, false, incident.NeedBuzzAssignee)

	// 测试P3 General的情况 (预期不会有任何incident)
	p3Issue := &jira.Issue{
		IssuePriority: global.PriorityP3,
		T1OrGeneral:   global.TierOther,
		Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
		Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
	}
	incident, err = evaluator.EvaluateBug(p3Issue)
	assert.NoError(t, err)
	assert.Nil(t, incident)
}

// TestJiraIncidentEvaluator_EvaluateBug_CreatedBy 测试评估带有CreatedBy字段的bug
func TestJiraIncidentEvaluator_EvaluateBug_CreatedBy(t *testing.T) {
	mockConfig := &configloader.CSPageConfig{
		RefUser: []string{"<EMAIL>"},
	}
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	mockCfg.Store(mockConfig)
	global.MoegoContacts["testuser"] = "<EMAIL>"

	evaluator := &JiraIncidentEvaluator{
		timeGetter:   time.Now,
		csPageConfig: &mockCfg,
	}

	issue := &jira.Issue{
		IssuePriority:   global.PriorityP1,
		T1OrGeneral:     global.TierT1,
		Assignee:        jira.UserInfo{EmailAddress: "<EMAIL>"},
		Reporter:        jira.UserInfo{EmailAddress: "<EMAIL>"},
		CreatedByCustom: "testuser",
	}

	incident, err := evaluator.EvaluateBug(issue)
	assert.NoError(t, err)
	assert.NotNil(t, incident)
	assert.Contains(t, incident.RefUsers, "<EMAIL>")
}

// TestJiraIncidentEvaluator_evaluateP0Incident_OtherTier 测试评估P0, 非T1的incident
func TestJiraIncidentEvaluator_evaluateP0Incident_OtherTier(t *testing.T) {
	mockConfig := &configloader.CSPageConfig{
		ComponentsSquadsMapping: map[string]string{
			"Workflow": global.SquadCRM,
		},
	}
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	mockCfg.Store(mockConfig)

	evaluator := &JiraIncidentEvaluator{
		timeGetter:   time.Now,
		csPageConfig: &mockCfg,
	}

	issue := &jira.Issue{
		IssuePriority: global.PriorityP0,
		T1OrGeneral:   global.TierOther,
		Components:    []string{"Workflow"},
		Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
		Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
	}

	incident, err := evaluator.evaluateP0Incident(issue)
	assert.NoError(t, err)
	assert.NotNil(t, incident)
	assert.Equal(t, true, incident.NeedCreateIncident)
	assert.Equal(t, false, incident.NeedT1Slack)
}
