package pkg

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestTryLockTimeout(t *testing.T) {
	locker := NewLocalLocker()

	// Test 1: Acquire lock successfully
	getLock, err := locker.TryLock(context.Background(), "key1", time.Millisecond*100)
	assert.<PERSON>l(t, err)
	assert.True(t, getLock, "Expected to acquire lock for key1")

	// Test 2: Try to acquire the same lock immediately, should fail
	getLock2, err := locker.TryLock(context.Background(), "key1", time.Millisecond*100)
	assert.Nil(t, err)
	assert.False(t, getLock2, "Expected to fail acquiring lock for key1 (already held)")

	// Test 3: Wait for lock to expire, then acquire successfully
	time.Sleep(time.Millisecond * 150) // Sleep longer than the timeout
	getLock3, err := locker.TryLock(context.Background(), "key1", time.Millisecond*100)
	assert.<PERSON><PERSON>(t, err)
	assert.True(t, getLock3, "Expected to acquire lock for key1 after timeout")
}

func TestTryLockAndManualUnlock(t *testing.T) {
	locker := NewLocalLocker()
	key := "key2"
	timeout := time.Second // A long timeout to ensure manual unlock happens first

	// Acquire lock successfully
	acquired, err := locker.TryLock(context.Background(), key, timeout)
	assert.Nil(t, err)
	assert.True(t, acquired, "Expected to acquire lock for key2")

	// Try to acquire again, should fail
	acquired2, err := locker.TryLock(context.Background(), key, timeout)
	assert.Nil(t, err)
	assert.False(t, acquired2, "Expected to fail acquiring lock for key2 (already held)")

	// Manually unlock
	err = locker.Unlock(key)
	assert.Nil(t, err, "Expected no error on manual unlock")

	// Try to acquire after manual unlock, should succeed
	acquired3, err := locker.TryLock(context.Background(), key, timeout)
	assert.Nil(t, err)
	assert.True(t, acquired3, "Expected to acquire lock for key2 after manual unlock")
}

func TestTryLockContextCancellation(t *testing.T) {
	locker := NewLocalLocker()
	key := "key3"
	timeout := time.Second * 5 // Long timeout

	ctx, cancel := context.WithCancel(context.Background())

	// Acquire lock successfully
	acquired, err := locker.TryLock(ctx, key, timeout)
	assert.Nil(t, err)
	assert.True(t, acquired, "Expected to acquire lock for key3")

	// Try to acquire again, should fail
	acquired2, err := locker.TryLock(context.Background(), key, timeout)
	assert.Nil(t, err)
	assert.False(t, acquired2, "Expected to fail acquiring lock for key3 (already held)")

	// Cancel the context, which should release the lock
	cancel()
	time.Sleep(time.Millisecond * 50) // Give time for the goroutine to process cancellation

	// Try to acquire after context cancellation, should succeed
	acquired3, err := locker.TryLock(context.Background(), key, timeout)
	assert.Nil(t, err)
	assert.True(t, acquired3, "Expected to acquire lock for key3 after context cancellation")
}

func TestUnlockMultipleTimes(t *testing.T) {
	locker := NewLocalLocker()
	key := "key4"
	timeout := time.Second // Long timeout

	// Acquire lock
	acquired, err := locker.TryLock(context.Background(), key, timeout)
	assert.Nil(t, err)
	assert.True(t, acquired, "Expected to acquire lock for key4")

	// Unlock once
	err = locker.Unlock(key)
	assert.Nil(t, err, "Expected no error on first unlock")

	// Unlock again, should not panic and return nil error
	err = locker.Unlock(key)
	assert.Nil(t, err, "Expected no error on second unlock (safe)")

	// Try to acquire after multiple unlocks, should succeed
	acquired2, err := locker.TryLock(context.Background(), key, timeout)
	assert.Nil(t, err)
	assert.True(t, acquired2, "Expected to acquire lock for key4 after multiple unlocks")
}

func TestUnlockNonExistentLock(t *testing.T) {
	locker := NewLocalLocker()
	key := "nonexistent_key"

	// Try to unlock a key that was never locked
	err := locker.Unlock(key)
	assert.Nil(t, err, "Expected no error when unlocking a non-existent key")

	// Try to acquire it, should succeed
	acquired, err := locker.TryLock(context.Background(), key, time.Second)
	assert.Nil(t, err)
	assert.True(t, acquired, "Expected to acquire lock for nonexistent_key")
}

func TestConcurrentTryLock(t *testing.T) {
	locker := NewLocalLocker()
	key := "key5"
	timeout := time.Millisecond * 100

	var wg sync.WaitGroup
	successCount := 0
	numGoroutines := 100

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			acquired, err := locker.TryLock(context.Background(), key, timeout)
			assert.Nil(t, err)
			if acquired {
				successCount++
				// Simulate work while holding the lock
				time.Sleep(time.Millisecond * 10)
				// Unlock after work, or let it auto-expire
				// For this test, we rely on auto-expiration for simplicity
			}
		}()
	}

	wg.Wait()
	// Only one goroutine should have successfully acquired the lock at any given time
	// and held it until it expired or was released.
	// Since we have a timeout, multiple goroutines might acquire the lock sequentially
	// as previous ones expire.
	// Let's adjust the expectation: at least one should succeed, and if the timeout is short,
	// more than one might succeed over time, but never concurrently.
	// The test should verify that only one goroutine holds the lock at any instant.
	// For this setup, we expect `successCount` to be greater than 0, and ideally,
	// if the timeout is short enough and goroutines are fast, it should be close to `numGoroutines`
	// if they acquire sequentially.
	// A better test for true concurrency would be to check if `TryLock` returns false for others.

	// Let's refine this: we want to ensure only ONE goroutine gets the lock *initially*
	// if they all try at the same time.
	// The current `TestTryLockTimeout` already covers this for two attempts.
	// For `TestConcurrentTryLock`, we want to see how many *eventually* get it over time
	// due to auto-expiration.

	// Re-thinking TestConcurrentTryLock:
	// The goal is to ensure that only one goroutine can hold the lock at any given moment.
	// If multiple goroutines try to acquire the lock simultaneously, only one should succeed.
	// The others should fail (return false).
	// The `timeout` here is the lease duration.

	// Let's make this test more precise:
	// We'll have a short timeout. We expect only one initial success.
	// Then, after the timeout, another one can succeed.
	// This test should verify that `TryLock` correctly returns `false` for concurrent attempts.

	locker = NewLocalLocker() // Reset locker for this test
	key = "key6"
	timeout = time.Millisecond * 50 // Short timeout

	acquiredLocks := make(chan bool, numGoroutines)
	var initialWg sync.WaitGroup

	for i := 0; i < numGoroutines; i++ {
		initialWg.Add(1)
		go func() {
			defer initialWg.Done()
			acquired, err := locker.TryLock(context.Background(), key, timeout)
			assert.Nil(t, err)
			acquiredLocks <- acquired
		}()
	}
	initialWg.Wait()
	close(acquiredLocks)

	successfulInitialAcquisitions := 0
	for acquired := range acquiredLocks {
		if acquired {
			successfulInitialAcquisitions++
		}
	}
	// In a truly concurrent scenario, only one should acquire the lock initially.
	// Others should fail.
	assert.Equal(t, 1, successfulInitialAcquisitions, "Expected only one goroutine to initially acquire the lock")

	// Now, wait for the lock to expire and try again to ensure it's available.
	time.Sleep(timeout + time.Millisecond*10) // Wait for the lock to expire

	acquiredAfterExpiry, err := locker.TryLock(context.Background(), key, timeout)
	assert.Nil(t, err)
	assert.True(t, acquiredAfterExpiry, "Expected to acquire lock after expiry")
}
