package pkg

import (
	"context"
	"sync"
	"sync/atomic"
	"time"
)

// Locker defines the interface for a distributed lock.
type Locker interface {
	// TryLock attempts to acquire a lock for the given key without blocking.
	// If successful, the lock is acquired and will be automatically released after the specified timeout.
	// Returns true if the lock was successfully acquired, false otherwise, and an error if any.
	TryLock(ctx context.Context, key string, timeout time.Duration) (bool, error)

	// Unlock releases the lock for the given key.
	// Returns an error if the lock could not be released (e.g., not owned by caller, lock does not exist).
	Unlock(key string) error
}

// lockState represents the state of a lock entry, including its mutex and a mechanism for auto-release.
type lockState struct {
	mu     sync.Mutex
	locked atomic.Bool        // Tracks if the logical lock is currently held
	cancel context.CancelFunc // Function to cancel the auto-release goroutine
}

// LocalLocker implements a pseudo-distributed lock using in-memory sync.Map.
// This is suitable for single-instance applications that need to simulate distributed lock behavior
// for concurrency control within the same process.
// It assumes Go 1.18+ for sync.Mutex.TryLock().
type LocalLocker struct {
	locks sync.Map // map[string]*lockState
}

// NewLocalLocker creates and returns a new instance of LocalLocker.
func NewLocalLocker() *LocalLocker {
	return &LocalLocker{}
}

// TryLock attempts to acquire a lock for the given key without blocking.
// If the lock is successfully acquired, it will be automatically released after the `timeout` duration,
// or if the provided `context` is cancelled.
func (l *LocalLocker) TryLock(ctx context.Context, key string, timeout time.Duration) (bool, error) {
	state, _ := l.locks.LoadOrStore(key, &lockState{})
	ls, _ := state.(*lockState)

	// Attempt to acquire the underlying mutex non-blocking.
	if !ls.mu.TryLock() {
		return false, nil // Mutex is already held by another goroutine.
	}

	// Mutex acquired. Set the logical lock state to true.
	ls.locked.Store(true)

	// Lock acquired and state set to locked. Set up auto-release.
	autoReleaseCtx, cancel := context.WithCancel(ctx)
	ls.cancel = cancel // Store cancel function to allow manual Unlock to stop auto-release

	go func() {
		defer func() {
			// Ensure mutex is unlocked and logical state is reset when this goroutine exits.
			// This defer will run even if the context is cancelled or timeout occurs.
			if ls.locked.CompareAndSwap(true, false) { // Only unlock if we were the ones holding it logically
				ls.mu.Unlock() // Safely unlock the underlying mutex
			}
			// Note: We don't delete from sync.Map here. The mutex remains for potential reuse.
		}()

		select {
		case <-autoReleaseCtx.Done():
			// Auto-release context cancelled (e.g., by manual Unlock or external cancellation).
			// The defer will handle unlocking.
		case <-time.After(timeout):
			// Timeout expired, lock should be released.
			// The defer will handle unlocking.
		}
	}()

	return true, nil
}

// Unlock releases the lock for the given key.
// It attempts to safely unlock the mutex and reset its state.
// This method is safe to call multiple times or by non-owners, but only the first
// successful call by the actual owner will release the lock.
func (l *LocalLocker) Unlock(key string) error {
	state, ok := l.locks.Load(key)
	if !ok {
		// Lock does not exist or already released.
		return nil
	}
	ls, _ := state.(*lockState)

	// Atomically set locked state to false. If successful, we are the ones to unlock.
	if ls.locked.CompareAndSwap(true, false) {
		// Cancel the auto-release goroutine if it's still running.
		if ls.cancel != nil {
			ls.cancel()
		}
		ls.mu.Unlock() // Safely unlock the underlying mutex
	}
	// If CompareAndSwap failed, it means another goroutine (e.g., auto-release)
	// already set locked to false, so the lock is already released.
	return nil
}
