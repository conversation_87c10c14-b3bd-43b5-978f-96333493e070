package onlinebookingsetting

import (
	"time"
)

// OnlineBookingSetting 对应数据库表 online_booking_setting
type OnlineBookingSetting struct {
	ID         int64      `gorm:"column:id;primary_key"`
	CompanyID  int64      `gorm:"column:company_id;not null"`
	BusinessID int64      `gorm:"column:business_id;not null"`
	CSS        string     `gorm:"column:css;type:text;not null"`
	JS         string     `gorm:"column:js;type:text;not null"`
	CreatedBy  int64      `gorm:"column:created_by;not null"`
	UpdatedBy  int64      `gorm:"column:updated_by;not null"`
	DeletedBy  *int64     `gorm:"column:deleted_by"`
	CreatedAt  time.Time  `gorm:"column:created_at;not null"`
	UpdatedAt  time.Time  `gorm:"column:updated_at;not null"`
	DeletedAt  *time.Time `gorm:"column:deleted_at"`
}

// TableName 表名
func (OnlineBookingSetting) TableName() string {
	return "online_booking_setting"
}
