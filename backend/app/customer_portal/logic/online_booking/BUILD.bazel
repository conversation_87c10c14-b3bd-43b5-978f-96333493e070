load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "online_booking",
    srcs = [
        "entity.go",
        "logic.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer_portal/logic/online_booking",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer_portal/repo/db/online_booking_setting",
        "//backend/app/customer_portal/utils",
        "//backend/common/rpc/framework/log",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
