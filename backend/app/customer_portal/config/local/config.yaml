server:
  filter:
    - debuglog
    - recovery
  service:
    - name: backend.proto.customer_portal.v1.CustomerPortalService
      ip: 127.0.0.1
      port: 9090
      protocol: grpc
      timeout: 5000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: postgres.moego_customer_portal
      target: dsn://postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_customer_portal?sslmode=disable
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_customer_portal
          max_idle: 10
          max_open: 50
          max_lifetime: 180000