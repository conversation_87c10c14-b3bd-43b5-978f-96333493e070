secrets:
  - name: 'moego/testing/datasource'
    prefix: 'secret.datasource.'
server:
  app: template
  server: template-go
  filter:
    - opentelemetry
    - debuglog
    - recovery
    - validation
  service:
    - name: backend.proto.customer_portal.v1.CustomerPortalService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: postgres.moego_customer_portal
      target: dsn://postgresql://${secret.datasource.postgres.username}:${secret.datasource.postgres.password}@${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_customer_portal?sslmode=disable
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_customer_portal
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
  config:
