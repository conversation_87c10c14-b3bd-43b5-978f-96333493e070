package google

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"net/http"
	"net/url"
	"os"
	"strings"

	"github.com/shenzhencenter/google-ads-pb/services"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/googleapi"
	oauth2api "google.golang.org/api/oauth2/v2"
	"google.golang.org/api/option"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	openplatformpb "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1"
)

type ReadWriter interface {
	ExchangeCode(ctx context.Context, code string) (*oauth2.Token, error)
	GetUserInfo(ctx context.Context, token *oauth2.Token) (*oauth2api.Userinfo, error)
	ListAccessibleCustomers(ctx context.Context, token *oauth2.Token) ([]string, error)
	Search(ctx context.Context, token *oauth2.Token, loginCustomerID string,
		query string) (*services.SearchGoogleAdsResponse, error)
	GetGoogleAdsOAuth2Link(ctx context.Context) (string, error)
}

type impl struct {
	oauth2Config *oauth2.Config
}

var (
	developerToken = "9dP47OpgkouMMCB4IT0WXA"
	googleAuthURL  = "https://accounts.google.com/o/oauth2/v2/auth"

	prodRedirectURL = "https://go.moego.pet/open/platform/google/oauth/callback"
	testRedirectURL = "https://go.t2.moego.dev/open/platform/google/oauth/callback"
)

func New() ReadWriter {
	return &impl{
		oauth2Config: &oauth2.Config{
			ClientID:     "************-ek4rjdgm1u15jvc9uqpmtq4hhdn67i36.apps.googleusercontent.com",
			ClientSecret: "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",
			RedirectURL:  getRedirectURL(),
			// from https://developers.google.com/identity/protocols/oauth2/scopes?hl=zh-cn
			Scopes: []string{
				"https://www.googleapis.com/auth/userinfo.email",
				"https://www.googleapis.com/auth/userinfo.profile",
				"openid",
				"https://www.googleapis.com/auth/adwords",
			},
			Endpoint: google.Endpoint,
		},
	}
}

func getRedirectURL() string {
	if os.Getenv("MOEGO_ENVIRONMENT") == "production" {
		return prodRedirectURL
	}

	return testRedirectURL
}

func (i *impl) ExchangeCode(ctx context.Context, code string) (*oauth2.Token, error) {
	token, err := i.oauth2Config.Exchange(ctx, code)
	if err != nil {
		log.ErrorContextf(ctx, "ExchangeCode err, err:%v", err)

		return nil, err
	}

	return token, nil
}

func (i *impl) GetUserInfo(ctx context.Context, token *oauth2.Token) (*oauth2api.Userinfo, error) {
	svc, err := oauth2api.NewService(ctx, option.WithTokenSource(i.oauth2Config.TokenSource(ctx, token)))
	if err != nil {
		log.ErrorContextf(ctx, "GetUserInfo NewService err, err:%v", err)

		return nil, err
	}

	userInfo, err := svc.Userinfo.Get().Do()
	if err != nil {
		return nil, convGoogleHTTPErr(ctx, err)
	}

	return userInfo, nil
}

func (i *impl) ListAccessibleCustomers(ctx context.Context, token *oauth2.Token) ([]string, error) {
	// new grpc cli
	conn, err := i.newGRPCConn(ctx, token, "")
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	// ListAccessibleCustomers
	res, err := services.NewCustomerServiceClient(conn).ListAccessibleCustomers(ctx,
		&services.ListAccessibleCustomersRequest{})
	if err != nil {
		return nil, convGoogleGRPCErr(ctx, err)
	}

	// parse customer ID
	customerIDs := make([]string, 0, len(res.ResourceNames))
	for _, resourceName := range res.ResourceNames {
		customerIDs = append(customerIDs, strings.TrimPrefix(resourceName, "customers/"))
	}

	return customerIDs, nil
}

func (i *impl) Search(ctx context.Context, token *oauth2.Token, loginCustomerID string,
	query string) (*services.SearchGoogleAdsResponse, error) {
	// new grpc cli
	conn, err := i.newGRPCConn(ctx, token, loginCustomerID)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	// search
	search, err := services.NewGoogleAdsServiceClient(conn).Search(ctx,
		&services.SearchGoogleAdsRequest{
			CustomerId: loginCustomerID,
			Query:      query,
		})
	if err != nil {
		return nil, convGoogleGRPCErr(ctx, err)
	}

	return search, nil
}

// GetGoogleAdsOAuth2Link generates the Google OAuth 2.0 authorization URL.
// It includes necessary parameters like client_id, redirect_uri, scope, and a secure state.
func (i *impl) GetGoogleAdsOAuth2Link(ctx context.Context) (string, error) {
	// Generate a secure state parameter (e.g., 32 bytes -> ~44 char base64)
	state, err := generateSecureRandomString(32)
	if err != nil {
		log.ErrorContext(ctx, "GetGoogleAdsOAuth2Link generateSecureRandomString err, err:%v", err)

		return "", err
	}

	// Build the URL using net/url package
	u, err := url.Parse(googleAuthURL)
	if err != nil {
		log.ErrorContextf(ctx, "GetGoogleAdsOAuth2Link Parse googleAuthURL err, err:%v", err)

		return "", err
	}

	// Add query parameters
	q := u.Query()
	q.Set("response_type", "code")
	q.Set("client_id", i.oauth2Config.ClientID)
	q.Set("redirect_uri", i.oauth2Config.RedirectURL)
	q.Set("scope", strings.Join(i.oauth2Config.Scopes, " ")) // url.Values.Set automatically encodes the value
	q.Set("state", state)
	q.Set("access_type", "offline") // Request a refresh token
	q.Set("prompt", "consent")      // Force consent screen even if already authorized
	u.RawQuery = q.Encode()

	return u.String(), nil
}

// generateSecureRandomString generates a cryptographically secure random string.
// It's suitable for use as an OAuth state parameter to prevent CSRF attacks.
// We generate random bytes and encode them using URL-safe base64.
// A 32-byte random string is common, resulting in a ~44 character base64 string.
func generateSecureRandomString(byteLength int) (string, error) {
	b := make([]byte, byteLength)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	// Use URLEncoding to ensure the string is safe for URL parameters
	return base64.URLEncoding.EncodeToString(b), nil
}

var (
	unauthorizedHTTPCodeMap = map[int]bool{
		http.StatusUnauthorized: true,
		http.StatusForbidden:    true,
	}
)

func convGoogleHTTPErr(ctx context.Context, err error) error {
	// 1. 检查错误是否是 *oauth2.RetrieveError 类型
	// 这是判断令牌失效（invalid_grant）最常见且准确的方式
	var retrieveErr *oauth2.RetrieveError
	if errors.As(err, &retrieveErr) {
		log.ErrorContextf(ctx, "convGoogleHTTPErr retrieveErr err, err:%v", retrieveErr)
		if retrieveErr.Response != nil &&
			unauthorizedHTTPCodeMap[retrieveErr.Response.StatusCode] {
			log.ErrorContextf(ctx, "convGoogleHTTPErr token is expired or revoked.")

			return errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_EXPIRED)
		}
	}

	// 2.也可以检查是否是 *googleapi.Error 类型
	// 虽然对于 invalid_grant 错误，*oauth2.RetrieveError 更常见，
	// 但 googleapi.Error 也能捕获到 HTTP 状态码。
	var apiErr *googleapi.Error
	if errors.As(err, &apiErr) {
		log.ErrorContextf(ctx, "convGoogleHTTPErr apiErr err, err:%v", apiErr)
		if unauthorizedHTTPCodeMap[apiErr.Code] {
			log.ErrorContextf(ctx, "convGoogleHTTPErr token is expired or revoked.")

			return errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_EXPIRED)
		}
	}

	// 其他错误
	log.ErrorContextf(ctx, "convGoogleHTTPErr common err, err:%v", err)

	return err
}

func convGoogleGRPCErr(ctx context.Context, err error) error {
	apiErr := status.Convert(err)
	if apiErr.Code() == codes.Unauthenticated {
		log.ErrorContextf(ctx, "convGoogleGRPCErr token is expired or revoked.")

		return errs.New(openplatformpb.ErrCode_ERR_CODE_OAUTH_EXPIRED)
	}
	log.ErrorContextf(ctx, "convGoogleGRPCErr err, code: %s, message: %s, details: %v",
		apiErr.Code(), apiErr.Message(), apiErr.Details())

	return errs.Newm(apiErr.Code(), apiErr.Message())
}
