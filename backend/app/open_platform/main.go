package main

import (
	"net/http"

	"github.com/gorilla/mux"

	"github.com/MoeGolibrary/moego/backend/app/open_platform/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	mhttp "github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	open_platformpb "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1"
)

func main() {
	s := rpc.NewServer()

	// 注册grpc服务
	grpc.Register(s, &open_platformpb.OpenPlatformService_ServiceDesc, service.NewOpenPlatformService())

	// 注册http服务
	router := mux.NewRouter()
	router.HandleFunc("/open/platform/google/oauth/callback",
		service.NewOAuthHandler().GoogleOAuthCallBackHandler).Methods(http.MethodGet)
	router.HandleFunc("/open/platform/meta/oauth/callback",
		service.NewOAuthHandler().MetaOAuthCallBackHandler).Methods(http.MethodGet)
	mhttp.RegisterNoProtocolServiceMux(s.Service("moego.open.platform"), router)

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
