package constdef

type Header<PERSON>ey string

const (
	HeaderKeyTodoUserID     HeaderKey = "X-Todo-User-Id"
	HeaderKeyAccountID      HeaderKey = "X-Moe-Account-Id"
	HeaderKeySessionID      HeaderKey = "X-Moe-Session-Id"
	HeaderKeySubSessionID   HeaderKey = "X-Moe-Sub-Session-Id"
	HeaderKeySessionData    HeaderKey = "X-Moe-Session-Data"
	HeaderKeySubSessionData HeaderKey = "X-Moe-Sub-Session-Data"
	HeaderKeyImpersonator   HeaderKey = "X-Moe-Impersonator"
)

// platform 子域
const (
	HeaderKeyEnterpriseID HeaderKey = "X-Moe-Enterprise-Id"
	HeaderKeyCompanyID    HeaderKey = "X-Moe-Company-Id"
	HeaderKeyBusinessID   HeaderKey = "X-Moe-Business-Id"
	HeaderKeyStaffID      HeaderKey = "X-Moe-Staff-Id"
	HeaderKeyCustomerID   HeaderKey = "X-Moe-Customer-Id"
	HeaderKeyOBName       HeaderKey = "X-Moe-Ob-Name"
)

// enterprise 子域
const (
	HeaderKeyEnterpriseEnterpriseID HeaderKey = "X-Moe-Enterprise-Enterprise-Id"
	HeaderKeyEnterpriseStaffID      HeaderKey = "X-Moe-Enterprise-Staff-Id"
)

// open platform 子域
const (
	HeaderKeyOpenPlatformRestrictions HeaderKey = "X-MOE-OPEN-PLATFORM-RESTRICTIONS"
)
