load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "open_platform_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/open_platform",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/open_platform/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/log",
        "//backend/proto/open_platform/v1:open_platform",
        "@com_github_gorilla_mux//:mux",
    ],
)

go_binary(
    name = "open_platform",
    embed = [":open_platform_lib"],
    visibility = ["//visibility:public"],
)
