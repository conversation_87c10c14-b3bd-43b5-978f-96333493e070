package oauth

import openplatformpb "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1"

type CreateCompanyTokenDatum struct {
	Code string

	CompanyID  int64
	BusinessID int64
	StaffID    int64
}

type LinkGoogleAdsAccountsDatum struct {
	GoogleAdsAccountIDs []int64

	CompanyID  int64
	BusinessID int64
	StaffID    int64
}

type LinkMetaAdsAccountsDatum struct {
	MetaAdsAccountIDs []int64

	CompanyID  int64
	BusinessID int64
	StaffID    int64
}

type GoogleUserInfoDatum struct {
	UserInfo   *openplatformpb.GoogleOAuthUserInfo
	AdsSetting *openplatformpb.GoogleAdsSetting
}

type MetaUserInfoDatum struct {
	UserInfo   *openplatformpb.MetaOAuthUserInfo
	AdsSetting *openplatformpb.MetaAdsSetting
}
