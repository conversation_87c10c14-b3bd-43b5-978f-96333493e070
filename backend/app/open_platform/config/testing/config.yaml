secrets:
  - name: 'moego/testing/datasource'
    prefix: 'secret.datasource.'
server:
  filter:
    - recovery
    - debuglog
  service:
    - name: backend.proto.open_platform.v1.OpenPlatformService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 5000
    - name: moego.open.platform
      network: tcp
      protocol: http_no_protocol
      timeout: 1000
      ip: 0.0.0.0
      port: 8080
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: postgres.moego_open_platform
      target: dsn://postgresql://${secret.datasource.postgres.moego_open_platform.username}:${secret.datasource.postgres.moego_open_platform.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/moego_open_platform?sslmode=disable
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_open_platform
          max_idle: 10
          max_open: 50
          max_lifetime: 180000