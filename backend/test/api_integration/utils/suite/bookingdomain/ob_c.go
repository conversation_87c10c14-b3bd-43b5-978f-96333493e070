package bookingdomain

import (
	"net/http"

	onlinebookingapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1"
)

func (c *Context) GetOBClientInfo() *onlinebookingapipb.OBClientInfoResponse {
	result := &onlinebookingapipb.OBClientInfoResponse{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.online_booking.v1.OBClientService/GetOBClientInfo").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}
