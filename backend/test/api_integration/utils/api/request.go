package api

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"reflect"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	moegohttp "github.com/MoeGolibrary/go-lib/http"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Request struct {
	host    string
	cookies []*http.Cookie
	method  string
	path    string
	queries map[string]string
	payload []byte
	result  any
	timeout time.Duration
	retry   int
	ctx     context.Context
}

func NewRequest(ctx context.Context, host string, cookies ...*http.Cookie) *Request {
	return &Request{
		ctx:     ctx,
		host:    host,
		cookies: cookies,
	}
}

func (r *Request) SetHost(host string) *Request {
	r.host = host
	return r
}

func (r *Request) SetMethodPath(method string, path string) *Request {
	r.method = method
	r.path = path
	return r
}

func (r *Request) AddQuery(key, value string) *Request {
	if r.queries == nil {
		r.queries = make(map[string]string)
	}
	r.queries[key] = value
	return r
}

func (r *Request) AddQueries(queries map[string]string) *Request {
	if r.queries == nil {
		r.queries = make(map[string]string)
	}
	for key, value := range queries {
		r.queries[key] = value
	}
	return r
}

func (r *Request) SetPayload(payload any) *Request {
	var err error
	if pb, ok := payload.(proto.Message); ok {
		r.payload, err = protojson.Marshal(pb)
		if err != nil {
			log.FatalContext(r.ctx, "protojson fail to marshal payload", err)
		}
	} else {
		r.payload, err = sonic.Marshal(payload)
		if err != nil {
			log.FatalContext(r.ctx, "sonic fail to marshal payload", err)
		}
	}
	return r
}

func (r *Request) SetResult(result any) *Request {
	if reflect.ValueOf(result).Kind() != reflect.Ptr {
		panic("result must be a pointer")
	}
	r.result = result
	return r
}

func (r *Request) SetTimeout(timeout time.Duration) *Request {
	r.timeout = timeout
	return r
}

func (r *Request) SetRetry(retry int) *Request {
	r.retry = retry
	return r
}

func (r *Request) Send() (*Response, error) {
	url := buildURL(r.host, normalizePath(r.path), r.queries)

	req, err := http.NewRequest(r.method, url, bytes.NewBuffer(r.payload))
	if err != nil {
		log.ErrorContext(r.ctx, "fail to build request", err)
		return nil, err
	}

	// set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "moego-api-integration-test")

	// set cookies
	for _, cookie := range r.cookies {
		req.AddCookie(cookie)
	}

	// 发起请求
	req = req.WithContext(r.ctx)
	if r.timeout > 0 {
		ctx, cancel := context.WithTimeout(r.ctx, r.timeout)
		defer cancel()
		req = req.WithContext(ctx)
	}

	resp, err := moegohttp.NewClient(r.host).Do(req.Context(), req)
	if err != nil {
		log.ErrorContext(r.ctx, "send request failed", err)
		if r.retry <= 0 {
			return nil, err
		}

		// retry
		for i := range r.retry {
			log.InfoContextf(r.ctx, "start to retry, attempt: %d", i+1)
			if resp, err = moegohttp.NewClient(r.host).Do(req.Context(), req); err == nil {
				log.InfoContextf(r.ctx, "retry request succeeded, attempt: %d", i+1)
				break
			}
			log.ErrorContextf(r.ctx, "retry request failed, attempt: %d, error: %v", i+1, err)
		}

		if err != nil {
			return nil, err
		}
	}

	response, err := NewResponse(r.ctx, r.result, resp)
	if err != nil {
		log.ErrorContextf(r.ctx, "fail to parse response: %v", err)
		return nil, err
	}

	return response, nil
}

func normalizePath(path string) string {
	// 去除首尾空白字符
	path = strings.TrimSpace(path)
	// 如果路径不以斜杠开头，则在前面加一个斜杠
	if !strings.HasPrefix(path, "/") {
		return "/" + path
	}
	return path
}

func buildURL(host, path string, queries map[string]string) string {
	url := fmt.Sprintf("https://%s%s", host, path)
	if len(queries) > 0 {
		url += "?"
		for key, value := range queries {
			url += key + "=" + value + "&"
		}
		url = url[:len(url)-1]
	}
	return url
}
